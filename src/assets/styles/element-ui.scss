 //to reset element-ui default css
.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// 解决table 因为body部分滚动条 header 错位问题：
.el-table--border th.gutter:last-of-type {
  display: block !important;
  width: 17px !important;
}

//暂时性解决diolag 问题 https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  
  .el-dialog__header{
    background-color: rgb(48, 77, 167); 
    color: #fff;
    .el-dialog__title{
      color: #fff;
    }
    .el-dialog__headerbtn i {
      color: #fff;
    }
  }
}

.el-tooltip__popper{
  max-width: 80%;
}

//element ui upload
.upload-container {
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}
//element ui 带选择列的input
.el-input-group {
  display: inline-table !important;
}
.input-with-select{
  .el-input-group__prepend{
    .el-select{
      width: 105px;
    }
  }
}
//element in-line form 一行两个带图村输入框
.el-form--inline{
  .el-form-item{
    width: 45%;
    margin-right: 25px;
    .el-form-item__label {
      font-size: 12px;
      line-height: 17px;
      padding: 0 0 5px;
    }
    .el-form-item__content{
      .el-input{
        .el-input__inner{
          // padding-left: 40px;
        }
        .el-input__prefix{
          left: 0px;
          background-color: #f5f7fa;
          color: #909399;
          vertical-align: middle;
          display: table-cell;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          width: 40px;
          height: 98%;
          white-space: nowrap;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
      }
    }
  }
}