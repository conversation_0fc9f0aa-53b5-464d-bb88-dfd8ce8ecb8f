.anji-card {
  margin-bottom: 5px;
  box-sizing: border-box;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  position: relative;
  background: #fff;
  border-radius: 2px;
  transition: all 0.3s;

  .card-head {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    min-height: 35px;
    padding: 0 24px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
    background: transparent;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 2px 2px 0 0;
    zoom: 1;

    .main-card-header-button {
      position: absolute;
      right: 20px;
    }
  }

  .card-body {
    padding: 24px;
    zoom: 1;
  }
}

.el-form-item .el-form-item__label {
  line-height: 28px;
}
.el-form-item__content {
  line-height: 28px;
}
.el-input__icon {
  line-height: 28px;
}

.el-input--suffix .el-input__inner {
  height: 28px;
  line-height: 28px;
}
.el-input .el-input__inner {
  height: 28px;
  line-height: 28px;
}
.el-button {
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.el-dropdown-menu__item {
  min-width: 80px;
  max-width: 110px;
  .el-button--text {
    margin-left: 0;
  }
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background: none !important;
}

.el-table td {
  padding: 6px 0;
}
