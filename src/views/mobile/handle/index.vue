<template>
  <section class="wrap">
    <span>处理信息</span>
<!--    <van-cell :value="this.list.status">-->
<!--      &lt;!&ndash; 使用 title 插槽来自定义标题 &ndash;&gt;-->
<!--      <template #title>-->
<!--        <span class="custom-title">处理状态</span>-->
<!--      </template>-->
<!--    </van-cell>-->
    <van-field readonly
               clickable
               is-link
               :value="list.status"
               label="处理状态"
               placeholder="请选择处理状态"
               @click="show = true"/>
    <van-popup v-model="show" position="bottom">
      <van-picker
          show-toolbar
          :columns="listhandleoption"
          @confirm="onSelect"
          @cancel="show = false"/>
    </van-popup>
    <van-field
        v-model="message"
        rows="2"
        autosize
        label="处理意见"
        type="textarea"
        maxlength="100"
        placeholder="请输入留言"
        show-word-limit
    />
    <div class="bottomDiv">
      <van-button @click="goback" class="buttom" plain type="info">取消</van-button>
      <van-button @click="handleSave" class="buttom" type="info">提交</van-button>
    </div>
  </section>
</template>
<script>
import {Notify} from 'vant';
import {warnHandlesave} from "@/api/dealrecord"
import {selectDictList} from "@/api/sysDict";

export default {
  components: {
  },
  data() {
    return {
      show: false,
      message: '',
      list:{},
      listhandleoption: []
    };
  },
  created() {
    document.title = '学生预警名单处理';
    selectDictList({code:"WARNING_HANDLE"}).then(res=>{
      let data= res.data.info
      data.forEach(item=>{
        this.listhandleoption.push(item.value)
      })
    });
    this.list = JSON.parse(this.$route.query.list)
  },
  computed: {

  },
  methods: {
    onSelect(item) {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      this.list.status = item;
      this.show = false;
    },
    goback() {
      this.$router.go(-1)
    },
    handleSave() {
      let param = {
        status: this.list.status,
        infoid: this.list.id,
        operatorcomment: this.message
      }
      warnHandlesave(param).then(res =>{
        if (res.data.code === '00000') {
          Notify({ type: 'success', message: '提交成功' });
          this.$router.go(-1)
        }
      })
    }
  }
  ,
}
</script>

<style scoped>
.wrap{
  height: 100%;
  width: 100%;
  background-color: #f8f8f8 !important;
}
.wrap>span{
  line-height: 45px;
  margin-left: 15px;
  background-color: #f8f8f8 !important;
}
.custom-title {
  margin-right: 4px;
  vertical-align: middle;
}
.bottomDiv{
  height: 50px;
  /*line-height: 50px;*/
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.buttom{
  height: 30px;
  margin-top: 10px;
  width: 35%;
  margin-left: 10%;
}
</style>
