<template>
    <section class="wrap">
        <div class="searchWrap">
            <div class="search" @click="isShowSearch=true">
                <van-icon name="search"/>
                <span>筛选</span>
            </div>
        </div>
        <search :isShow.sync="isShowSearch" v-on:update:query-obj="updateQuery"></search>
        <list :url="url" :queryObj="queryObj" style="margin-top: 40px;">
            <template v-slot:default="slotProps">
                <van-cell-group>
                    <van-cell v-for="(item,index) in slotProps.list" :key="item.id" @click="opennamelist(item)">
                        <!--                        <span style="float:right;font-size: 13px;color: #999;">-->
                        <!--                            {{item.warnTime}}-->
                        <!--                        </span>-->
                        <div class="list-item-name">{{ item.humanName }}-{{ item.humanCode }}</div>
                        <div class="list-item-info">所属机构：{{ item.organizationNames }}</div>
                        <div class="list-item-info">预警类型：{{ item.warnType }}</div>
                        <div class="list-item-info">预警级别：{{ item.warnLevel }} <span
                                :style="{'background-color': item.warnLevelColor}"
                                style="display: inline-block;border-radius:5px;width: 10px;height: 10px;margin-left: 5px"></span>
                        </div>
                        <div class="list-item-info">预警名称：{{ item.warnName }}</div>
                        <div class="list-item-info">预警原因：{{ item.warnReason }}</div>
                        <div class="list-item-info">预警时间：{{ item.warnTime }}</div>
                        <!--                        <span style="float:left;font-size: 13px;color: #999;">-->
                        <!--                            {{item.warnTime}}-->
                        <!--                        </span>-->
                        <span style="float:right;font-size: 13px;color: #999;">
                            {{ index + 1 }}
                        </span>
                    </van-cell>
                </van-cell-group>
            </template>
        </list>
    </section>
</template>
<script>

    import List from "@/views/mobile/components/List";
    import Search from "@/views/mobile/components/Search";
    import {switchRole} from "@/api/user";
    import {setStore} from "@/util/store";
    import {queryPage,} from "@/api/mobile";

    export default {
        components: {
            List, Search
        },
        data() {
            return {
                isShowSearch: false,
                obj: {},
                url: queryPage,
            };
        },
        created() {
            document.title = '学生预警名单列表';
        },
        updated() {
            console.log(111)
            console.log(this.$refs.cell)
        },
        computed: {
            queryObj() {
                // let switchRole = getStore({name: 'switch-role'});
                // if (switchRole && switchRole === this.$route.params.roleId) {
                //   console.log(`switchRole`, switchRole);
                //   this.$message({type: "success", message: "switchRole:"+switchRole});
                // } else {
                console.log(`changeRoleHandle`, (this.$route.params.roleId.indexOf('Object') !== -1 ? switchRole : this.$route.params.roleId));
                // this.$message({type: "success", message: "changeRoleHandle:"+(this.$route.params.roleId.indexOf('Object') !== -1 ? switchRole : this.$route.params.roleId)});
                this.changeRoleHandle(this.$route.params.roleId.indexOf('Object') !== -1 ? switchRole : this.$route.params.roleId);
                // }
                return Object.assign({taskRecordId: this.$route.params.taskRecordId}, this.obj);
            },
        },
        methods: {
            updateQuery(e) {
                console.log(`updateQuery`, e)
                this.obj = '';
                this.obj = e;
            },
            opennamelist(list) {
                this.$router.push({name: '移动名单详情', params: {list: list}})
            },
            changeRoleHandle(roleId) {
                switchRole({role: roleId}).then((res) => {
                    if (res.data && res.data.code === "00000") {
                        setStore({name: "switch-role", content: roleId});
                    }
                });
            },
        }
        ,

    }
</script>

<style scoped>
    .list-item-name {
        font-size: 16px;
        font-weight: bold;
    }

    .searchWrap {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        height: 45px;
        line-height: 45px;
        padding: 0px 10px;
        background-color: #f8f8f8;
        z-index: 999 !important;
    }

    .search {
        width: 100%;
        height: 30px;
        padding-left: 10px;
        line-height: 30px;
        margin-top: 7.5px;
        background: #fff;
        border-radius: 3px;
        font-size: 12px;
        color: #666;
        text-align: center;
        box-sizing: border-box;
    }

</style>
