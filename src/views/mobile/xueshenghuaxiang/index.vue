<template>
    <section class="wrap">
        <div class="searchWrap">
            <div class="search" @click="isShowSearch=true">
                <van-icon name="search"/>
                <span>筛选</span>
            </div>
            <van-icon name="manager-o" class="new-tx" @click="isShow = true"/>
        </div>

        <search :isShow.sync="isShowSearch" v-on:update:query-obj="updateQuery"></search>
        <list :url="url" :queryObj="queryObj" style="margin-top: 40px;">
            <template v-slot:default="slotProps">
                <van-cell-group>
                    <van-cell v-for="item in slotProps.list" :key="item.id" @click="opennamelist(item)">
                        <div class="list-item-name">{{ item.humanname }}-{{ item.humancode }}
                            {{item.sex==='female'?'女':'男'}}
                        </div>
                        <div class="list-item-info">所属机构：{{ item.organizationnames }}</div>
                        <div class="list-item-info">手机号码：{{ item.telmobile1 }}</div>
                        <div class="list-item-info">宿舍信息：{{ item.dormitoryInfo }} <span
                                :style="{'background-color': item.warnLevelColor}"
                                style="display: inline-block;border-radius:5px;width: 10px;height: 10px;margin-left: 5px"></span>
                        </div>
                    </van-cell>
                </van-cell-group>
            </template>
        </list>
        <!-- 个人中心 -->
        <div class="container" v-show="isShow">
            <div class="option">
                <div>选择角色</div>
                <div class="option-item"
                     :class="{'is-active': curRoleName == item}"
                     v-for="item in roleLists"
                     :key="item"
                     @click="clickHandle(item)">{{item}}
                </div>
            </div>
        </div>
    </section>
</template>
<script>

    import List from "@/views/mobile/components/List";
    import Search from "@/views/mobile/components/HuaxiangSearch";
    import {sytPermissionAccountQueryPage, GetUser, GetUserSwitchRole} from "@/api/mobile";
    import {GetSysParamNoLogin} from "@/api/sysParam";

    export default {
        components: {
            List, Search
        },
        data() {
            return {
                isShow: false,
                isShowSearch: false,
                obj: {},
                url: sytPermissionAccountQueryPage,
                roleLists: [],
                curRoleName: null,
            };
        },
        created() {
            document.title = '学生画像';
            this.getUser()
        },
        updated() {
        },
        computed: {
            queryObj() {
                return Object.assign({employeeType: "student"}, this.obj);
            },
        },
        methods: {
            clickHandle(role) {
                this.getUserSwitchRole(role)
                this.isShow = false;
            },
            updateQuery(e) {
                this.obj = '';
                this.obj = e;
            },
            getUserSwitchRole(role) {
                GetUserSwitchRole({role: role}).then(res => {
                    if (res.data.info === 'success') {
                        this.obj = {switchRole: role}
                        this.getUser()
                    }
                })
            },
            getUser() {
                GetUser({}).then(res => {
                    let resData = res.data.info.roleList
                    this.roleLists = resData;
                    if (res.data.info.roles.length > 0) this.curRoleName = res.data.info.roles[0].rolename
                })
            },
            opennamelist(list) {
                GetSysParamNoLogin({idOrNameOrType: "reportId"}).then(res => {
                    let param = {
                        id: res.data.info.value,
                        humancode: list.humancode
                    }
                    this.$router.push({
                        path: '/mobiles/portrait',
                        query: {param: JSON.stringify(param)}
                    })
                })
            },
        }
    }
</script>

<style scoped>
    .list-item-name {
        font-size: 16px;
        font-weight: bold;
    }

    .searchWrap {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        height: 45px;
        line-height: 45px;
        padding: 0px 10px;
        background-color: #f8f8f8;
        z-index: 999 !important;
    }

    .search {
        width: 88%;
        height: 30px;
        padding-left: 10px;
        line-height: 30px;
        margin-top: 7.5px;
        background: #fff;
        border-radius: 3px;
        font-size: 12px;
        color: #666;
        text-align: center;
        box-sizing: border-box;
    }

    .new-tx {
        position: absolute;
        right: 20px;
        top: 10px;
        height: 30px;
    }

    .wrap .container {
        position: absolute;
        top: 40px;
        right: 6px;
        width: 100px;
        /* height: 300px; */
        background-color: #fff;
        border-radius: 6px;
        z-index: 999;
    }

    .wrap .container::before {
        position: absolute;
        top: -6px;
        right: 17.5px;
        content: "";
        width: 12px;
        height: 12px;
        background-color: #fff;
        transform: rotate(45deg);
    }

    .container {
        font-size: 14px;
        color: #888;
    }

    .container .title {
        height: 36px;
        line-height: 30px;
        display: flex;
        border-bottom: 1px solid #eee;
    }

    .container .title .con-tx {
        width: 30px;
        height: 30px;
        margin: 3px;
    }

    .container .title .text {
        font-size: 14px;
        color: #888;
        height: 36px;
        line-height: 36px;
        text-align: center;
    }

    .container .option {
        /* margin-left: 10px; */
        margin-top: 2px;
        margin-bottom: 10px;
    }

    .container .option .option-item {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .container .option > div {
        padding: 4px;
        padding-left: 6px;
    }

    .container .option > div:first-child {
        border-bottom: 1px solid #eee;
    }

    .is-active {
        background-color: #eee;
    }
</style>
