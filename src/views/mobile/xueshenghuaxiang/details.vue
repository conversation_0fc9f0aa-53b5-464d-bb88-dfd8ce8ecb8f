<template>
  <section v-show="Show" :style="{
    width: viewerwidth
  }">
<!--    <div class="wrapAlls">-->
<!--      <div class="boxs">-->
<!--        <div class="content">-->
<!--          <viewer class="viewer" :report-id="this.reportId" :humancode="this.humancode"></viewer>-->
<!--        </div>-->
<!--        <footer class="foot">-->
<!--          <span>-->
<!--            {{ dbxx }}-->
<!--          </span>-->
<!--        </footer>-->
<!--      </div>-->
<!--    </div>-->
    <viewer class="viewer" :report-id="this.reportId" :humancode="this.humancode" @over="isover"></viewer>
  </section>
</template>

<script>
import { GetSysParamNoLogin } from "@/api/sysParam";
import viewer from "@/views/report/bigscreen/viewer/layoutViewer.vue";
export default {
  name: "GitSanythReportUiDataWarning",
  components: {
    viewer
  },
  data() {
    return {
      dbxx: ``,
      reportId: '',
      humancode: '',
      show: false,
      Show: true,

      viewerwidth: '1980px'
    };
  },
  computed: {

  },
  created() {
    this.getcharts()
  },
  mounted() {
    this.footP(this.footParam());
    console.log(`sessionStorage`,JSON.parse(this.$route.query.param))
    // if( this.$route.query !== undefined ) {
    //   console.log(`sessionStorage`,this.$route.params.param)
    //   sessionStorage.setItem("param",JSON.stringify(this.$route.params.param))
    // }
    // this.getcharts()
  },

  methods: {
    isover({bool}){
      console.log(`1111`,bool)
      if(bool){
          let width = document.body.clientWidth;
          this.viewerwidth = width + 'px'
          this.Show = true
      }
    },
    getcharts() {
      document.title = '学生画像';
      let param = JSON.parse(this.$route.query.param)
      this.reportId = param.id;
      this.humancode = param.humancode;
      // let width = document.body.clientWidth;
      // this.viewerwidth = '1980px'
      // this.show = true
      // setTimeout(()=>{
      //   this.viewerwidth = width + 'px'
      //   this.Show = true
      // },2000)
    },
    // getcharts() {
    //   document.title = '学生画像';
    //   let param = JSON.parse(this.$route.query.param)
    //   this.reportId = param.id;
    //   this.humancode = param.humancode;
    //   let width = document.body.clientWidth;
    //   this.viewerwidth = '1980px'
    //   this.show = true
    //   setTimeout(()=>{
    //     this.viewerwidth = width + 'px'
    //     this.Show = true
    //   },2000)
    // },
    footP(param) {
      GetSysParamNoLogin(param).then((res) => {
        this.dbxx = res.data.info.value;
      });
    },
    footParam() {
      return {
        idOrNameOrType: "dbxx",
      };
    },
  },
};
</script>
<style scoped>
@import url(../../../styles/assets/css/reset.css);
@import url(../../../styles/assets/css/head.css);
@import url(../../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
.viewer{
  width: 100%;
   height: 100%;

  // height: 800px;
}
.wrapAlls {
  // height: calc(100% - 60px);
  //   bottom: -60px;
  // height: 100vh;
  overflow: hidden;
}
.boxs {
  overflow-y: scroll;
}
.foot {
  background: rgb(240, 242, 245);
  font-size: 12px;
  line-height: 30px;
  color: rgb(147, 150, 149);
  text-align: center;
}
.content {
  width: 100%;
  // height: 100%;
  //min-height: calc(100vh - 90px);
  //padding: 15px;
  background: rgb(236, 235, 235);
}
</style>
