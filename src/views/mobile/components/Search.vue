<template>
    <div>
        <van-popup :value="isShow" v-on:input="onChangeShow" position="bottom" :style="{width:'101%', height: '50%' }">
            <div class="base-info">
                <van-field v-model="queryParam.humanCode" label="账号" placeholder="请输入查询账号"/>
                <van-field v-model="queryParam.humanName" label="姓名" placeholder="请输入查询姓名"/>
                <van-field v-model="queryParam.warnLevel" label="预警等级" placeholder="请输入查询等级"/>
<!--                <van-field is-link label="预警类型" @click="show = true" v-model="queryParam.warnType" />-->
<!--                <van-action-sheet v-model="show" :actions="actions" @select="onSelect" />-->
              <!-- 状态 -->
                                    <van-field readonly
                                               clickable
                                               is-link
                                               :value="queryParam.warnType"
                                               label="预警类型"
                                               placeholder="请选择预警类型"
                                               @click="show = true"/>
                                    <van-popup v-model="show" position="bottom">
                                        <van-picker
                                                show-toolbar
                                                :columns="list"
                                                @confirm="onSelect"
                                                @cancel="show = false"/>
                                    </van-popup>
            </div>
            <div class="van-submit-bar">
                <van-button type="info" @click="onSearch">查询</van-button>
                <van-button type="info" @click="onClear"
                            style="margin-right: 10px;color: #00a5ec;border: 1px solid #00a5ec;background: #fff;">清空
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
import {mapState} from "vuex";
import {selectDictList} from "@/api/sysDict";

export default {
        name: "COMMONSEARCH",
        props: {
            isShow: Boolean
        },
        data() {
            return {
                queryParam: {},
              show: false,
              list: [],
            }
        },
        computed: {
            ...mapState(["roleKey"])
        },
        created() {
          selectDictList({code: "WARNING_TYPE"}).then(res => {
            let data = res.data.info
            data.forEach(item => {
              this.list.push(item.value)
            })
          });
        },
        methods: {
          onSelect(item) {
            // 默认情况下点击选项时不会自动收起
            // 可以通过 close-on-click-action 属性开启自动收起
            this.queryParam.warnType = item;
            this.show = false;
          },
            onChangeShow(e) {
                this.$emit("update:isShow", e);
            },
            onSearch() {
                this.onChangeShow(false);
                this.$emit("update:query-obj", this.queryParam);
            },
            onClear() {
                this.onChangeShow(false);
                this.queryParam = {};
                this.$emit("update:query-obj", {});
            },
        }
    }
</script>

<style scoped>
    .wrapper {
        margin-top: 10px;
    }

    .base-info {
        margin-bottom: 10px;
    }

    .van-submit-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        background-color: #fff;
    }

    .van-button--normal {
        height: 35px;
        margin-top: 7.5px;
        line-height: 35px;
        width: 94%;
        margin-left: 3%;
        background: #00a5ec;
        font-size: 16px;
        border: none;
    }
</style>
