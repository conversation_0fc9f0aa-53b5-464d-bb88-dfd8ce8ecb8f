<template>
  <div>
    <van-popup :value="isShow" v-on:input="onChangeShow" position="bottom" :style="{width:'101%', height: '50%' }">
      <div class="base-info">
        <van-field v-model="queryParam.humancode" label="学号" placeholder="请输入查询学号"/>
        <van-field v-model="queryParam.humanname" label="姓名" placeholder="请输入查询姓名"/>
        <van-field readonly
                   clickable
                   is-link
                   :value="sex"
                   label="性别"
                   placeholder="请选择性别"
                   @click="show = true"/>
        <van-popup v-model="show" position="bottom">
          <van-picker
              show-toolbar
              :columns="['男','女']"
              @confirm="onSelectsex"
              @cancel="show = false"/>
        </van-popup>
        <van-field readonly
                   clickable
                   is-link
                   :value="organizationnames"
                   label="组织机构"
                   placeholder="请选择组织机构"
                   @click="ordshow = true"/>
        <van-popup v-model="ordshow" position="bottom">
          <van-cascader title="请选择组织机构" :options="columns" v-model="queryParam.organizationnames"
                      :field-names="fieldNames" @change="onSelectorganizationnames"
                      @close="ordshow = false" />
          <van-button @click="ordshow = false" type="info">确认</van-button>
        </van-popup>
        <van-field v-model="queryParam.telmobile1" label="手机号码" placeholder="请输入查询手机号码"/>
        <van-field v-model="queryParam.dormitoryInfo" label="宿舍信息" placeholder="请输入查询宿舍信息"/>
      </div>
      <div class="van-submit-bar">
        <van-button type="info" @click="onSearch">查询</van-button>
        <van-button type="info" @click="onClear"
                    style="margin-right: 10px;color: #00a5ec;border: 1px solid #00a5ec;background: #fff;">清空
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {mapState} from "vuex";
import {selectDictList} from "@/api/sysDict";
import {GetOrgJsonArray} from "@/api/basetable";

export default {
  name: "COMMONSEARCH",
  props: {
    isShow: Boolean
  },
  data() {
    return {
      queryParam: {},
      show: false,
      ordshow: false,
      list: [],
      sex: '',
      organizationnames: '',
      fieldNames:{
        text: 'label',
        value: 'id',
        children: 'children',
      },
      columns: [

      ],
    }
  },
  computed: {
    ...mapState(["roleKey"])
  },
  created() {
    selectDictList({code: "WARNING_TYPE"}).then(res => {
      let data = res.data.info
      data.forEach(item => {
        this.list.push(item.value)
      })
    });
    GetOrgJsonArray({}).then(res => {
      console.log(`GetOrgJsonArray`,res.data)
      let data = res.data
      data.forEach(item=>{
        if( item.children.length > 0){
          this.forEachchild(item.children)
        }
      })
      this.columns = data
    });
  },
  methods: {
    forEachchild(children){
      children.forEach(child=>{
        if(child.children.length > 0){
          this.forEachchild(child.children)
        }else {
          delete child.children
        }
      })
    },
    onSelectsex(item) {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      this.sex = item
      if(item == '男'){
        this.queryParam.sex = 'male';
      }
      if(item == '女'){
        this.queryParam.sex = 'female';
      }
      this.show = false;
    },
    onSelectorganizationnames({selectedOptions}) {
      console.log(selectedOptions)
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      // let currentData = selectedOptions.map((option) => option.id).join('/');
      let name = selectedOptions.map((option) => option.orgname).join('/');

      // // state.valueShow = currentData
      // // this.organizationnames = item.text
      // console.log(selectedOptions)
      this.organizationnames = name
      // this.queryParam.organizationnames = currentData;
      // this.ordshow = false;
    },
    onChangeShow(e) {
      this.$emit("update:isShow", e);
    },
    onSearch() {
      this.onChangeShow(false);
      this.$emit("update:query-obj", this.queryParam);
    },
    onClear() {
      this.onChangeShow(false);
      this.queryParam = {};
      this.organizationnames = ''
      this.sex = ''
      this.$emit("update:query-obj", {});
    },
  }
}
</script>

<style scoped>
.wrapper {
  margin-top: 10px;
}

.base-info {
  margin-bottom: 10px;
}

.van-submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 50px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 3px -1px #eee;
  background-color: #fff;
}

.van-button--normal {
  height: 35px;
  margin-top: 7.5px;
  line-height: 35px;
  width: 94%;
  margin-left: 3%;
  background: #00a5ec;
  font-size: 16px;
  border: none;
}
</style>
