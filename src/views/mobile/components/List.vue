<template>
    <van-list v-model="isLoading"
              :finished="finished"
              finished-text="没有更多了"
              @load="loadMore"
              :error.sync="error"
              error-text="请求失败，点击重新加载"
              :immediate-check="firstLoad">
        <slot v-bind:list="list2"></slot>
    </van-list>
</template>
<script>
    // import {queryPage,} from "@/api/mobile";

    export default {
        name: "BASE_LIST",
        props: {
            url: String,
            queryObj: {
                type: Object,
                default() {
                    return {};
                }
            },
            queryJson: String,
            list: {
                type: Array,
                default() {
                    return [];
                }
            }
        },
        data() {
            return {
                pageIndex: 0,
                pageSize: 20,
                list2: [],
                isLoading: false,
                finished: false,
                error: null
            };
        },
        watch: {
            list(newV) {
                if (newV !== this.list2) {
                    this.list2 = newV;
                    this.count = null;
                }
            },
            queryObj() {
                console.log('list222222')
                this.onReload();
            },
            queryJson() {
                this.onReload();
            }
        },
        computed: {
            firstLoad() {
                return true;
            }
        },
        methods: {
            onReload() {
                this.finished = true;
                this.pageIndex = 0;
                this.pageSize = 20;
                this.list2 = [];
                this.$emit("update:list", this.list2);
                this.finished = false;
                this.isLoading = true;
                this.loadMore();
            },
            loadMore() {
                if (this.isLoading) {
                    this.pageIndex++;
                    let newObj = {
                        page: this.pageIndex,
                        pageSize: this.pageSize,
                        queryParam: this.queryObj
                    }
                    this.url(newObj).then(response => {
                        this.$emit("allData", response.data);
                        if (response.data.info) {
                            let pageData = response.data.info;
                            if (
                                pageData.hasOwnProperty("total") &&
                                pageData.hasOwnProperty("current")
                            ) {
                                this.list2 = this.list2.concat(pageData.records);
                                this.$emit("update:list", this.list2);
                                this.$emit("update:pageData", pageData);
                                this.finished = pageData.total <= pageData.current;
                            } else {
                                this.error = true;
                            }
                        }
                    }).catch(error => {
                        console.error(error);
                        this.error = true;
                    }).finally(() => (this.isLoading = false));
                }
            }
        }
    };
</script>
<style scoped>
</style>
