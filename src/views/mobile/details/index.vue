<template>
  <section class="wrap">
    <div class="Topwrap" @click="portrait">
      <i class="el-icon-arrow-right"></i>
      <div class="Topwrapleft">
        <img :style="{
                      width: '100%',
                      height: '100%',
                  }" :src="this.avatarUrl" alt="">
      </div>
      <div class="Topwrapright">
        <p :style="{lineHeight: '40px','margin': '0',fontSize:'20px',fontWeight:'bold', 'white-space': 'nowrap'}">{{list.humanName}} - {{list.humanCode}}</p>
<!--        <p :style="{lineHeight: '20px',margin: '0',color: '#bbb'}">{{list.sex}}  |  {{list.organizationNames}}  |  {{list.humanCode}}</p>-->
        <p :style="{lineHeight: '20px',margin: '0',color: 'rgba(255,255,255,0.82)', 'white-space': 'nowrap'}">{{list.organizationNames}}</p>
        <p :style="{lineHeight: '20px','margin-top': '3px',color: 'rgba(255,255,255,0.82)', 'white-space': 'nowrap'}"><span @click.stop="callPhone(list.telmobile1)"><i class="el-icon-phone"></i>{{list.telmobile1}}</span></p>
      </div>
    </div>
    <van-tabs color="#409EFF" line-width="60px" :style="{ marginTop: '10px' }" v-model="active">
      <van-tab title="预警内容" :style="{ backgroundColor:'#fff',padding:'10px',color: '#bbb'}">
        <div class="list-item-info">预警时间：{{list.warnTime}}</div>
        <div class="list-item-info">预警级别：{{list.warnLevel}}</div>
        <div class="list-item-info">预警名称：{{list.warnName}}</div>
        <div class="list-item-info">预警类型：{{list.warnType}}</div>
        <div class="list-item-info">预警原因：{{list.warnReason}}</div>
        <div class="list-item-info">处理状态：<span :style="{
          color: list.status == '已处理' ? '#2d8cf0' : '#ef9b2d'
        }">{{list.status}}</span></div>
      </van-tab>
      <van-tab title="历史预警">
        <list :queryObj="queryObj">
          <template v-slot:default="slotProps">
            <van-cell-group>
              <van-cell v-for="item in slotProps.list" :key="item.id" @click="opennamelist(item)" >
                        <span style="float:right;font-size: 13px;color: #bbb;">
                            {{item.warnTime}}
                        </span>
                <div class="list-item-info">预警类型：{{ item.warnType }}</div>
                <div class="list-item-info">预警级别：{{ item.warnLevel }}</div>
                <div class="list-item-info">预警名称：{{ item.warnName }}</div>
                <div class="list-item-info">预警原因：{{ item.warnReason }}</div>
                <div class="list-item-info">预警状态：<span :style="{
          color: list.status == '已处理' ? '#2d8cf0' : '#ef9b2d'
        }">{{list.status}}</span></div>

              </van-cell>
            </van-cell-group>
          </template>
        </list>
      </van-tab>
    </van-tabs>
    <div v-if="list.status !== '已处理' && active == 0 " class="bottomDiv">
      <van-button  @click="handleClick" class="buttom" type="info">处理预警</van-button>
    </div>
  </section>
</template>
<script>
import List from "@/views/mobile/details/List";
import {warnInfoget} from "@/api/dataWarningWarnInfo";
import {GetSysParamNoLogin} from "@/api/sysParam";

export default {
  components: {
    List
  },
  data() {
    return {
      active: 0,
      queryObj: {},
      list: {},
      avatarUrl: '',
    };
  },
  created() {
    document.title = '学生预警名单详情';
    if( this.$route.params.list !== undefined && typeof this.$route.params.list == "object"  ) {
      sessionStorage.setItem("list",JSON.stringify(this.$route.params.list))
    }
    this.getlist();
  },
  computed: {

  },
  methods: {
    getlist(){
      let id
      id = JSON.parse(sessionStorage.getItem("list")).id
      warnInfoget({"id":id}).then(res=>{
        this.list = res.data.info;
        if(this.list.sex){
          if(this.list.sex == "male"){
            this.list.sex = "男"
          }
          if(this.list.sex == "female"){
            this.list.sex = "女"
          }
        }
        this.queryObj = {
          humanCode: this.list.humanCode
        }
        // GetPhoto(this.list.humanCode).then(res=>{
        //   if (res.data.code === "00000") {
            this.avatarUrl = '/file/view/' + this.list.humanCode;
          // }
        // })
      })
      // this.list = JSON.parse(sessionStorage.getItem("list"));
      // console.log(`list`,this.list)
      // this.queryObj = {
      //   humanCode: this.list.humanCode
      // }
    },
    handleClick() {
      let param = JSON.stringify(this.list)
      this.$router.push({path: '/mobile/handle/index',query: { list: param}})
    },
    callPhone(phoneNumber) {
      window.location.href = 'tel:' + phoneNumber
    },
    portrait(){
      GetSysParamNoLogin({idOrNameOrType: "reportId"}).then(res=>{
        let param = {
          id: res.data.info.value,
          humancode: this.list.humanCode
        }
        this.$router.push({
          path: '/mobiles/portrait',
          query:{param: JSON.stringify(param)}
        })
      })


      // let param = {1502200583942086657
      //   id: this.$route.params.reportId,
      //   humancode: this.list.humancode
      // }
      // let { href } = this.$router.push({
      //   name: '个人画像',
      //   params:{param:param}
      // })
      // window.open(href, '_blank')
    }
  }
  ,
}
</script>

<style scoped>
.wrap{
  height: 100vh !important;
  width: 100%;
  background-color: #f8f8f8 !important;
  position: relative;
}
.Topwrap{
  width: 100%;
  min-height: 130px;
  background-color: #3875C6;
  padding: 10px;
  position: relative;
}
.Topwrap>i{
  color: #fff;
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 58px;
}
.list-item-info{
  line-height: 24px;
  color: #bbb
}
.bottomDiv{
  height: 50px;
  /*line-height: 50px;*/
  background-color: #fff;
  position: absolute;
  bottom: 0 !important;
  left: 0;
  right: 0;
}
.buttom{
  height: 30px;
  margin-top: 10px;
  width: 60%;
  margin-left: 20%;
}
.Topwrapleft{
  float: left;
  width: 60px;
  height: 80px;
  margin-top: 15px;
  /*border: 1px solid red;*/
}
.Topwrapright{
  float: left;
  margin-left: 15px;
  margin-top: 10px;
  color: #fff;
  width: calc(100% - 100px);
  overflow: hidden;
}
.statusss{
  color: #ef9b2d;
}
</style>
