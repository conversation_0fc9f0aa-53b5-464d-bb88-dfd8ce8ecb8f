<template>
  <basic-container>
    <h3 class="pageTitle">素材管理</h3>
    <avue-form :option="option" v-model="form"> </avue-form>
  </basic-container>
</template>

<script>
export default {
  data() {
    return {
      type: "info",
      option: {
        submitText: "提交",
        size: "small",
        labelWidth: 150,
        column: [
          {
            label: "站点logo",
            type: "upload",
            listType: "picture-img",
            propsHttp: {
              res: "data.0",
            },
            canvasOption: {
              text: "avue",
              ratio: 0.1,
            },
            tip: "只能上传jpg/png图片，图片尺寸高度为70像素",
            span: 24,
            prop: "logo",
          },
          {
            label: "登录页背景元素图",
            type: "upload",
            listType: "picture-img",
            propsHttp: {
              res: "data.0",
            },
            canvasOption: {
              text: "avue",
              ratio: 0.1,
            },
            tip: "只能上传png透明图片，图片尺寸为1114*775像素",
            span: 24,
            prop: "img",
          },
          {
            label: "登录页背景底图",
            type: "upload",
            listType: "picture-img",
            propsHttp: {
              res: "data.0",
            },
            canvasOption: {
              text: "avue",
              ratio: 0.1,
            },
            tip: "只能上传jpg/png图片，图片尺寸为1920*900像素",
            span: 24,
            prop: "img",
          },
          {
            label: "版权信息",
            prop: "copyright",
          },
        ],
      },
      form: [],
    };
  },
  methods: {
    handleSubmit() {
      this.$message({
        message: this.form,
        type: "success",
      });
    },
  },
};
</script>

<style>
</style>