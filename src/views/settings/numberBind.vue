<template>
    <basic-container>
        <h3>账号绑定</h3>
        <el-alert style="margin:10px 0;" title="系统已集成多家第三方平台登录认证，在使用第三方帐号登录时，可在这里进行帐号的绑定/解绑操作。" type="info"
                  show-icon></el-alert>
        <div class="user-setting__main" v-if="initElRows">
            <el-row v-for="row in initElRows" :key="row">
                <el-col :span="8">
                    <div class="user-setting__meta">
                        <p class="title">
                            <img :src="row.icon" alt="">
                            {{row.title}}
                        </p>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="user-setting__meta">
                        <p class="subtitle">{{row.sfBind==='是'?'已绑定':'未绑定'}}</p>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="user-setting__menu">
                        <el-button v-if="row.sfBind==='是'" plain type="warning" size="mini"
                                   @click="bindUserDelete(row)">解绑
                        </el-button>
                        <!-- <el-button v-else plain type="primary" size="mini" @click="bindUserType(row)">绑定</el-button> -->
                        <el-button v-else plain type="primary" size="mini" @click="bindUserType()">绑定</el-button>
                    </div>
                </el-col>
            </el-row>
        </div>
        <el-dialog modal-append-to-body
                   append-to-body
                   :title="showTitle+'绑定'"
                   top="50px"
                   :visible.sync="showDialog"
                   width="60%">
            <iframe :src="thirdUrl" frameborder="0" width="100%" height="600px"></iframe>

            <div slot="footer"
                 class="screenshot__menu">
                <el-button type="primary"
                           icon="el-icon-check"
                           @click="showDialog=false">取消
                </el-button>
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
    import {getCurrentBind, bindUserType, bindUserDelete} from "@/api/numberBind";

    export default {
        data() {
            return {
                setInitElRows: [
                    {
                        title: '企业微信',
                        authType: 'WECHAT_ENTERPRISE',
                        icon: require("../../styles/image/qywx.png"),
                        sfBind: '否',
                    },
                    // {
                    //     title: '微信',
                    //     authType: 'WECHAT',
                    //     icon: require("../../styles/image/weixin.png"),
                    //     sfBind: '否',
                    // },
                    {
                        title: '钉钉',
                        authType: 'DINGTALK',
                        icon: require("../../styles/image/ding.png"),
                        sfBind: '否',
                    },
                    // {
                    //     title: '腾讯QQ',
                    //     authType:'QQ',
                    //     icon:require("../../styles/image/qq.png"),
                    // sfBind:'否',
                    // },
                    // {
                    //     title: '易班',
                    //     authType:'',
                    //     icon:require("../../styles/image/yiban.png"),
                    // sfBind:'否',
                    // },
                    // {
                    //     title: '新浪微博',
                    //     authType:'WEIBO',
                    //     icon:require("../../styles/image/weibo.png"),
                    // sfBind:'否',
                    // },
                ],
                initElRows: [],
                showDialog: false,
                showTitle: '',
                thirdUrl: '',
            }
        },
        created() {
            this.getCurrentBind()
        },
        methods: {
            getCurrentBind() {
                getCurrentBind().then(res => {
                    this.initElRows = [];
                    if (res.data.code === '00000') {
                        this.setInitElRows.forEach(e => {
                            res.data.info.forEach(row => {
                                if (e.authType === row.authType) {
                                    e.sfBind = row.sfBind
                                    e.id = row.id
                                    e.userId = row.userId
                                }
                            })
                        })
                    }
                    this.initElRows = this.setInitElRows;
                })
            },
            // bindUserType(row) {
            //     this.showDialog = true;
            //     this.showTitle = row.title;
            //     bindUserType({"oauthType": row.authType.toLowerCase()}).then((res) => {
            //         if (res.data.code === '00000') {
            //             console.log(res.data.info)
            //             this.thirdUrl = res.data.info;
            //         } else {
            //             this.$message.error(res.data.info);
            //         }
            //     })
            // },
            bindUserType(){
                    this.$confirm('请返回登录页，在其他登录方式中选择对应的第三方应用图标链接进行绑定操作。', '绑定提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                    }).then(() => {
                        window.location.href = "/logout"
                    }).catch(() => {
                           
                    });
            },
            bindUserDelete(row) {
                bindUserDelete({"id": row.id}).then(res => {
                    if (res.data.code === '00000') {
                        this.$message({type: "success", message: "操作成功"});
                        this.getCurrentBind()
                    } else {
                        this.$message.error(res.data.info);
                    }
                })
            },
        }

    };
</script>

<style lang="scss" scoped>
    h3 {
        margin: 0;
        font-size: 16px;
        margin-bottom: 0px;
    }

    .el-alert--info.is-light{
        background: #ECF5FF;
        color: #7FBEFF;
    }

    .title img {
        width: 30px;
        vertical-align: middle;
        margin-right: 5px;
    }

    .user-setting__menu {
        text-align: right;
    }

    .el-row {
        border-bottom: 1px solid #f1f1f1;
        margin-bottom: 10px;
    }

    .user-setting {
        &__main {
            // padding: 8px 40px;
            padding: 8px 0px;
            padding-left: 0;
        }

        &__item {
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            // display: -webkit-box;
            // display: -ms-flexbox;
            // display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        &__title {
            font-size: 20px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 28px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        &__meta {
            // flex: 1;
            line-height: 40px;

            .title {
                // margin: 6px 0;
                margin: 0;
                font-weight: 700;
                font-size: 14px;
            }

            .subtitle {
                // margin: 8px 0;
                margin: 0;
                font-size: 14px;
                color: #888;
                text-align: center;
            }
        }
    }
</style>
