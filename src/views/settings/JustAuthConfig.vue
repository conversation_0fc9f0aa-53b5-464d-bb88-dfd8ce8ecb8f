<template>
    <div>
        <basic-container>
            <h3 class="pageTitle">第三方登录参数</h3>
            <avue-crud ref="crud"
                       v-model="form"
                       :before-open="beforeOpen"
                       :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="pageList"
                       :page.sync="page"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-save="saveHandle"
                       @row-update="editHandle"
                       @row-del="deleteHandle"
                       @search-change="searchChange">
            </avue-crud>
        </basic-container>
    </div>
</template>
<script>
    import {mapGetters} from "vuex";
    import {EditAuthConfig, DeleteAuthConfig, GetAuthConfigList} from "@/api/justAuthConfig";

    export default {
        inject: ["reload"],
        props: {},
        data() {
            return {
                form: {},
                uploadObj: {},
                tableLoading: false,
                pageList: [],
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10
                },
                data: [],
                initUrl: window.location.protocol + '//' + window.location.host.toString() + '/user/auth/callback/',
                optionDetail: {
                    index: true,
                    size: "small",
                    dialogWidth: 780,
                    labelWidth: 210,
                    dialogClickModal: false,
                    searchBtn: true,
                    searchShow: false,
                    menuWidth: 280,
                    align: "center",
                    editBtnText: "修改",
                    delBtnText: '删除',
                    border: true,
                    column: [
                        {
                            prop: "id",
                            hide: true,
                            editDisplay: false,
                            addDisplay: false
                        },
                        {
                            label: 'appKey',
                            prop: "clientId",
                            span: 24,
                            rules: [{required: true, message: "请输入appKey", trigger: blur}]
                        },
                        {
                            label: 'appSecret',
                            prop: "clientSecret",
                            span: 24,
                            rules: [{required: true, message: "请输入appSecret", trigger: blur}]
                        },
                        {
                            label: '类型',
                            prop: "authType",
                            span: 24,
                            search: true,
                            type: "select",
                            dicData: [
                                // {label: "微信", value: "WECHAT"},
                                {label: "企业微信", value: "WECHAT_ENTERPRISE"},
                                {label: "钉钉", value: "DINGTALK"},
                                // {label: "腾讯QQ", value: "QQ"},
                                // {label: "新浪微博", value: "WEIBO"},
                                // {label: "百度", value: "BAIDU"},
                            ],
                            rules: [{required: true, message: "请选择类型", trigger: blur}],
                        },
                        {
                            label: '回调地址',
                            prop: "redirectUri",
                            span: 24,
                            rules: [{required: true, message: "请输入回调地址", trigger: blur}]
                        },
                        // // {
                        // //     label: '支付宝公钥',
                        // //     prop: "alipayPublicKey",
                        // //     span: 24,
                        // //     rules: [{required: false, message: "请输入支付宝公钥", trigger: blur}]
                        // // },
                        {
                            label: '企业微信授权方的网页应用ID',
                            prop: "agentId",
                            span: 24,
                            display: false,
                            rules: [{required: true, message: "请输入企业微信授权方的网页应用ID", trigger: blur}]
                        },
                        {
                            label: '备注',
                            prop: "bz",
                            span: 24,
                            type: "textarea",
                            showColumn: false,
                        },
                    ]
                }
            }
        },
        computed: {
            ...mapGetters(["userInfo"])
        },
        created() {
            this.onLoad(this.pageParam());
        },
        watch: {
            'form.authType': {
                handler(val) {
                    var agentId = this.findObject(this.optionDetail.column, 'agentId')
                    this.form.redirectUri = this.initUrl + val;
                    if (val === 'WECHAT_ENTERPRISE') {
                        agentId.display = true
                    } else {
                        agentId.display = false
                    }
                },
                immediate: true
            },
        },
        methods: {
            beforeOpen(done, type) {
                console.log(this.form)
                if (['view', 'edit'].includes(type)) {
                    // 查看和编辑逻辑
                } else {
                    //新增逻辑
                    //一定要用setTimeout包裹，由于form组件底层一些机制的设计
                    setTimeout(() => {
                        this.form.redirectUri = this.initUrl;
                    }, 0)
                }
                done();
            },
            saveHandle(row, done, loading) {
                console.log("save", row);
                EditAuthConfig(row).then(res => {
                    if (res.data.code == '00000') {
                        this.$message({type: "success", message: "操作成功"});
                    }
                    this.onLoad(this.pageParam())
                    done();
                }).catch(res => {
                    loading();
                    this.$message.error(res.info);
                });
            },
            // 编辑
            editHandle(row, done, loading) {
                setTimeout(() => {
                    EditAuthConfig(row).then(res => {
                        if (res.data.code == '00000') {
                            this.$message({type: "success", message: "操作成功"});
                        }
                        this.onLoad(this.pageParam())
                        loading();
                    }).catch(res => {
                        done();
                        this.$message.error(res.info);
                    });
                }, 1000)
            },
            // 删除
            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    DeleteAuthConfig({"id": row.id}).then(res => {
                        if (res.data.code === '00000') {
                            this.$message({type: "success", message: "删除成功"});
                            this.onLoad(this.pageParam())
                        }
                    })
                }).catch(() => {
                    console.log('已取消删除操作')
                });
            },
            // 列表查询
            onLoad(param) {
                this.tableLoading = true
                GetAuthConfigList(param).then(res => {
                    this.tableLoading = false
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.pageList = data.records;
                    this.data = data.records;
                    this.pageList.forEach(item => {
                        item.bz = item.bz ? item.bz : '';
                    });
                })
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad(this.pageParam())
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad(this.pageParam())
            },
            pageParam() {
                return {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                }
            },
            searchChange(param, done) {
                var pageParam = this.pageParam()
                pageParam.queryParam = param
                this.onLoad(pageParam)
                done()
            },
        }
    }
</script>
<style scoped>
    .basic-container >>> .el-card .avue-form__menu {
        width: 25%;
        text-align: left;
        padding-top: 0;
    }

    .avue-view {
        height: 100%;
    }
</style>
