<template>
    <basic-container>
        <h3 class="pageTitle">认证日志</h3>
        <avue-crud :data="data"
                   :option="option"
                   :page.sync="page"
                   :table-loading="loading"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {GetLoginLogs} from "@/api/settings";
    import {dateFormat} from "@/util/date";

    export default {
        inject: ["reload"],
        data() {
            return {
                page: {
                    currentPage: 1,
                    total: 1,
                    pageSize: 10,
                },
                obj: {},
                data: [],
                loading: false,
            };
        },
        created() {
            this.getInfo();
        },
        methods: {
            getInfo() {
                this.loading = true;
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {},
                };
                GetLoginLogs(context).then((res) => {
                    this.loading = false;
                    if (res.data && res.data.code === "00000") {
                        const data = res.data.info;
                        this.page.currentPage = data.current;
                        this.page.total = data.total;
                        this.page.pageSize = data.size;
                        this.data = data.records.map((item) => {
                            item.loginTime = dateFormat(item.loginTime);
                            return item;
                        });
                    }
                });
            },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getInfo();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getInfo();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.getInfo();
            },
        },
        computed: {
            option() {
                return {
                    addBtn: false,
                    index: true,
                    size: "mini",
                    border: true,
                    menu: false,
                    align: "center",
                    menuAlign: "center",
                    dialogWidth: 580,
                    menuWidth: 150,
                    dialogClickModal: false,
                    // refreshBtn:false,
                    // columnBtn:false,
                    header: false,
                    column: [
                        {
                            label: "账号",
                            prop: "humanCode",
                        },
                        {
                            label: "IP地址",
                            prop: "ip",
                        },
                        {
                            label: "登入时间",
                            prop: "loginTime",
                        },
                        {
                            label: "浏览器",
                            prop: "browser",
                        },
                        {
                            label: "操作系统",
                            prop: "operatingSystem",
                        },
                        {
                            label: "地址详情",
                            prop: "address",
                        },
                    ],
                };
            },
        },
    };
</script>

<style scoped>
    .w {
        padding: 10px;
        padding-top: 0;
    }
</style>
