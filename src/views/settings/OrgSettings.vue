<template>
    <basic-container>
        <h3 class="pageTitle">组织机构管理</h3>
        <avue-crud ref="crud"
                   :table-loading="loading"
                   v-model="form"
                   :option="option"
                   :data="data"
                   :before-close="beforeClose"
                   @tree-load="treeLoad"
                   @row-save="rowSave"
                   @row-update="rowUpdate"
                   @row-del="rowDel"
                   @refresh-change="refresh"
                   @search-change="searchChange">
            <template slot-scope="scope" slot="menu">
                <el-button icon="el-icon-circle-plus-outline"
                           type="text"
                           :size="scope.size"
                           @click.stop="addHandle(scope.row)">添加子级组织
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
    import {GetLazyOrg, EditOrg, RemoveOrg} from "@/api/basetable";

    export default {
        data() {
            return {
                maps: new Map(),
                form: {},
                data: [],
                param: {},
                loading: false,
            };
        },
        created() {
            this.onLoad();
        },
        methods: {
            searchChange(param, done) {
                this.param = param;
                this.onLoad()
                done()
            },
            onLoad() {
                this.loading = true;
                let queryParam = this.param;
                GetLazyOrg(queryParam).then((res) => {
                    res.data.info.forEach((item) => {
                        item.parent == null ? (item.parent = "") : "";
                    });
                    this.data = res.data.info;
                    this.loading = false;
                });
            },
            addHandle(row) {
                this.$refs.crud.value.parent = row.id;
                this.$refs.crud.option.column.filter((item) => {
                    if (item.prop === "parent") {
                        item.value = row.id;
                        item.addDisabled = true;
                    }
                });
                this.$refs.crud.rowAdd();
            },
            treeLoad(tree, child, resolve){
                const id = tree.id
                this.maps.set(id, { tree,child, resolve })
                const param = {
                    parent: tree.id
                }
              GetLazyOrg(param).then((res)=>{
                    resolve(res.data.info);
                })
            },
            beforeClose(done) {
                this.$refs.crud.value.parent = "";
                this.$refs.crud.option.column.filter((item) => {
                    if (item.prop == "parent") {
                        item.value = "";
                        item.addDisabled = false;
                    }
                });
                done();
            },
            // 添加
            rowSave(row, done, loading) {
                EditOrg(row).then((res) => {
                        if (res.data.code == "00000") {
                            this.$message.success("操作成功！");
                          this.updateTable();
                            this.onLoad();
                            done();
                        } else {
                            this.$message.error(res.data.info);
                            loading();
                        }
                    }, () => {
                        this.$message.error("服务异常");
                        loading();
                    }
                );
            },
            // 修改
            rowUpdate(row, index, done, loading) {
                this.rowSave(row, done, loading);
            },
          updateTable() { // 在删除或者添加操作成功之后，调用此函数
            this.maps.forEach((item, key) => {
              const { tree, child, resolve } = this.maps.get(key)
              this.treeLoad(tree, child, resolve)
            })
          },
            // 删除
            rowDel(row) {
              if (row.hasChildren) {
                this.$message.warning("请先删除【" + row.orgname + "】的子级组织机构");
                return;
              }

                this.$confirm(
                    "此操作将永久删除组织机构【" + row.orgname + "】, 是否继续?",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                ).then(() => {
                    RemoveOrg(row.id).then(
                        (res) => {
                            if (res.data.code == "00000") {
                                this.$message.success("删除成功！");
                              this.$set(this.$refs.crud.$refs.table.store.states.lazyTreeNodeMap, row.id, [])
                                this.updateTable();
                              this.onLoad();
                            } else {
                                this.$message.error("操作失败");
                            }
                        },
                        () => {
                            this.$message.error("服务异常");
                        }
                    );
                }).catch(() => {
                });
            },
            refresh() {
                this.onLoad();
            },
        },
        computed: {
            option() {
                return {
                    lazy:true,
                    dialogWidth: 580,
                    menuWidth: 230,
                    // headerAlign: "center",
                    // border: true,
                    // stripe: true,
                    index: true,
                    border: true,
                    defaultExpandAll: false,
                    dialogClickModal: false,
                    editBtnText: "修改",
                    delBtnText: "删除",
                    refreshBtn: true,
                    columnBtn: true,
                    searchBtn: true,
                    searchShow: false,
                    size: "mini",
                    // menuType: "button",
                    column: [
                        {
                            label: "机构名称",
                            prop: "label",
                            rules: [{required: true, message: "机构名称不能为空"}],
                            span: 24,
                            // width: 300,
                            search: true,
                        },{
                            label: "机构简称",
                            prop: "orgshortname",
                            span: 24,
                            // width: 300,
                        },
                        {
                            label: "机构代码",
                            prop: "code",
                            rules: [{required: true, message: "机构代码不能为空"}],
                            span: 24,
                            // width: 200,
                            align: "center",
                        },
                        {
                            label: "上级菜单",
                            prop: "parent",
                            type: "tree",
                            dicData: this.data,
                            span: 24,
                            props: {
                                value: "id",
                            },
                            hide: true,
                        },
                        {
                            label: "是否有效",
                            prop: "valid",
                            type: "radio",
                            width: 200,
                            dicData: [
                                {label: "有效", value: "1"},
                                {label: "无效", value: "0"},
                            ],
                            rules: [{required: true, message: "是否有效不能为空"}],
                            value: "1",
                            align: "center",
                        },
                        {
                            label: "显示顺序",
                            prop: "displayorder",
                            type: "number",
                            hide: true,
                        },
                        {
                            label: "机构类型",
                            prop: "categoryId",
                            span: 24,
                            align: "center",
                        },
                        {
                            label: "机构描述",
                            prop: "orgdescription",
                            type: "textarea",
                            maxlength: 150,
                            span: 24,
                            showWordLimit: true,
                            hide: true,
                        },
                    ],
                };
            },
        },
    };
</script>

<style scoped>
    .basic-container {
        height: 100%;
        border-radius: 10px;
    }

    .basic-container::v-deep .el-card {
        overflow: auto;
        height: 101%;
    }
</style>
