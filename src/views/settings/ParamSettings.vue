<template>
    <div>
        <basic-container>
            <avue-crud :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="pageList"
                       :page.sync="page"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       :upload-after="uploadAfter"
                       @row-save="saveHandle"
                       @row-update="editHandle"
                       @row-del="deleteHandle"
                       @refresh-change="refresh"
                       @search-change="searchChange">
            </avue-crud>
        </basic-container>
    </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteParam, EditParam, GetSysParamList} from "@/api/sysParam";

export default {
        inject: ["reload"],
        data() {
            return {
                uploadObj: {},
                tableLoading: false,
                pageList: [],
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10
                },
                data: [],
                optionDetail:{
                    index: true,
                    size: "mini",
                    dialogWidth: 580,
                    dialogClickModal: false,
                    searchBtn: true,
                    searchShow: false,
                    menuWidth: 150,
                    align: "center",
                    editBtnText: "修改",
                    delBtnText: '删除',
                    border: true,
                    column: [
                        {
                            prop: "id",
                            hide: true,
                            editDisplay: false,
                            addDisplay: false
                        },
                        {
                            label: '参数名称',
                            prop: "name",
                            span: 24,
                            search: true,
                            rules: [{required: true, message: "请输入参数名称", trigger: blur}]
                        },
                        {
                            label: '参数值',
                            prop: "value",
                            span: 24,
                            rules: [{required: true, message: "请输入参数值", trigger: blur}]
                        },
                        {
                            label: '参数类型',
                            prop: "type",
                            span: 24,
                            rules: [{required: true, message: "请输入参数类型", trigger: blur}]
                        },
                        {
                            label: '备注',
                            prop: "bz",
                            span: 24,
                            type: "textarea"
                        },{
                            label: "图片",
                            prop: "img",
                            type: "upload",
                            listType: "picture-img",
                            accept:'image/png, image/jpeg',
                            tip: '只能上传jpg/png',
                            uploadError: (error, column) => {
                              if(error == '文件太大不符合'){
                                this.$message.error('图片大小超过限制')
                              }
                              else{
                                this.$message.error(error)
                              }
                            },
                            uploadBefore:(file, done, loading,column) =>{
                              const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
                              const isFlag =
                                  suffixName === "jpg" || suffixName === "jpeg" || suffixName === "png";
                              if (!isFlag) {
                                this.$message({
                                  message: "格式不支持!",
                                  type: "error",
                                });
                                loading()  //阻断上传
                              }
                              else{
                                done()  //继续上传
                              }
                            },
                            span: 24,
                            showColumn: false,
                            propsHttp: {
                                home: window.location.origin,
                                res: "info"
                            },
                            // tip: "只能上传jpg/png用户头像，且不超过500kb",
                            action: "/file/upload"
                        }
                    ]
                }
            }
        },
        computed: {
            ...mapGetters(["userInfo"])
        },
        created() {
            this.onLoad(this.pageParam());
        },
        methods: {
            saveHandle(row, done, loading) {
                let arr = [];
                arr.push(this.uploadObj);
                row.img = JSON.stringify(arr);
                console.log("save", row);
                EditParam(row).then(res => {
                    if (res.data.code == '00000') {
                        this.$message({type: "success", message: "操作成功"});
                    }
                    this.uploadObj = {};
                    this.onLoad(this.pageParam())
                    done();
                }).catch(res => {
                    loading();
                    this.$message.error(res.info);
                    this.uploadObj = {};
                });
            },
            // 编辑
            editHandle(row, done, loading) {
                if (JSON.stringify(this.uploadObj) == "{}") {
                    // console.log(this.uploadObj + "图片没修改");
                    row.img = row.uploadObj;
                } else {
                    // console.log(this.uploadObj + "图片已修改");
                    row.img = [];
                    row.img.push(this.uploadObj);
                }
                row.img = JSON.stringify(row.img);
                console.log(row);
                EditParam(row).then(res => {
                    if (res.data.code == '00000') {
                        this.$message({type: "success", message: "操作成功"});
                    }
                    this.uploadObj = {};
                    this.onLoad(this.pageParam())
                    loading();
                }).catch(res => {
                    done();
                    this.$message.error(res.info);
                    this.uploadObj = {};
                });
            },
            // 删除
            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    DeleteParam({"id": row.id}).then(res => {
                        if (res.data.code === '00000') {
                            this.$message({type: "success", message: "删除成功"});
                            this.onLoad(this.pageParam())
                        }
                    })
                }).catch(() => {
                    console.log('已取消删除操作')
                });
            },
            // 列表查询
            onLoad(param) {
                this.tableLoading = true
                GetSysParamList(param).then(res => {
                    this.tableLoading = false
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.pageList = data.records;
                    this.data = data.records;
                    this.data.forEach(item => {
                        item.uploadObj = JSON.parse(item.img);
                        item.img = JSON.parse(item.img)[0].url;
                    });

                })
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad(this.pageParam())
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad(this.pageParam())
            },
            pageParam() {
                return {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                }
            },
            searchChange(param, done) {
                var pageParam = this.pageParam()
                pageParam.queryParam = param
                this.onLoad(pageParam)
                done()
            },
            uploadAfter(res, done) {
                if (res) {
                    this.uploadObj = res;
                    done();
                    this.$message.success("上传成功");
                }
            },
            refresh() {
                this.onLoad(this.pageParam());
            },
        }
    }
</script>
<style scoped>
    .basic-container >>> .el-card .avue-form__menu {
        width: 25%;
        text-align: left;
        padding-top: 0;
    }

    .avue-view {
        height: 100%;
    }
</style>
