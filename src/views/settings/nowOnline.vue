<template>
  <basic-container>
    <h3 class="pageTitle">当前登录</h3>
    <el-alert style="margin:10px 0;" title="当前展示所有使用该账号登录的地址信息，您可以选择踢出其他非本人登录信息。客户端类型的数据为操作系统和浏览器的内核信息，信息获取主要取决于第三方是否开放，此信息可能存在一定误差，仅供参考。" type="info" show-icon></el-alert>
    <avue-crud :data="data" :option="option">

    </avue-crud>
  </basic-container>
</template>

<script>
export default {
 data() {
      return {
        data: [
          {
            humanCode:'10001',
            ip:'************',
            loginTime:'2021-03-17 15:33:18',
            browser:'Chrome 8',
            operatingSystem:'Mac OS X',
            address:'内网 内网',
          },
          {
            humanCode:'10001',
            ip:'************',
            loginTime:'2021-03-17 15:33:18',
            browser:'Chrome 8',
            operatingSystem:'Mac OS X',
            address:'外网',
          },
        ],
        option:{
          title:'表格的标题',
          addBtn: false,
          border:true,
          page:false,
          align:'center',
          menuAlign:'center',
          delBtnText:'踢出',
          editBtn: false,
          // refreshBtn:false,
          // columnBtn:false,
          header:false,
          column: [
          {
            label: "账号",
            prop: "humanCode",
          },
          {
            label: "IP地址",
            prop: "ip",
          },
          {
            label: "登入时间",
            prop: "loginTime",
          },
          {
            label: "浏览器",
            prop: "browser",
          },
          {
            label: "操作系统",
            prop: "operatingSystem",
          },
          {
            label: "地址详情",
            prop: "address",
          },
        ]
        }
      }
    }
  }
</script>

<style scoped>
    .basic-container >>> .el-alert--info.is-light{
        background: #ECF5FF;
        color: #7FBEFF;
    }
</style>