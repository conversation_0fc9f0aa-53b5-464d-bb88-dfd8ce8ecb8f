<template>
    <basic-container>
        <h3 class="pageTitle">账号审计</h3>
        <avue-crud :data="data" :option="option" :cell-style="cellStyle">

        </avue-crud>
    </basic-container>
</template>

<script>
    export default {
        data() {
            return {
                data: [
                    {
                        number: '0 个休眠账号',
                        tip: '200天内没有登录的账号'
                    }, {
                        number: '0 个孤儿账号',
                        tip: '创建来源未知的账号'
                    }, {
                        number: '0 个密码强度不符合要求',
                        tip: '账号的密码强度小于密码策略对它的要求'
                    }, {
                        number: '0 个账号不符合规范',
                        tip: '账号不符合系统定义的规范'
                    },

                ],
                option: {
                    title: '表格的标题',
                    addBtn: false,
                    border: true,
                    page: false,
                    align: 'left',
                    menuAlign: 'center',
                    editBtnText: '自定义',
                    delBtn: false,
                    editTitle: false,
                    header: false,
                    column: [
                        {
                            label: "审计项目",
                            prop: "number",
                        },
                        {
                            label: "提示",
                            prop: "tip",
                        }
                    ]
                }
            }
        },
        methods: {
            cellStyle({row, columnIndex}) {
                // console.log(row, column, rowIndex, columnIndex)
                if (columnIndex == 0) {
                    if (row.money <= 3000) {
                        return {
                            color: 'green',
                            fontWeight: 'bold',
                            fontSize: '20'
                        }
                    } else {
                        return {
                            color: 'red',
                            fontWeight: 'bold',
                            fontSize: '20'
                        }
                    }
                }
            }
        }
    }
</script>
<style>
</style>
