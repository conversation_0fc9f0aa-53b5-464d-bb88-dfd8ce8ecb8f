<template>
  <basic-container>
      <h3 class="pageTitle">认证审计</h3>
      <avue-crud :data="data" :option="option" :cell-style="cellStyle">

    </avue-crud>
  </basic-container>
</template>

<script>
export default {
 data() {
      return {
        data: [
          {
            number:'0 个恶意认证账号',
            tip:'同一个账号当天发生的认证成功次数超过50次'
          },{
            number:'0 个暴力密码猜解账号',
            tip:'同一个账号当天发生的认证失败次数超过3次'
          },{
            number:'0 个恶意IP认证',
            tip:'同一个IP地址当天认证的总次数超过50次'
          },
        ],
        option:{
          title:'表格的标题',
          addBtn: false,
          border:true,
          page:false,
          align:'left',
          menuAlign:'center',
          editBtnText:'自定义',
          delBtn:false,
          editTitle: false,
          header:false,
          column: [
          {
            label: "审计项目",
            prop: "number",
          },
          {
            label: "提示",
            prop: "tip",
          }
        ]
        }
      }
    },
    methods:{
      cellStyle({row,column,rowIndex,columnIndex}){
        if(columnIndex==0){
          if(row.money<=3000){
            return {
              color:'green',
              fontWeight:'bold',
              fontSize:'20'
            }
          }else{
            return {
              color:'red',
              fontWeight:'bold',
              fontSize:'20'
            }
          }
        }
      }
    }
  }
</script>
<style>

</style>