<template>
  <basic-container>
    <h3 class="pageTitle">模块管理</h3>
    <avue-crud ref="crud"
               :table-loading="loading"
               v-model="form"
               :option="option"
               :data="data"
               :before-close="beforeClose"
               @row-save="rowSave"
               @row-update="rowUpdate"
               @row-del="rowDel"
               @refresh-change="refresh">
      <template slot="icon" slot-scope="scope">
        <i :class="scope.row.icon" style="font-size:24px"></i>
        <span v-if="!scope.row.icon">-</span>
      </template>
      <!--            <template slot-scope="scope" slot="menu">
                      <el-button icon="el-icon-circle-plus-outline"
                                 type="text"
                                 :size="scope.size"
                                 @click.stop="addHandle(scope.row)">添加子模块
                      </el-button>
                  </template>-->
    </avue-crud>
  </basic-container>
</template>

<script>
import {DeleteModuleList, EditModuleList, GetModuleTree} from "@/api/settings";

export default {
  data() {
    return {
      form: {},
      data: [],
      loading: false
    };
  },
  created() {
    this.onLoad();
  },
  methods: {
    onLoad() {
      this.loading = true;
      GetModuleTree().then(res => {
        res.data.info.forEach(item => {
          item.parentId == null ? (item.parentId = "") : "";
        });
        this.data = res.data.info;
        this.loading = false;
      });
    },
    /*addHandle(row) {
        // this.$refs.crud.value.parentId = row.id;
        this.$refs.crud.option.column.filter(item => {
            if (item.prop === "parentId") {
                item.value = row.id;
                item.addDisabled = true;
            }
        });
        this.$refs.crud.rowAdd();
    },*/
    beforeClose(done) {
      this.$refs.crud.option.column.filter(item => {
        if (item.prop === "parentId") {
          item.value = "";
          item.addDisabled = false;
        }
      });
      done();
    },
    // 添加
    rowSave(row, done, loading) {
      EditModuleList(row).then(res => {
            if (res.data.code == "00000") {
              this.$message.success("操作成功！");
              this.onLoad();
              done();
            } else {
              this.$message.error(res.data.info);
              loading();
            }
          }, () => {
            this.$message.error("服务异常");
            loading();
          }
      );
    },
    // 修改
    rowUpdate(row, index, done, loading) {
      this.rowSave(row, done, loading);
    },
    // 删除
    rowDel(row) {
      console.log(row);
      if (row.children.length != 0) {
        this.$message.warning("请先删除【" + row.name + "】的子级菜单");
        return;
      }

      this.$confirm(
          "此操作将永久删除菜单【" + row.name + "】, 是否继续?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
      ).then(() => {
        DeleteModuleList(row.id).then(res => {
              if (res.data.code == "00000") {
                this.$message.success("删除成功！");
                this.onLoad();
              } else {
                this.$message.error("操作失败");
              }
            }, () => {
              this.$message.error("服务异常");
            }
        );
      }).catch(() => {
      });
    },
    refresh() {
      this.onLoad();
    }
  },
  computed: {
    option() {
      return {
        dialogWidth: 580,
        size: "mini",
        index: true,
        border: true,
        // tabs: true,
        // stripe: true,
        defaultExpandAll: false,
        dialogClickModal: false,
        menuWidth: 230,
        // align: "left",
        // span: 24,
        // menuType: "button",
        editBtnText: "修改",
        delBtnText: "删除",
        refreshBtn: false,
        columnBtn: false,
        column: [
          {
            label: "链接名称",
            prop: "name",
            span:24,
          },
          {
            label: "链接地址",
            prop: "pattern",
            span:24,
          },
          {
            label: "链接类型",
            prop: "type",
            align: "center",
            span:24,
            type: "select",
            placeholder: "请选择链接类型",
            dicData: [
              {label: "接口", value: "接口"},
              {label: "页面", value: "页面"},
            ],
          },
          {
            label: "父节点",
            prop: "parentId",
            align: "center",
            hide: true,
            span:24,
            type: "tree",
            placeholder: "请选择父节点",
            dicData: this.data,
            props: {
              label: "name",
              value: "id",
            },
          }
        ],
        group: [
          {
            // label: "基本信息",
            prop: "group1",
            column: [
            ],
          },
        ],
      };
    }
  }
};
</script>

<style scoped>
.basic-container {
  height: 100%;
}

.basic-container::v-deep .el-card {
  overflow: auto;
  height: 101%;
}
</style>
