<template>
  <div>
    <basic-container>
      <avue-crud :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @refresh-change="refresh"
                 @search-change="searchChange">
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteDict, EditDict, QueryPageDict} from "@/api/sysDict";

export default {
  inject: ["reload"],
  data() {
    return {
      uploadObj: {},
      tableLoading: false,
      pageList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      data: [],
      optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 580,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 150,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            prop: "id",
            hide: true,
            editDisplay: false,
            addDisplay: false
          },
          {
            label: '编码',
            prop: "code",
            span: 24,
            search: true,
            rules: [{required: true, message: "请输入字典编码", trigger: blur}]
          }, {
            label: '名称',
            prop: "name",
            span: 24,
            search: true,
            rules: [{required: true, message: "请输入字典名称", trigger: blur}]
          },
          {
            label: '字典值',
            prop: "value",
            span: 24,
            rules: [{required: true, message: "请输入字典值", trigger: blur}]
          },
          {
            label: '字典扩展',
            prop: "extend",
            span: 24,
          },
          {
            label: "附件",
            prop: "attachment",
            hide: true,
            type: "upload",
            accept: '*/*',
            // tip: '上传附件',
            uploadError: (error, column) => {
              if (error == '文件太大不符合') {
                this.$message.error('大小超过限制')
              } else {
                this.$message.error(error)
              }
            },
            uploadBefore: (file, done, loading, column) => {
              const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
              // const isFlag =
              //     suffixName === "jpg" || suffixName === "jpeg" || suffixName === "png";
              // if (!isFlag) {
              //     this.$message({
              //         message: "格式不支持!",
              //         type: "error",
              //     });
              //     loading()  //阻断上传
              // }
              // else{
              done()  //继续上传
              // }
            },
            span: 24,
            propsHttp: {
              home: window.location.origin,
              res: "info"
            },
            action: "/file/upload"
          },
          {
            label: '排序',
            prop: "sortBy",
            type: "number",
          },
          {
            label: '备注',
            prop: "remark",
            span: 24,
            type: "textarea"
          },
        ]
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  created() {
    this.onLoad(this.pageParam());
  },
  methods: {
    saveHandle(row, done, loading) {
      if (Array.isArray(row.attachment)) {
        row.attachment = "";
      }
      EditDict(row).then(res => {
        if (res.data.code == '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.uploadObj = {};
        this.onLoad(this.pageParam())
        done();
      }).catch(res => {
        loading();
        this.$message.error(res.info);
        this.uploadObj = {};
      });
    },
    // 编辑
    editHandle(row, done, loading) {
      EditDict(row).then(res => {
        if (res.data.code == '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.uploadObj = {};
        this.onLoad(this.pageParam())
        loading();
      }).catch(res => {
        done();
        this.$message.error(res.info);
        this.uploadObj = {};
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteDict({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      QueryPageDict(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
        this.data = data.records;
        /*this.data.forEach(item => {
            item.uploadObj = JSON.parse(item.img);
            item.img = JSON.parse(item.img)[0].url;
        });*/

      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
</style>
