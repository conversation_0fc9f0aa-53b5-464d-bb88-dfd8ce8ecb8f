<template>
  <basic-container>
    <h3 class="pageTitle">
      密码生成策略
      <el-tag class="smallFont">
        <i class="el-icon-info"></i> 此处统一设置新用户的初始密码</el-tag
      >
    </h3>
    <avue-form :option="option" v-model="form" @submit="handleSubmit">
        <template slot="group1Header">
        <h4>
          密码安全策略
          <el-tag class="smallFont">
            <i class="el-icon-info"></i>
            此处设置用户密码安全策略</el-tag
          >
        </h4>
      </template>
      <template slot="group2Header">
        <h4>
          密码修改策略
          <el-tag class="smallFont">
            <i class="el-icon-info"></i>
            此处设置用户在修改新密码时限制新密码的强度</el-tag
          >
        </h4>
      </template>
    </avue-form>
  </basic-container>
</template>

<script>
var DIC = {
  YXQ: [
    {
      label: "30天",
      value: "30",
    },
    {
      label: "60天",
      value: "60",
    },
    {
      label: "90天",
      value: "90",
    },
    {
      label: "120天",
      value: "120",
    },
  ],
  SCCL: [
    {
      label: "取身份证件号后6位",
      value: "idcard",
    },
    {
      label: "取账号名",
      value: "loginnumber",
    },
    {
      label: "指定密码",
      value: "manageset",
    },
  ],
  INCLUDE: [
    {
      label: "是",
      value: 1,
    },
    {
      label: "否",
      value: 0,
    },
  ],
};
export default {
  data() {
    return {
      form: {},
      option: {
        submitText: "提交",
        labelWidth: 150,
        column: [
          {
            label: "密码生成策略",
            prop: "sccl",
            span: 12,
            row: true,
            type: "radio",
            dicData: DIC.SCCL,
          },
          {
            label: "指定密码",
            span: 12,
            row: true,
            prop: "setnumber",
            display: true,
          },
        ],
        group: [
          {
            label: "密码安全策略",
            collapse: false,
            prop: "group1",
            column: [
              {
                label: "密码输错次数限制",
                prop: "number",
                type: "number",
                span: 12,
                row: true,
                value: 3,
                minRows: 0,
              },
              {
                label: "登录超时限制(分钟)",
                prop: "overtime",
                type: "number",
                span: 12,
                row: true,
                value: 20,
                minRows: 10,
              },
              {
                label: "用户密码有效期",
                prop: "mmyxq",
                type: "select",
                dicData: DIC.YXQ,
                span: 12,
                row: true,
              },
            ],
          },
          {
            label: "密码修改策略",
            arrow: false,
            prop: "group2",
            column: [
              {
                label: "密码最小长度",
                prop: "passwordlength",
                type: "number",
                span: 12,
                row: true,
                value: 8,
                minRows: 8,
              },
              {
                label: "是否包含数字",
                prop: "sfsz",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
              },
              {
                label: "是否包含小写字母",
                prop: "sfxxzm",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
              },
              {
                label: "是否包含大写字母",
                prop: "sfdxzm",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
              },
              {
                label: "是否包含符号",
                prop: "sffh",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
              },
              {
                label: "是否允许包含用户名",
                prop: "sfyhm",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
              },
            ],
          },
        ],
      },
    };
  },
  watch: {
    "form.sccl": {
      handler(val) {
        var setnumber = this.findObject(this.option.column, "setnumber");
        if (val === "manageset") {
          setnumber.display = true;
        } else {
          setnumber.display = false;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.option1 = Object.assign(this.deepClone(this.option), {
      card: true,
    });
  },
  methods: {
    handleSubmit(form) {
      this.$message.success(JSON.stringify(this.form));
    },
  },
};
</script>

<style scoped>
.basic-container >>> .avue-group__header {
  margin-bottom: 10px;
  border-bottom: none;
}
.basic-container >>> .avue-group__title {
  color: #303133;
  font-weight: bold;
}
.smallFont {
  font-size: 12px;
  font-weight: normal;
  margin-left: 20px;
}
</style>