<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refresh"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.dictfield_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/dict/dictfield";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "英文名",
              prop: "fielden",
              rules: [{
                required: true,
                message: "请输入英文名",
                trigger: "blur"
              }]
            },
            {
              label: "中文名",
              prop: "fieldzh",
              search:true,
              rules: [{
                required: true,
                message: "请输入中文名",
                trigger: "blur"
              }]
            },
            {
              label: "排序",
              prop: "sort",
              type: "number",
              rules: [{
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }]
            },
            {
              label: "字段类型",
              prop: "fieldType",
              value:'文本',
              type: "select",
              dicMethod: "post",
              dicUrl: '/sytSysDict/list?code=DICT_FIELD_TYPE',
              props: {
                label: "name",
                value: "value",
              },
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }]
            },
            {
              label: "字典分组",
              prop: "gid",
              type: "select",
              search: true,
              dicUrl: "/dict/dictgroup/all",
              props: {
                label: "name",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择",
                trigger: "blur"
              }]
            },
            {
              label: "是否显示",
              prop: "sfxs",
              type: "select",
              value:'是',
              dicData: [
                {
                  label: "否",
                  value: "否"
                },
                {
                  label: "是",
                  value: "是"
                }
              ],
            },
            {
              label: "是否编辑",
              prop: "sfbj",
              type: "select",
              value:'否',
              dicData: [
                {
                  label: "否",
                  value: "否"
                },
                {
                  label: "是",
                  value: "是"
                }
              ],
            },
            {
              label: "是否搜索",
              prop: "sfss",
              type: "select",
              dicData: [
                {
                  label: "否",
                  value: "否"
                },
                {
                  label: "是",
                  value: "是"
                }
              ],
            },
            {
              label: "样式",
              prop: "style",
            },
            {
              label: "验证规则",
              prop: "validRules",
            },
            {
              label: "数据来源",
              prop: "fieldData",
            },
            {
              label: "扩展字段",
              prop: "fieldAttribute",
              span: 24,
              type: "textarea",
            },
            {
              label: "备注",
              prop: "bz",
              span: 24,
              type: "textarea",
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.dictfield_add, false),
          viewBtn: this.vaildData(this.permission.dictfield_view, false),
          delBtn: this.vaildData(this.permission.dictfield_delete, false),
          editBtn: this.vaildData(this.permission.dictfield_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh() {
        this.onLoad(this.page);
      },
    }
  };
</script>

<style>
</style>
