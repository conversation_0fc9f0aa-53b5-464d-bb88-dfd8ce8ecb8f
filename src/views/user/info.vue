<template>
    <basic-container>
        <h3 class="pageTitle">个人信息</h3>
        <avue-form ref="form" v-model="obj" :option="option">
            <template slot="menuForm">
                <el-row>
                    <el-col :span="12" style="text-align:left;padding-left:85px;"><el-button icon="el-icon-check" type="primary" @click="handleSubmit">保 存</el-button></el-col>
                </el-row>
                
                <!-- <el-button icon="el-icon-delete" @click="handleEmpty">清 空</el-button> -->
            </template>
        </avue-form>
    </basic-container>
</template>

<script>
    import option from "@/const/user/info";
    // switchRole
    import {getUserInfo, setUserUpdateInfo} from "@/api/user";

    export default {
        inject: ["reload"],
        data() {
            return {
                type: "info",
                option: option,
                // form: [],
                obj: {},
            };
        },
        mounted() {

        },
        created() {
            this.getUserInfo();
        },
        methods: {
            // handleEmpty() {
            //     this.$refs.form.resetForm();
            // },
            getUserInfo() {
                getUserInfo().then((res) => {
                    if (res.data.code === "00000") {
                        this.obj = res.data.info;
                    }
                });
            },
            handleSubmit() {
                this.$refs.form.validate((vaild, done) => {
                    if (vaild) {
                        setUserUpdateInfo(this.obj).then(res => {
                            if (res.data.code == "00000") {
                                this.$message({
                                    type: "success",
                                    message: "编辑成功!"
                                });
                                // done();
                                this.reload();
                            } else {
                                this.$message.error("编辑失败");
                                // done();
                                this.reload();
                            }
                        });
                    }
                })
            },
        }
    };
</script>

<style scoped>
.avue-form >>> .el-input__suffix{
  display: none;
}
</style>
