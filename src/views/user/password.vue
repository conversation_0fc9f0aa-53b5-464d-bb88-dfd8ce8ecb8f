<template>
  <basic-container>
    <h3 class="pageTitle">修改密码
      <el-tag class="smallFont" type="danger" v-if="includeTip">
        <i class="el-icon-info"></i> {{ includeTip }}</el-tag
      ></h3>
    <avue-form ref="form" v-model="obj" :option="option">
      <template slot="menuForm">
        <el-row>
          <el-col :span="24" style="text-align:left;padding-left:85px;">
            <el-button icon="el-icon-check" type="primary" @click="handleSubmit">保 存</el-button>
            <el-button icon="el-icon-delete" @click="handleEmpty">清 空</el-button>
          </el-col>
        </el-row>
      </template>
    </avue-form>
  </basic-container>
</template>

<script>
import option from "@/const/user/password";
import {setNewPassword} from "@/api/user";

export default {
  inject: ["reload"],
  data() {
    return {
      type: "info",
      option: option,
      obj: {},
      passwordRule: {},
      includeTip: "请使用包含数字大小写字母或特殊字符,10到16位作为密码",
    };
  },
  methods: {
    handleEmpty() {
      this.$refs.form.resetForm();
    },
    handleSubmit() {
      this.$refs.form.validate((vaild, done) => {
        if (vaild) {

          if (this.obj.newpassword !== this.obj.newpasswords) {
            this.$message.error("两次新密码不一致");
            done();
            return;
          }
          setNewPassword(this.obj).then(res => {
            console.log(res.data)
            if (res.data.code == "00000") {
              // this.$message({
              //     type: "success",
              //     message: "修改成功!"
              // });
              // window.location.href = "/logout";
              this.$confirm(
                  "修改成功, 重新登录",
                  "提示",
                  {
                    confirmButtonText: "确定",
                    // cancelButtonText: "取消",
                    // type: "warning",
                    showCancelButton: false,
                  }
              ).then(() => {
                window.location.href = "/logout";
              }).catch(() => {
                window.location.href = "/logout";
              });
            } else {
              this.$message.error("修改失败!" + res.data.info);
              this.reload();
            }
          });
        }
      })
    },
  }
};
</script>

<style lang="scss">
</style>
