<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
      </avue-crud>
    </basic-container>

  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {warningLeveldelete, warningLeveledit, warningLevelqueryPage} from "@/api/dataWarningLevel";
import 'codemirror/mode/sql/sql.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/lib/codemirror.css' // 核心样式
import 'codemirror/theme/cobalt.css' // 引入主题后还需要在 options 中指定主题才会生效

export default {
  inject: ["reload"],
  data() {
    return {
      form: {},
      tableLoading: false,
      pageList: [],
      page: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      dataSetTransformDtoList: [
        {
          transformType: 'js',
          transformScript: `function dataTransform(data){
                      //自定义脚本内容
                      return data;
                    }`,
        },
      ],
      transformScript: `function dataTransform(data){
              //自定义脚本内容
              return data;
            }`,
      itemFilterScriptId: '',
      setTypes: [
      ],
      cols: [],
      dialogCaseResult: false,
      caseResultTitle: '',
      caseResultContent: null,
      testMassageCode: null,
      dataSetParamDtoList: [],//查询参数table
      optionsSql: {
        mode: 'text/x-sql',
        tabSize: 2, // 缩进格式
        // theme: 'cobalt', // monokai主题，对应主题库 JS 需要提前引入
        lineNumbers: true, // 显示行号
        line: true,
        styleActiveLine: true, // 高亮选中行
        hintOptions: {
          completeSingle: true, // 当匹配只有一项的时候是否自动补全
        },
      },
      optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 1200,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 200,
        align: "center",
        editBtnText: "编辑",
        delBtnText: '删除',
        border: true,
        column: [
            {
              label: '预警规则名称',
              prop: "ruleId",
              search: true,
              type: "select",
              dicMethod: "post",
              dicUrl: '/dataWarning/rule/list',
              props: {
                label: "setName",
                value: "id",
              },
              rules: [{required: true, message: "请选择数据源", trigger: blur}]
            },
          {
            label: '预警级别名称',
            prop: "levelName",
            align: "center",
            rules: [{required: true, message: "请选择分类", trigger: blur}],
          },
          {
            label: '最大值',
            prop: "maxVal",
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: '最小值',
            prop: "minVal",
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: '颜色',
            prop: "color",
            type: "color",
            align: "center",
            rules: [{required: true, message: "请选择颜色", trigger: blur}],
          },
          {
            label: '预警原因',
            prop: "warnReason",
            type: "textarea",
            span: 24,
            rules: [{required: true, message: "请输入预警原因", trigger: blur}]
          },
          {
            label: '备注',
            prop: "bz",
            type: "textarea",
            span: 24,
          },
        ],
      },
    }
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  created() {
    this.onLoad(this.pageParam());
  },
  methods: {
    beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
        this.testMassageCode = '00000';
        if (this.form.caseResult) {
          this.cols = JSON.parse(this.form.caseResult);
        } else {
          this.cols = [];
        }
        if (this.form.fieldConfig) {
          this.previewData = JSON.parse(this.form.fieldConfig);
        } else {
          this.previewData = [];
        }
        this.dataSetTransformDtoList = this.form.dataSetTransformDtoList;
        this.dataSetParamDtoList = this.form.dataSetParamDtoList;
        this.dataSetDrillParamDtoList = this.form.dataSetDrillParamDtoList;
      } else {
        this.cols = [];
        this.previewData = [];
        this.caseResultContent = [];
        this.dataSetTransformDtoList = [];
        this.dataSetParamDtoList = [];
        this.dataSetDrillParamDtoList = [];
      }
      done();
    },
    saveHandle(row, done, loading) {
      this.editHandle(row, done, loading);
    },
    // 编辑
    editHandle(row, done, loading) {
      row.dataSetTransformDtoList = this.dataSetTransformDtoList;
      row.dataSetParamDtoList = this.dataSetParamDtoList;
      row.dataSetDrillParamDtoList = this.dataSetDrillParamDtoList;
      row.caseResult = JSON.stringify(this.cols);
      row.fieldConfig = JSON.stringify(this.previewData);
      warningLeveledit(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
        done();
      }).catch(res => {
        console.log(res.info)
        // this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        warningLeveldelete({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      warningLevelqueryPage(param).then(res => {
        console.log(`param`,param)
        console.log(`res`,res)
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}
::v-deep .fieldConfig .avue-crud__menu {
  display: none !important;
}
.avue-view {
  height: 100%;
}
</style>
