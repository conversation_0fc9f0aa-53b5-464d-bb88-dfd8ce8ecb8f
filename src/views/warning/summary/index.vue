<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-video-play"
                     size="small"
                     @click.stop="allisEnableFlag">全部启用</el-button>
          <el-button type="warning"
                     icon="el-icon-video-pause"
                     size="small"
                     @click.stop="alloffEnableFlag">全部停用</el-button>
        </template>
        <template slot-scope="{row}" slot="menu">
          <el-button type="text"
                     icon="el-icon-switch-button"
                     size="mini"
                     @click="implement(row)"
          >执行</el-button>
          <el-button type="text"
                     icon="el-icon-video-play"
                     size="mini"
                     @click="isEnableFlag(row)"
          >启用</el-button>
          <el-button type="text"
                     icon="el-icon-video-pause"
                     size="mini"
                     @click="offEnableFlag(row)"
          >停用</el-button>
          <el-button type="text"
                     icon="el-icon-time"
                     size="mini"
                     @click="record(row)"
          >记录</el-button>
        </template>
        <template slot="execcronForm" slot-scope="scope">
          <div id="execcronDiv">
            <el-row>
              <el-col :span="6" :offset="9">
                <cron-input v-model="cron" @change="change" @reset="reset"/>
              </el-col>
            </el-row>
          </div>
        </template>
      </avue-crud>
      <el-dialog width="70%" title="预警记录" :visible.sync="outerVisible" append-to-body>
        <el-dialog
            width="60%"
            title="预警名单"
            :visible.sync="innerVisible"
            append-to-body>
          <avue-crud ref="crud"
                     v-model="form"
                     :table-loading="tableLoading"
                     :option="inoptionRecord"
                     :data="inpagerecordlist"
                     :page.sync="page3"
                     @size-change="sizeChangeChecked"
                     @current-change="currentChangeChecked"
          >
          </avue-crud>
        </el-dialog>
        <avue-crud ref="crud"
                   v-model="form"
                   :table-loading="tableLoading"
                   :option="optionRecord"
                   :data="pagerecordlist"
                   :page.sync="page2"
                   @size-change="sizeChangeRecord"
                   @current-change="currentChangeRecord"
        >
          <template slot-scope="{row}" slot="menu">
            <el-button type="text"
                       icon="el-icon-tickets"
                       size="mini"
                       @click="checked(row)"
            >名单</el-button>
          </template>
        </avue-crud>
        <!--        <div slot="footer" class="dialog-footer">-->
        <!--          <el-button @click="outerVisible = false">取 消</el-button>-->
        <!--          <el-button type="primary" @click="innerVisible = true">打开内层 Dialog</el-button>-->
        <!--        </div>-->
      </el-dialog>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {
  DeleteTaskSummary,
  EditTaskSummary,
  execute,
  modifyEnableFlag,
  QueryTaskSummary
} from "@/api/dataWarningSummary";
import CronInput from 'vue-cron-generator/src/components/cron-input'
import {DEFAULT_CRON_EXPRESSION} from 'vue-cron-generator/src/constant/filed'
import {recordqueryPage} from "@/api/dataWarningRecord";
import {warnInfoqueryPage} from "@/api/dataWarningWarnInfo";
import {selectDictList} from "@/api/sysDict";

export default {
  inject: ["reload"],
  components: {
    CronInput
  },
  data() {
    return {
      form: {},
      outerVisible: false,
      innerVisible: false,
        list:[],
      cron: DEFAULT_CRON_EXPRESSION,
      tableLoading: false,
      pageList: [],
      rowrecord: ``,
      rowchecked: ``,
      pagerecordlist: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      page2: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      page3: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 1200,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 200,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: '名称',
            prop: "taskname",
            search: true,
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: '执行定时',
            prop: "execcron",
            formslot: true,
            span: 12,
            // rules: [{required: true, message: "请输入检查定时", trigger: blur}]
          },
          {
            label: '预警规则',
            prop: "ruleids",
            search: true,
            type: "select",
            multiple: true,
            dicMethod: "post",
            dicUrl: '/dataWarning/rule/list',
            props: {
              label: "setName",
              value: "id",
            },
            span: 24,
          },
          {
            label: '状态',
            prop: "enableflag",
            align: "center",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            filterable: true,
            dicData: [
              {value: 0,label: "启用"},
              {value: 1,label: "停用"},
            ],
            // rules: [{required: true, message: "请选择状态", trigger: blur}]
          },
          {
            label: '描述',
            prop: "taskdesc",
            span: 24,
            type: "textarea"
          },
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      },
      // optionRecord: {
      //   index: true,
      //   size: "mini",
      //   dialogWidth: 1200,
      //   dialogClickModal: false,
      //   searchBtn: true,
      //   searchShow: false,
      //   align: "center",
      //   editBtn: false,
      //   delBtn: false,
      //   header: false,
      //   border: true,
      //   menuWidth: 200,
      //   column: [
      //     {
      //       label: '分类',
      //       prop: "type",
      //       type: "select",
      //       search: true,
      //       filterable:true,
      //       cascaderItem:["setCode"],
      //       dicData: [
      //         {label: "学业预警",value: "学业预警"},
      //         {label: "考勤预警",value: "考勤预警"},
      //         {label: "消费预警",value: "消费预警"},
      //         {label: "安全预警",value: "安全预警"},
      //       ],
      //     },
      //     {
      //       label: '规则名称',
      //       prop: "rulename",
      //       search: true,
      //     },
      //     {
      //       label: '开始时间',
      //       prop: "starttime",
      //       search: true,
      //     },
      //     {
      //       label: '结束时间',
      //       prop: "stoptime",
      //       search: true,
      //     },
      //     {
      //       label: '运行状态',
      //       prop: "status",
      //       align: "center",
      //       type: "select",
      //       filterable:true,
      //       dicData: [
      //         {label: "运行失败",value: "0"},
      //         {label: "运行成功",value: "1"},
      //         {label: "运行中",value: "2"},
      //       ],
      //     },
      //   ],
      // },
      inoptionRecord: {
        index: true,
        size: "mini",
        dialogWidth: 1200,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        align: "center",
        editBtn: false,
        delBtn: false,
        header: false,
        border: true,
        menu: false,
        menuWidth: 200,
        column: [
          {
            label: "执行记录id",
            prop: "taskRecordId",
            overHidden:true,
            hide: true
          },
          {
            label: "预警规则id",
            prop: "ruleId",
            overHidden:true,
            hide: true
          },
          {
            label: "登录账号",
            prop: "humanCode",
            overHidden:true,
          },
          {
            label: "人员名称",
            prop: "humanName",
            overHidden:true,
          },
          {
            label: "所属机构",
            prop: "organizationNames",
            overHidden:true,
          },
          {
            label: "预警类型",
            prop: "warnType",
            overHidden:true,
          },
          {
            label: "预警名称",
            prop: "warnName",
            overHidden:true,
          },
          {
            label: "预警原因",
            prop: "warnReason",
            overHidden:true,
            row:true,
          },
          {
            label: "预警时间",
            prop: "warnTime",
            overHidden:true,
            row:true,
          },
          {
            label: "预警值",
            prop: "warnValue",
            overHidden:true,
          },
          {
            label: "预警级别",
            prop: "warnLevel",
            overHidden:true,
          },
        ],
      },
      inpagerecordlist: [],
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
      optionRecord(){
        return {
            index: true,
            size: "mini",
            dialogWidth: 1200,
            dialogClickModal: false,
            searchBtn: true,
            searchShow: false,
            align: "center",
            editBtn: false,
            delBtn: false,
            header: false,
            border: true,
            menuWidth: 200,
            column: [
                {
                    label: '分类',
                    prop: "type",
                    type: "select",
                    search: true,
                    filterable:true,
                    cascaderItem:["setCode"],
                    dicData: this.list,
                },
                {
                    label: '规则名称',
                    prop: "rulename",
                    search: true,
                },
                {
                    label: '开始时间',
                    prop: "starttime",
                    search: true,
                },
                {
                    label: '结束时间',
                    prop: "stoptime",
                    search: true,
                },
                {
                    label: '运行状态',
                    prop: "status",
                    align: "center",
                    type: "select",
                    filterable:true,
                    dicData: [
                        {label: "运行失败",value: "0"},
                        {label: "运行成功",value: "1"},
                        {label: "运行中",value: "2"},
                    ],
                },
            ],
        }
      }
  },
  created() {
      selectDictList({code: "WARNING_TYPE"}).then(res => {
          let data = res.data.info
          data.forEach(item => {
              if(item.value !== '全部'){
                  this.list.push({label: item.value,value: item.value})
              }

          })
      });
    this.onLoad(this.pageParam());
  },
  methods: {
    change(cron) {
      this.cron = cron
    },
    reset() {
      this.cron = DEFAULT_CRON_EXPRESSION
    },
    beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
        this.testMassageCode = '00000';
        if (this.form.execcron) {
          this.cron = this.form.execcron;
        }
      }
    },
    saveHandle(row, done, loading) {
      this.editHandle(row, done, loading);
    },
    //名单
    checked(row) {
      this.innerVisible = true;
      this.rowchecked = row;
      let param = this.pageParamChecked(row);
      warnInfoqueryPage(param).then(res =>{
        console.log(`param`,param)
        console.log(`res`,res)
        this.tableLoading = false
        const data = res.data.info;
        this.page3.currentPage = data.current;
        this.page3.total = data.total;
        this.page3.pageSize = data.size;
        this.page3.pageSize = data.size;
        for ( let i = 0 ; i < data.records.length; i++ ) {
          data.records[i].warnTime = data.records[i].warnTime.replace("T","-")
          data.records[i].warnTime = data.records[i].warnTime.replace("+0000","")
        }
        this.inpagerecordlist = data.records;
      })
    },
    // 编辑
    editHandle(row, done, loading) {
      var ruleids = row.ruleids;
      if (typeof ruleids ==='object') {
        row.ruleids = ruleids.join(',');
      }
      row.execcron = this.cron;
      EditTaskSummary(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
        done();
      }).catch(res => {
        done();
        // this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteTaskSummary({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    //全部启用
    allisEnableFlag() {
      let parm = {};
      parm.enableflag = 0;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "已全部启用"});
        }
        console.log(`res`,res)
        this.onLoad(this.pageParam())
      });
    },
    //全部停用
    alloffEnableFlag() {
      let parm = {};
      parm.enableflag = 1;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({ message: "已全部停用"});
        }
        console.log(`res`,res)
        this.onLoad(this.pageParam())
      });
    },
    //启用
    isEnableFlag(row) {
      let parm = {};
      parm.id = row.id;
      parm.enableflag = 0;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "已启用"});
        }
        console.log(`res`,res)
        this.onLoad(this.pageParam())
      });
    },
    //停用
    offEnableFlag(row) {
      let parm = {};
      parm.id = row.id;
      parm.enableflag = 1;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({ message: "已停用"});
        }
        console.log(`res`,res)
        this.onLoad(this.pageParam())
      });
    },
    //执行
    implement(row) {
      let parm = {};
      parm.id = row.id;
      execute(parm).then(res =>{
        console.log(`res`,res)
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "执行成功"});
        }
        this.onLoad(this.pageParam())
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      QueryTaskSummary(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    sizeChangeRecord(val) {
      this.page2.currentPage = 1;
      this.page2.pageSize = val;
      this.record(this.rowrecord)
    },
    sizeChangeChecked(val) {
      this.page3.currentPage = 1;
      this.page3.pageSize = val;
      this.checked(this.rowchecked)
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    currentChangeRecord(val) {
      this.page2.currentPage = val;
      this.record(this.rowrecord)
    },
    currentChangeChecked(val) {
      this.page3.currentPage = val;
      this.checked(this.rowchecked)
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    pageParamRecord(row) {
      return {
        page: this.page2.currentPage,
        pageSize: this.page2.pageSize,
        queryParam: {
          rulecode: row.ruleids
        }
      }
    },
    pageParamChecked(row) {
      return {
        page: this.page3.currentPage,
        pageSize: this.page3.pageSize,
        queryParam: {
          taskRecordId: row.id
        }
      }
    },
    //预警记录
    record(row) {
      this.outerVisible = true;
      this.rowrecord = row;
      let param = this.pageParamRecord(row);
      recordqueryPage(param).then(res =>{
        this.tableLoading = false
        const data = res.data.info;
        this.page2.currentPage = data.current;
        this.page2.total = data.total;
        this.page2.pageSize = data.size;
        for ( let i = 0 ; i < data.records.length; i++ ) {
          data.records[i].starttime = data.records[i].starttime.replace("T","-")
          data.records[i].starttime = data.records[i].starttime.replace("+0000","")
          data.records[i].stoptime = data.records[i].stoptime.replace("T","-")
          data.records[i].stoptime = data.records[i].stoptime.replace("+0000","")
        }
        this.pagerecordlist = data.records;
      })
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}
.el-col-offset-9 {
  margin-left: 0 !important;
  width: 100%;
}
::v-deep .el-table__cell > .cell > .el-button {
  padding: 0;
}
.avue-view {
  height: 100%;
}
</style>
