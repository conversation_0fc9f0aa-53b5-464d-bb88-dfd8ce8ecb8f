<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import CronInput from 'vue-cron-generator/src/components/cron-input';
import {
  recordqueryPage
} from "@/api/dataWarningRecord";
export default {
  inject: ["reload"],
  components: {
    CronInput
  },
  data() {
    return{
      row: {}
    }
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  created() {
    this.row = this.$route.query.row;
    let param = {
      page: 1,
      pageSize: 20,
      queryParam: {
        id: this.row.id
      }
    }
    recordqueryPage(param).then(res => {
      console.log(`res`,res)
    })
    console.log(`row`,this.row)
  },
  mounted() {

  },
  methods: {

  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}
::v-deep .fieldConfig .avue-crud__menu {
  display: none !important;
}
::v-deep .avue-crud__search {
  display: none !important;
}
.avue-view {
  height: 100%;
}
.el-col-offset-9 {
  margin-left: 0 !important;
  width: 100%;
}
::v-deep .el-table__cell > .cell > .el-button {
  padding: 0;
}
</style>
<style lang="less">
#checkCronDiv {
}
</style>
