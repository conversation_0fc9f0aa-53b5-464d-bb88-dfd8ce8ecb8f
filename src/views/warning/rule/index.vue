<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="primary"
                     icon="el-icon-video-play"
                     size="small"
                     @click.stop="allisEnableFlag">全部启用</el-button>
          <el-button type="warning"
                     icon="el-icon-video-pause"
                     size="small"
                     @click.stop="alloffEnableFlag">全部停用</el-button>
        </template>
        <template slot-scope="{row}" slot="menu">
          <el-button type="text"
                     icon="el-icon-circle-plus-outline"
                     size="mini"
                     @click="copy(row)"
          >复制</el-button>
          <el-button type="text"
                     icon="el-icon-switch-button"
                     size="mini"
                     @click="implement(row)"
          >执行</el-button>
          <el-button type="text"
                     icon="el-icon-video-play"
                     size="mini"
                     @click="isEnableFlag(row)"
          >启用</el-button>
          <el-button type="text"
                     icon="el-icon-video-pause"
                     size="mini"
                     @click="offEnableFlag(row)"
          >停用</el-button>
          <el-button type="text"
                     icon="el-icon-time"
                     size="mini"
                     @click="record(row)"
          >记录</el-button>
          <el-button type="text"
                     icon="el-icon-time"
                     size="mini"
                     @click="warnrulelist(row)"
          >特殊名单</el-button>
        </template>
        <template slot="checkCronForm" slot-scope="scope">
          <div id="checkCronDiv">
            <el-row>
              <el-col :span="6" :offset="9">
                <cron-input v-model="cron" @change="change" @reset="reset"/>
              </el-col>
            </el-row>
          </div>
        </template>
        <template slot="optForm" slot-scope="scope">
          <el-tabs v-model.trim="tabsActiveName" type="card" @tab-click="handleClickTabs" style="width: 1050px">
            <el-tab-pane label="条件设置" name="second">
<!--              <el-button v-if="dataSetParamDtoList.length == 0" type="text" size="small" @click="addRow()">添加</el-button>-->
              <el-table :data="dataSetParamDtoList" border style="width: 100%">
                <el-table-column align="center" label="序号" type="index" width="50"/>
                <el-table-column label="开始" align="center">
                  <template slot-scope="scope">
                    <el-select v-model.trim="dataSetParamDtoList[scope.$index].prefix" clearable placeholder="请选择">
                      <el-option label="开始" value="("></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="连接符号" align="center">
                  <template slot-scope="scope">
                    <el-select v-model="dataSetParamDtoList[scope.$index].joinSymbol" clearable placeholder="请选择">
                      <el-option label="并" value="and"></el-option>
                      <el-option label="或" value="or"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="参数名" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.trim="dataSetParamDtoList[scope.$index].paramName" :disabled="true"/>
                  </template>
                </el-table-column>
                <el-table-column label="描述" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.trim="dataSetParamDtoList[scope.$index].paramDesc" :disabled="true"/>
                  </template>
                </el-table-column>
                <el-table-column label="比较符号" align="center">
                  <template slot-scope="scope">
                    <el-select v-model="dataSetParamDtoList[scope.$index].compareSymbol" clearable placeholder="请选择">
                      <el-option label="大于" value=">"></el-option>
                      <el-option label="大于等于" value=">="></el-option>
                      <el-option label="小于" value="<"></el-option>
                      <el-option label="小于等于" value="<="></el-option>
                      <el-option label="等于" value="="></el-option>
                      <el-option label="不等于" value="!="></el-option>
                      <el-option label="包含" value="like"></el-option>
                      <el-option label="不包含" value="not like"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="参数值" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.trim="dataSetParamDtoList[scope.$index].sampleItem"/>
                  </template>
                </el-table-column>
                <el-table-column label="结束" align="center">
                  <template slot-scope="scope">
                    <el-select v-model="dataSetParamDtoList[scope.$index].suffix" clearable placeholder="请选择">
                      <el-option label="结束" value=")"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
<!--            <el-tab-pane label="字段设置" name="third">
              <div style="max-height: 400px; overflow: auto;">
                <p>说明：上下拖拽行实现字段排序,点击行编辑</p>
                <avue-crud class="fieldConfig" ref="crud1" :data="previewData" :option="previewOption" @sortable-change="sortableChange" @row-click="handleRowClick"></avue-crud>
              </div>
            </el-tab-pane>-->
          </el-tabs>
        </template>
        <template slot="paramForm" slot-scope="scope">
          <span v-text="paramDesc"></span>
<!--          <span>预警类型：${ruleType}，规则名称：${ruleName}，人数：${count}</span>-->
        </template>
<!--        <template slot="typeForm" slot-scope="scope" >-->
<!--          <el-select v-model="form.type" filterable placeholder="请选择" @blur="setTypeBlur">-->
<!--            <el-option-->
<!--                v-for="item in types"-->
<!--                :key="item.TYPE"-->
<!--                :label="item.TYPE"-->
<!--                :value="item.TYPE"-->
<!--            ></el-option>-->
<!--          </el-select>-->
<!--        </template>-->
      </avue-crud>
      <el-dialog width="70%" title="预警记录" :visible.sync="outerVisible" append-to-body>
        <el-dialog
            width="60%"
            title="预警名单"
            :visible.sync="innerVisible"
            append-to-body>
          <avue-crud ref="crud"
                     v-model="form"
                     :table-loading="tableLoading"
                     :option="inoptionRecord"
                     :data="inpagerecordlist"
                     :page.sync="page3"
                     @selection-change="selectionChange"
                     @size-change="sizeChangeChecked"
                     @current-change="currentChangeChecked"
          >
            <template slot-scope="scope" slot="menuLeft">
              <el-button type="primary"
                         icon="el-icon-plus"
                         size="small"
                         @click="listAdd()">添加到特殊名单</el-button>
              <el-button type="success"
                         size="mini"
                         icon="el-icon-download"
                         @click="crudexport">导出
              </el-button>
            </template>
          </avue-crud>
        </el-dialog>
        <avue-crud ref="crud"
                   v-model="form"
                   :table-loading="tableLoading"
                   :option="optionRecord"
                   :data="pagerecordlist"
                   :page.sync="page2"
                   @size-change="sizeChangeRecord"
                   @current-change="currentChangeRecord"
                   >
          <template slot-scope="{row}" slot="menu">
            <el-button type="text"
                       icon="el-icon-tickets"
                       size="mini"
                       @click="checked(row)"
            >名单</el-button>
          </template>
        </avue-crud>
      </el-dialog>
      <el-dialog width="70%" title="预警名单" :visible.sync="listVisible" append-to-body>
        <avue-crud ref="crud"
                   v-model="form"
                   :table-loading="tableLoading"
                   :option="optionrulelist"
                   :data="rulelistdata"
                   :page.sync="pagelist"
                   @search-change="searchChangelist"
                   @row-del="deleteHandlelist"
                   @size-change="sizeChangeList"
                   @current-change="currentChangeList"
        >
          <template slot="menuLeft">
            <el-button type="success"
                       size="mini"
                       icon="el-icon-upload2"
                       @click="handleImport">导入特殊名单
            </el-button>
          </template>

        </avue-crud>
      </el-dialog>
      <el-dialog title="用户数据导入"
                 append-to-body
                 :visible.sync="excelBox"
                 width="600px"
                 :close-on-click-modal="false">
        <avue-form :option="excelOption"
                   v-model="excelForm"
                   :upload-after="uploadAfter">
          <template slot="menuForm">
            <el-button type="primary" @click="handleTemplate()">
              点击下载<i class="el-icon-download el-icon--right"></i>
            </el-button>
          </template>
        </avue-form>
      </el-dialog>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {
  DeleteDataWarningRule,
  EditDataWarningRule,
  execute,
  ListDataWarningRule,
  ListDataWarningRuleType,
  modifyEnableFlag,
  replicateWarningRule
} from "@/api/dataWarningRule";
import {GenExportData, warnInfoqueryPage} from "@/api/dataWarningWarnInfo";
import {recordqueryPage} from "@/api/dataWarningRecord";
import {GetDataWarningSet} from "@/api/dataWarningSet"
import {GetRoleInfo} from "@/api/settings";
import CronInput from 'vue-cron-generator/src/components/cron-input'
import {DEFAULT_CRON_EXPRESSION} from 'vue-cron-generator/src/constant/filed'
import {warnspecialListAdds, warnspecialListdelete, warnspecialListPage} from "@/api/specialList"
import {GetDict, selectDictList} from "@/api/sysDict";

export default {
  inject: ["reload"],
  components: {
    CronInput
  },
  data() {
    return {
      form: {},
        list: [],
      form_record: {},
      cron: DEFAULT_CRON_EXPRESSION,
      tableLoading: false,
      outerVisible: false,
      excelBox: false,
      listVisible: false,
      listruleid: '',
      listhumancode: [],
      excelForm: {},
      rowrecord: ``,
      rowchecked: ``,
      innerVisible: false,
      pageList: [],
      pagerecordlist: [],
      inpagerecordlist: [],
      rulelistdata: [],
      paramDesc:'',
      page: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      page2: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      page3: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      pagelist: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      listquery: {
        ruleid: '',
        humancode: '',
        humanname: ''
      },
      tabsActiveName: 'second',
      isItemFilterType: '', // 选中的转换类型id
      dataSetTransformDtoList: [
        {
          transformType: 'js',
          transformScript: `function dataTransform(data){
                      //自定义脚本内容
                      return data;
                    }`,
        },
      ],
      transformScript: `function dataTransform(data){
              //自定义脚本内容
              return data;
            }`,
      itemFilterScriptId: '',
      types: [],
      cols: [],
      dialogCaseResult: false,
      caseResultTitle: '',
      caseResultContent: null,
      testMassageCode: null,
      dataSetParamDtoList: [],//查询参数table
      inoptionRecord: {
        index: true,
        size: "mini",
        dialogWidth: 1200,
        dialogClickModal: false,
        searchShowBtn: false,
        searchShow: false,
        align: "center",
        // selection: true,
        editBtn: false,
        refreshBtn: false,
        columnBtn: false,
        addBtn: false,
        menu: false,
        delBtn: false,
        header: true,
        border: true,
        menuWidth: 200,
        column: [
          {
            label: "执行记录id",
            prop: "taskRecordId",
            overHidden:true,
            hide: true
          },
          {
            label: "预警规则id",
            prop: "ruleId",
            overHidden:true,
            hide: true
          },
          {
            label: "登录账号",
            prop: "humanCode",
            overHidden:true,
          },
          {
            label: "人员名称",
            prop: "humanName",
            overHidden:true,
          },
          {
            label: "所属机构",
            prop: "organizationNames",
            overHidden:true,
          },
          {
            label: "手机号",
            prop: "telmobile1",
            overHidden:true,
          },
          {
            label: "预警类型",
            prop: "warnType",
            overHidden:true,
          },
          {
            label: "预警名称",
            prop: "warnName",
            overHidden:true,
          },
          {
            label: "预警原因",
            prop: "warnReason",
            overHidden:true,
            row:true,
          },
          {
            label: "预警时间",
            prop: "warnTime",
            overHidden:true,
            row:true,
          },
          {
            label: "预警值",
            prop: "warnValue",
            overHidden:true,
          },
          {
            label: "预警级别",
            prop: "warnLevel",
            overHidden:true,
          },
        ],
      },
      previewData:[],
      previewOption:{
        sortable:true,
        menu:false,
        cellBtn:false,
        columnBtn:false,
        listruleid: '',
        cancelBtn:false,
        refreshBtn:false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        column:[
          {
            label:'列名',
            prop:'field',
          },
          {
            label:'列排序',
            prop:'sortable',
            type: "radio",
            dicData: [
              {
                label: "否",
                value: "否"
              },
              {
                label: "是",
                value: "是"
              }
            ],
            cell:true
          },
        ]
      },
      optionrulelist:{
        sortable:true,
        editBtn: false,
        addBtn: false,
        column:[
          {
            label: "登录账号",
            prop: "humancode",
            search: true,
            // overHidden:true,
            // hide: true
          },
          {
            label: "人员名称",
            prop: "humanname",
            search: true,
            // overHidden:true,
            // hide: true
          },
          {
            label: "所属机构",
            prop: "organizationnames",
            // addDisplay: false,
            // editDisplay: false,
            slot: true,
            align: "left",
            type: "tree",
            dicMethod: "post",
            dicUrl: "/sytSysOrganization/treeListJsonArray",
            // dicData: this.orgList,
            props: {
              value: "id",
            },
            span: 12,
            // search: true,
          },
        ]
      },
      excelOption:{
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "file",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            data:{ruleid: this.listruleid},
            propsHttp: {
              res: "info",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/dataWarning/specialList/import",
          },
        ],
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
      optionRecord(){
        return {
            index: true,
            size: "mini",
            dialogWidth: 1200,
            dialogClickModal: false,
            searchBtn: true,
            searchShow: false,
            align: "center",
            editBtn: false,
            delBtn: false,
            header: false,
            border: true,
            menuWidth: 200,
            column: [
                {
                    label: '分类',
                    prop: "type",
                    type: "select",
                    search: true,
                    filterable:true,
                    cascaderItem:["setCode"],
                    dicData: this.list,
                },
                {
                    label: '规则名称',
                    prop: "rulename",
                    search: true,
                },
                {
                    label: '开始时间',
                    prop: "starttime",
                    search: true,
                },
                {
                    label: '结束时间',
                    prop: "stoptime",
                    search: true,
                },
                {
                    label: '运行状态',
                    prop: "status",
                    align: "center",
                    type: "select",
                    filterable:true,
                    dicData: [
                        {label: "运行失败",value: "0"},
                        {label: "运行成功",value: "1"},
                        {label: "运行中",value: "2"},
                    ],
                },
            ],
        }
      },
      optionDetail(){
        return{
            index: true,
            size: "mini",
            dialogWidth: 1200,
            dialogClickModal: false,
            searchBtn: true,
            searchShow: false,
            align: "center",
            editBtnText: "修改",
            delBtnText: '删除',
            border: true,
            menuWidth: 200,
            column: [
                {
                    sortable:true,
                    label: '规则名称',
                    prop: "setName",
                    search: true,
                    rules: [{required: true, message: "请输入名称", trigger: blur}]
                },
                {
                    label: '预警类型',
                    prop: "ruleType",
                    type: "select",
                    search: true,
                    filterable:true,
                    cascaderItem:["setCode"],
                    dicData: this.list,
                    // allowCreate: true,
                    // formslot: true,
                    // dicMethod: "post",
                    // dicUrl: '/dataWarning/rule/list',
                    // props: {
                    //   label: "name",
                    //   value: "id",
                    // },
                    rules: [{required: true, message: "请选择分类", trigger: blur}],
                },
                {
                    label: '规则描述',
                    prop: "setDesc",
                    type: "textarea",
                    span: 24,
                },
                {
                    label: '检查定时',
                    prop: "checkCron",
                    formslot: true,
                    span: 24,
                    // rules: [{required: true, message: "请输入检查定时", trigger: blur}]
                },
                {
                    label: '即时推送',
                    prop: "pushNow",
                    align: "center",
                    type: "select",
                    filterable:true,
                    dicData: [
                        {label: "是",value: "是"},
                        {label: "否",value: "否"},
                    ],
                    rules: [{required: true, message: "请选择是否及时推送", trigger: blur}]
                },
                {
                    label: '推送渠道',
                    prop: "pushChannel",
                    align: "center",
                    type: "select",
                    filterable:true,
                    dicData: [
                        {label: "微信",value: "微信"},
                        {label: "短信",value: "短信"},
                    ],
                    rules: [{required: true, message: "请选择是否即时推送", trigger: blur}]
                },
                {
                    label: "推送角色",
                    prop: "roles",
                    type: "select",
                    placeholder: "请选择 推送角色-可多选",
                    drag: true,
                    multiple: true,
                    dicData: [],
                    props: {
                        label: "rolename",
                        value: "id"
                    }
                    // rules: [{ required: true, message: "授权角色不能为空" }]
                },
                {
                    label: "指定部门",
                    prop: "organizations",
                    type: "tree",
                    multiple: true,
                    dicMethod:"post",
                    checkStrictly:true,
                    dicUrl:"/sytSysOrganization/treeListJsonArray",
                    // dicData: [],
                    props: {
                        label: "orgname",
                        value: "id",
                    },
                    span: 12,
                    // addDisplay: false,
                    // editDisplay: false,
                    // hide: true,

                },
                {
                    label: "推送范围",
                    prop: "scope",
                    type: "select",
                    // multiple: true,
                    hide: true,
                    dicData: [
                        {label: "本级",value: "本级"},
                        {label: "下级",value: "下级"},
                    ],
                    span: 12,
                },
                {
                    label: "保存名单",
                    prop: "isSaveList",
                    type: "select",
                    hide: true,
                    dicData: [
                        {label: "是",value: "是"},
                        {label: "否",value: "否"},
                    ],
                    span: 12,
                },
                {
                    label: "消息参数",
                    prop: "paramer",
                    type: "select",
                    dicMethod: "post",
                    dicUrl: '/sytSysDict/list?code=WARNING_MSG_PARAM',
                    props: {
                        label: "name",
                        value: "value",
                    },
                    hide: true,
                    /*dicData: [
                      {label: "是",value: "yes"},
                      {label: "否",value: "no"},
                    ],*/
                    span: 12,
                    change: ({value}) => {
                        if (value && value !== "") {
                            this.form.content = `${value}`
                        }
                    }
                },
                {
                    label: '状态',
                    prop: "enableFlag",
                    align: "center",
                    type: "select",
                    addDisplay: false,
                    editDisplay: false,
                    filterable: true,
                    dicData: [
                        {value: 0,label: "启用"},
                        {value: 1,label: "停用"},
                    ],
                    // rules: [{required: true, message: "请选择状态", trigger: blur}]
                },
                {
                    label: '参数说明',
                    prop: "param",
                    span: 24,
                    formslot: true,
                    hide: true,
                },
                {
                    label: '消息模板',
                    prop: "content",
                    type: "textarea",
                    span: 24,
                },
                {
                    label: '操作人',
                    prop: "createUser",
                    addDisplay: false,
                    editDisplay: false,
                },
                {
                    label: '操作时间',
                    prop: "createTime",
                    addDisplay: false,
                    editDisplay: false,
                    type: "date",
                    format: "yyyy-MM-dd hh:mm:ss",
                },
                {
                    label: '预警数据集',
                    prop: "setCode",
                    search: true,
                    hide: true,
                    type: "select",
                    hiden: true,
                    dicMethod: "post",
                    dicUrl: '/dataWarning/dataSet/getAll?setType={{key}}',
                    props: {
                        label: "setName",
                        value: "id",
                    },
                    span: 24,
                    change: ({value}) => {
                        if (value && value !== "") {
                            this.getDataWarningSet({id: value});
                        }
                    }
                },

                {
                    label: '操作',
                    prop: "opt",
                    formslot: true,
                    hide: true,
                },
            ],
            group: [
                {
                    prop: "group1",
                    column: [],
                }
            ]
        }
      }
  },
  created() {
      selectDictList({code: "WARNING_TYPE"}).then(res => {
          let data = res.data.info
          data.forEach(item => {
              if(item.value !== '全部'){
                  this.list.push({label: item.value,value: item.value})
              }

          })
      });
    console.log(`this.userInfo`, this.userInfo);
    if (this.userInfo.roletype == '2' || this.userInfo.roletype == '3') {
      let organizations = this.findObject(this.optionDetail.column, 'organizations');
      organizations.addDisplay = false;
      organizations.editDisplay = false;
    }
    this.onLoad(this.pageParam());
  },
  mounted() {
    this.getRoleInfo();
    this.getParamDesc();
  },
  watch:{
  },
  methods: {
    getParamDesc(){
      GetDict({idOrNameOrCode:"WARNING_MSG_PARAM_DESC"}).then(res=>{
        this.paramDesc = res.data.info.value;
      });
    },
    sortChange(val){
      this.$message.success('查看控制台');
    },
    change(cron) {
      this.cron = cron
    },
    reset(cron) {
      this.cron = DEFAULT_CRON_EXPRESSION
    },
    getRoleInfo() {
      GetRoleInfo().then(res => {
        // this.rolesList = res.data.info;
        var column = this.findObject(this.optionDetail.column, 'roles');
        column.dicData = res.data.info;
      });
    },
    getDataWarningSet(param){
      GetDataWarningSet(param).then(res=>{
        console.log(`this.form`, this.form);
        if (res.data.info.dataSetParamDtoList && res.data.info.dataSetParamDtoList.length > 0) {
          if (!this.form.hasOwnProperty("id")) {
            res.data.info.dataSetParamDtoList.forEach(dto => {
              dto.sampleItem = '';
            });
            console.log(`dataSetParamDtoList`,this.dataSetParamDtoList)
            this.dataSetParamDtoList = res.data.info.dataSetParamDtoList;
          } else {
            let paramlist = res.data.info.dataSetParamDtoList;
            let oldlist = this.form.dataSetParamDtoList;
            paramlist.forEach((item,index) =>{
              oldlist.forEach((e,index2)=>{
                if(item.paramName == e.paramName){
                  paramlist[index] = e
                }
              })
            })

            this.dataSetParamDtoList = paramlist
          }
        }
        var organizations = this.findObject(this.optionDetail.column, "organizations");
        if (res.data.info.orgQuery == '是') {
          organizations.addDisplay = true;
          organizations.editDisplay = true;
          organizations.hide = false;
        } else {
          organizations.addDisplay = false;
          organizations.editDisplay = false;
          organizations.hide = true;
        }
      });
    },
    sortableChange(oldindex, newindex, row, list) {
      this.previewData=[]
      this.$nextTick(()=>{
        this.previewData=list;
      })
    },
    handleRowClick(row) {
      row.$cellEdit=true
    },
    setTypeBlur(e){
      this.$set(this.form,"setType",e.target.value)
    },
    isShowCaseResult(item) {
      this.dialogCaseResult = true
      this.caseResultTitle = item.setName
      this.caseResultContent = JSON.parse(item.caseResult)
    },
    handleClickTabs(tab, event) {
      // this.$refs['crud'].validate((valid) => {
      //   if (valid) {
          if (tab.paneName === 'first') {
            const params = {
              sourceCode: this.form.sourceCode,
              dynSentence: this.form.dynSentence,
              dataSetParamDtoList: this.dataSetParamDtoList,
              dataSetDrillParamDtoList: this.dataSetDrillParamDtoList,
              dataSetTransformDtoList: this.dataSetTransformDtoList,
            }
            if (params.dynSentence && params.dynSentence !== '') {
              testTransformSet(params).then(res => {
                if (res.data.code === '00000') {
                  this.$message({type: "success", message: "操作成功"});
                  // this.cols = res.data.info.data;
                  var arr = [];
                  if (res.data.info.data.length > 5) {
                    arr = res.data.info.data.slice(0,5);
                  } else {
                    arr = res.data.info.data;
                  }
                  this.cols = arr;
                  this.testMassageCode = res.data.code;
                }
              }).catch(res => {
                this.cols = [];
                this.$message.error(res.info);
              });
            } else {
              this.$message.error("请输入查询SQL或请求体");
            }
          }else if (tab.paneName === 'second') {
            if (this.cols.length > 0) {
              let previewArr = [];
              for (let arrKey in this.cols[0]) {
                let ob = {};
                ob.field = arrKey;
                ob.$cellEdit = false;
                previewArr.push(ob);
              }
              if (!this.form.fieldConfig || this.form.fieldConfig.length == 0 || this.form.fieldConfig === '[]') {
                this.previewData = previewArr;
              }
            }
          }
        // }
      // })
    },
    //预警记录
    record(row) {
      this.outerVisible = true;
      this.rowrecord = row;
      this.listruleid = row.id;
      let param = this.pageParamRecord(row);
      recordqueryPage(param).then(res =>{
        this.tableLoading = false
        const data = res.data.info;
        this.page2.currentPage = data.current;
        this.page2.total = data.total;
        this.page2.pageSize = data.size;
        this.page2.pageSize = data.size;
        for ( let i = 0 ; i < data.records.length; i++ ) {
          data.records[i].starttime = data.records[i].starttime.replace("T","-")
          data.records[i].starttime = data.records[i].starttime.replace("+0000","")
          data.records[i].stoptime = data.records[i].stoptime.replace("T","-")
          data.records[i].stoptime = data.records[i].stoptime.replace("+0000","")
        }
        this.pagerecordlist = data.records;
      })
    },
    //名单
    checked(row) {
      this.innerVisible = true;
      this.rowchecked = row;
      let param = this.pageParamChecked(row);
      warnInfoqueryPage(param).then(res =>{
        this.tableLoading = false
        const data = res.data.info;
        this.page3.currentPage = data.current;
        this.page3.total = data.total;
        this.page3.pageSize = data.size;
        this.page3.pageSize = data.size;
        for ( let i = 0 ; i < data.records.length; i++ ) {
          data.records[i].warnTime = data.records[i].warnTime.replace("T","-")
          data.records[i].warnTime = data.records[i].warnTime.replace("+0000","")
        }
        this.inpagerecordlist = data.records;
      })
    },
    //获取名单
    warnrulelist(row){
      this.listVisible = true;
      this.listruleid = row.id;
      this.warnrulelistget(row)
    },
    warnrulelistget(){
      this.listquery.ruleid = this.listruleid;
      let param = this.pageParamList();
      warnspecialListPage(param).then(res =>{
        this.rulelistdata = res.data.info.records
      })
    },
    //删除名单
    deleteHandlelist(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        warnspecialListdelete({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.warnrulelistget()
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    //搜索名单
    searchChangelist(param, done) {
      this.listquery.humancode = param.humancode;
      this.listquery.humanname = param.humanname;
      this.warnrulelistget()
      done()
    },
    //导入名单
    handleImport(){
      this.excelBox = true;
    },
    uploadAfter(res, done, loading) {
      if (res === "success") {
        this.excelBox = false;
        this.refresh();
        done();
      } else if (res === undefined) {
        this.$message.error("上传内容格式有误！");
        loading();
      } else {
        this.$message.warning("请上传 .xls,.xlsx 标准格式文件");
        loading();
      }
    },
    handleTemplate() {
      window.open(`/sytPermissionAccount/downImportTemplate`);
    },
    isEnableFlag(row) {
      let parm = {};
      parm.id = row.id;
      parm.enableFlag = 0;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "已启用"});
        }
        this.onLoad(this.pageParam())
      });
    },
    offEnableFlag(row) {
      let parm = {};
      parm.id = row.id;
      parm.enableFlag = 1;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({ message: "已停用"});
        }
        this.onLoad(this.pageParam())
      });
    },
    allisEnableFlag() {
      let parm = {};
      parm.enableFlag = 0;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "已全部启用"});
        }
        this.onLoad(this.pageParam())
      });
    },
    copy(row){
      console.log(`copy`,row)
      this.$confirm("此操作将复制此规则, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        // type: "warning"
      }).then(() => {
        replicateWarningRule({"ruleid": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "复制成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消复制')
      });
    },
    implement(row) {
      let parm = {};
      parm.id = row.id;
      execute(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "执行成功"});
        }
        this.onLoad(this.pageParam())
      });
    },
    alloffEnableFlag() {
      let parm = {};
      parm.enableFlag = 1;
      modifyEnableFlag(parm).then(res =>{
        if (res.data.code === '00000') {
          this.$message({ message: "已全部停用"});
        }
        this.onLoad(this.pageParam())
      });
    },
    async beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
        this.testMassageCode = '00000';
        if (this.form.caseResult) {
          this.cols = JSON.parse(this.form.caseResult);
        } else {
          this.cols = [];
        }
        if (this.form.fieldConfig) {
          this.previewData = JSON.parse(this.form.fieldConfig);
        } else {
          this.previewData = [];
        }
        if (this.form.checkCron) {
          this.cron = this.form.checkCron;
        }
        this.dataSetTransformDtoList = this.form.dataSetTransformDtoList;
        let param = {
          id: this.form.setCode
        }
        await GetDataWarningSet(param).then(res=>{
          if (res.data.info.dataSetParamDtoList && res.data.info.dataSetParamDtoList.length > 0) {
            res.data.info.dataSetParamDtoList.forEach(dto=>{
              dto.sampleItem = '';
            });
            this.dataSetParamDtoList = []
            let listnew = res.data.info.dataSetParamDtoList;
            let listold = this.form.dataSetParamDtoList;
            if(listnew.length > listold.length){
              for(let i =0; i<listnew.length;i++){
                if(listold[i]){
                  if(listold[i].paramName == listnew[i].paramName){
                    this.dataSetParamDtoList.push(listold[i])
                  }else{
                    this.dataSetParamDtoList.push(listnew[i])
                  }
                }else {
                  this.dataSetParamDtoList.push(listnew[i])
                }
              }
            }
            if(listnew.length == listold.length){
              for(let i =0; i<listnew.length;i++){
                if(listold[i]){
                  if(listold[i].paramName == listnew[i].paramName){
                    this.dataSetParamDtoList.push(listold[i])
                  }else{
                    this.dataSetParamDtoList.push(listnew[i])
                  }
                }
              }
            }
            if(listnew.length < listold.length){
              for(let i =0; i<listnew.length;i++){
                if(listold[i]){
                  if(listold[i].paramName == listnew[i].paramName){
                    this.dataSetParamDtoList.push(listold[i])
                  }else{
                    this.dataSetParamDtoList.push(listnew[i])
                  }
                }
              }
            }
            console.log(`dataSetParamDtoList`,this.dataSetParamDtoList)
          }
        });
        this.$nextTick()
        // this.dataSetParamDtoList = this.form.dataSetParamDtoList;
        this.dataSetDrillParamDtoList = this.form.dataSetDrillParamDtoList;
      } else {
        this.cols = [];
        this.previewData = [];
        this.caseResultContent = [];
        this.dataSetTransformDtoList = [];
        this.dataSetParamDtoList = [];
        this.dataSetDrillParamDtoList = [];
      }
      done();
    },
    saveHandle(row, done, loading) {
      this.editHandle(row, done, loading);
    },
    // 编辑
    editHandle(row, done, loading) {
      var roles = row.roles;
      if (typeof roles ==='object') {
        row.roles = roles.join(',');
      }
      var organizations = row.organizations;
      if (organizations && typeof organizations === 'object') {
        row.organizations = organizations.join(',');
      }
      row.checkCron = this.cron;
      row.dataSetTransformDtoList = this.dataSetTransformDtoList;
      row.dataSetParamDtoList = this.dataSetParamDtoList;
      row.dataSetDrillParamDtoList = this.dataSetDrillParamDtoList;
      row.caseResult = JSON.stringify(this.cols);
      row.fieldConfig = JSON.stringify(this.previewData);
      row.enableFlag = 1;
      EditDataWarningRule(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
        done();
      }).catch(res => {
        console.log(res.info)
        // this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteDataWarningRule({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      ListDataWarningRule(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
      ListDataWarningRuleType().then(res=>{
        this.types = res.data.info;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    sizeChangeRecord(val) {
      this.page2.currentPage = 1;
      this.page2.pageSize = val;
      this.record(this.rowrecord)
    },
    selectionChange(list){
      this.listhumancode = list;
    },
    listAdd(){
      if (this.listhumancode > 0) {
        let list = {
          warnInfoJson: JSON.stringify(this.listhumancode)
        };
        warnspecialListAdds(list).then(res =>{
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "新增成功"});
          }
        })
      } else {
        this.$message({type: "warning", message: "请选择数据"});
      }

    },
    sizeChangeChecked(val) {
      this.page3.currentPage = 1;
      this.page3.pageSize = val;
      this.checked(this.rowchecked)
    },
    sizeChangeList(val) {
      this.pagelist.currentPage = 1;
      this.pagelist.pageSize = val;
      // this.checked(this.rowchecked)
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    currentChangeRecord(val) {
      this.page2.currentPage = val;
      this.record(this.rowrecord)
    },
    currentChangeChecked(val) {
      this.page3.currentPage = val;
      this.checked(this.rowchecked)
    },
    currentChangeList(val) {
      this.pagelist.currentPage = val;
      // this.record(this.rowrecord)
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    pageParamRecord(row) {
      return {
        page: this.page2.currentPage,
        pageSize: this.page2.pageSize,
        queryParam: {
          rulecode: row.id
        }
      }
    },
    pageParamChecked(row) {
      return {
        page: this.page3.currentPage,
        pageSize: this.page3.pageSize,
        queryParam: {
          taskRecordId: row.id
        }
      }
    },
    pageParamList(row) {
      return {
        page: this.pagelist.currentPage,
        pageSize: this.pagelist.pageSize,
        queryParam: this.listquery,
        // queryParam: {
        //   ruleid: this.listruleid,
        //   humancode: row.humancode,
        //   humanname: row.humanname
        // }
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
    // 必选
    Mandatory(val) {
      if (!this.dataSetParamDtoList[val].mandatory) {
        this.dataSetParamDtoList[val].requiredFlag = 0
      } else {
        this.dataSetParamDtoList[val].requiredFlag = 1
      }
    },
    permissionClick(row, index) {
      this.title = '自定义高级规则'
      if (this.isRowData.sampleItem != '') {
        this.isRowData = row
        const fnCont = `function verification(data){
          //自定义脚本内容
          return true;
        }`
        this.validationRules = row.validationRules
            ? row.validationRules
            : fnCont
        this.dialogPermissionVisible = true
      }
    },
    // 删除
    cutOutRow(index, rows) {
      rows.splice(index, 1);
    },
    // 追加
    addRow(index, row) {
      this.dataSetParamDtoList.push({
        paramName: "",
        paramDesc: "",
        paramType: "",
        sampleItem: "",
        mandatory: true,
        requiredFlag: 1,
        validationRules: `function verification(data){\n\t//自定义脚本内容\n\treturn true;\n}`
      });
    },
    // 删除
    cutOutDrillRow(index, rows) {
      rows.splice(index, 1);
    },
    // 追加
    addDrillRow(index, row) {
      this.dataSetDrillParamDtoList.push({
        paramName: "",
        paramDesc: "",
        paramType: "",
        sampleItem: "",
        mandatory: true,
        requiredFlag: 1,
        validationRules: `function verification(data){\n\t//自定义脚本内容\n\treturn true;\n}`
      });
    },
    crudexport() {
      let param = {
        taskRecordId: this.rowchecked.id,
      };
      GenExportData(param).then((res) => {
        if (res.data.code == "00000") {
          window.open(`/dataWarning/warnInfo/export`);
        }
      });
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}
::v-deep .fieldConfig .avue-crud__menu {
  display: none !important;
}
.avue-view {
  height: 100%;
}
.el-col-offset-9 {
  margin-left: 0 !important;
  width: 100%;
}
::v-deep .el-table__cell > .cell > .el-button {
  padding: 0;
}
</style>
<style lang="less">
#checkCronDiv {
}
</style>
