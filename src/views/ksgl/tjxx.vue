<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.tjxx_delete"
                   @click="handleDelete">删 除
        </el-button>
<!--        <el-button type="success"
                   size="mini"
                   icon="el-icon-upload2"
                   @click="handleImport">导入
        </el-button>-->
      </template>
    </avue-crud>

    <el-dialog title="数据导入"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
      <avue-form :option="excelOption"
                 v-model="excelForm"
                 :upload-after="uploadAfter">
        <template slot="menuForm">
          <el-button type="primary" @click="handleTemplate()">
            点击下载模板<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/ksgl/tjxx";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        excelBox: false,
        excelForm: {},
        excelOption: {
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "文件上传",
              prop: "file",
              type: "upload",
              drag: true,
              loadText: "文件上传中，请稍等",
              span: 24,
              propsHttp: {
                res: "info",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/ksgl/ksjl/import",
            },
          ],
        },
        selectionList: [],
        option: {
          height: 'auto',
        //  calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "年份",
              prop: "nf",
              search:true,
              type: 'select',
              props: {
                label: 'nf',
                value: 'nf'
              },
              dicUrl: '/ksgl/tdd/allnf',
              rules: [{
                required: true,
                message: "请输入年份",
                trigger: "blur"
              }]
            },
            {
              label: "考生号",
              prop: "ksh",
              search:true,
              rules: [{
                required: true,
                message: "请输入考生号",
                trigger: "blur"
              }]
            },
            {
              label: "体检序号",
              prop: "tjxh",
              rules: [{
                required: true,
                message: "请输入体检序号",
                trigger: "blur"
              }]
            },
            {
              label: "既往病史标志(1-无，0-有)",
              prop: "jwbsbz",
              rules: [{
                required: true,
                message: "请输入既往病史标志(1-无，0-有)",
                trigger: "blur"
              }]
            },
            {
              label: "既往病史",
              prop: "jwbs",
              rules: [{
                required: true,
                message: "请输入既往病史",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.裸眼视力(右)",
              prop: "ykLysly",
              rules: [{
                required: true,
                message: "请输入眼科.裸眼视力(右)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.裸眼视力(左)",
              prop: "ykLyslz",
              rules: [{
                required: true,
                message: "请输入眼科.裸眼视力(左)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.矫正视力(右)",
              prop: "ykJzsly",
              rules: [{
                required: true,
                message: "请输入眼科.矫正视力(右)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.矫正视力(左)",
              prop: "ykJzslz",
              rules: [{
                required: true,
                message: "请输入眼科.矫正视力(左)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.矫正度数(右)",
              prop: "ykJzdsy",
              rules: [{
                required: true,
                message: "请输入眼科.矫正度数(右)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.矫正度数(左)",
              prop: "ykJzdsz",
              rules: [{
                required: true,
                message: "请输入眼科.矫正度数(左)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.彩色图案及数码检查(1-正常,2-其他)",
              prop: "ykSjjc ",
              rules: [{
                required: true,
                message: "请输入眼科.彩色图案及数码检查(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.色觉检查图名称(1-喻自萍,2-其他)",
              prop: "ykSjjct",
              rules: [{
                required: true,
                message: "请输入眼科.色觉检查图名称(1-喻自萍,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.单色识别能力（红）（1-能识别，0-不能识别）",
              prop: "ykSjds1",
              rules: [{
                required: true,
                message: "请输入眼科.单色识别能力（红）（1-能识别，0-不能识别）",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.单色识别能力（黄）（1-能识别，0-不能识别）",
              prop: "ykSjds2",
              rules: [{
                required: true,
                message: "请输入眼科.单色识别能力（黄）（1-能识别，0-不能识别）",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.单色识别能力（绿）（1-能识别，0-不能识别）",
              prop: "ykSjds3",
              rules: [{
                required: true,
                message: "请输入眼科.单色识别能力（绿）（1-能识别，0-不能识别）",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.单色识别能力（蓝）（1-能识别，0-不能识别）",
              prop: "ykSjds4",
              rules: [{
                required: true,
                message: "请输入眼科.单色识别能力（蓝）（1-能识别，0-不能识别）",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.单色识别能力（紫）（1-能识别，0-不能识别）",
              prop: "ykSjds5",
              rules: [{
                required: true,
                message: "请输入眼科.单色识别能力（紫）（1-能识别，0-不能识别）",
                trigger: "blur"
              }]
            },
            {
              label: "眼科医师意见(1-合格，2-专业受限，3-不合格）",
              prop: "ykYsyj",
              rules: [{
                required: true,
                message: "请输入眼科医师意见(1-合格，2-专业受限，3-不合格）",
                trigger: "blur"
              }]
            },
            {
              label: "眼科.眼病",
              prop: "ykYb",
              rules: [{
                required: true,
                message: "请输入眼科.眼病",
                trigger: "blur"
              }]
            },
            {
              label: "内科.血压收缩压（高压）(单位:kpa)",
              prop: "nkXyssy",
              rules: [{
                required: true,
                message: "请输入内科.血压收缩压（高压）(单位:kpa)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.血压舒张压（低压）(单位:kpa)",
              prop: "nkXyszy",
              rules: [{
                required: true,
                message: "请输入内科.血压舒张压（低压）(单位:kpa)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.发育情况(1-良,2-中,3-差)",
              prop: "nkFyqk",
              rules: [{
                required: true,
                message: "请输入内科.发育情况(1-良,2-中,3-差)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.心脏及血管(1-正常,2-其他)",
              prop: "nkXzjxg",
              rules: [{
                required: true,
                message: "请输入内科.心脏及血管(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.呼吸系统(1-正常,2-其他)",
              prop: "nkHxxt",
              rules: [{
                required: true,
                message: "请输入内科.呼吸系统(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.神经系统(1-正常,2-其他)",
              prop: "nkSjxt",
              rules: [{
                required: true,
                message: "请输入内科.神经系统(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.肝(单位:厘米)",
              prop: "nkG",
              rules: [{
                required: true,
                message: "请输入内科.肝(单位:厘米)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.肝性质(1-正常,2-其他)",
              prop: "nkGxz",
              rules: [{
                required: true,
                message: "请输入内科.肝性质(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.脾(单位:厘米)",
              prop: "nkP",
              rules: [{
                required: true,
                message: "请输入内科.脾(单位:厘米)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.脾性质(1-正常,2-其他)",
              prop: "nkPxz",
              rules: [{
                required: true,
                message: "请输入内科.脾性质(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "内科医师意见(1-合格，2-专业受限，3-不合格)",
              prop: "nkYsyj",
              rules: [{
                required: true,
                message: "请输入内科医师意见(1-合格，2-专业受限，3-不合格)",
                trigger: "blur"
              }]
            },
            {
              label: "内科.其他",
              prop: "nkQt",
              rules: [{
                required: true,
                message: "请输入内科.其他",
                trigger: "blur"
              }]
            },
            {
              label: "外科.身高(单位:厘米)",
              prop: "wkSg",
              rules: [{
                required: true,
                message: "请输入外科.身高(单位:厘米)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.体重(单位:千克)",
              prop: "wkTz",
              rules: [{
                required: true,
                message: "请输入外科.体重(单位:千克)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.皮肤(1-正常,2-其他)",
              prop: "wkPf",
              rules: [{
                required: true,
                message: "请输入外科.皮肤(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.面部(1-正常,2-其他)",
              prop: "wkMb",
              rules: [{
                required: true,
                message: "请输入外科.面部(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.颈部",
              prop: "wkJb",
              rules: [{
                required: true,
                message: "请输入外科.颈部",
                trigger: "blur"
              }]
            },
            {
              label: "外科.脊柱(1-正常,2-其他)",
              prop: "wkJz",
              rules: [{
                required: true,
                message: "请输入外科.脊柱(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.四肢(1-正常,2-其他)",
              prop: "wkSz",
              rules: [{
                required: true,
                message: "请输入外科.四肢(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.关节(1-正常,2-其他)",
              prop: "wkGj",
              rules: [{
                required: true,
                message: "请输入外科.关节(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "外科医师意见(1-合格，2-专业受限，3-不合格)",
              prop: "wkYsyj",
              rules: [{
                required: true,
                message: "请输入外科医师意见(1-合格，2-专业受限，3-不合格)",
                trigger: "blur"
              }]
            },
            {
              label: "外科.其他",
              prop: "wkQt",
              rules: [{
                required: true,
                message: "请输入外科.其他",
                trigger: "blur"
              }]
            },
            {
              label: "耳鼻喉科.左耳听力(单位:米)",
              prop: "ebZetl",
              rules: [{
                required: true,
                message: "请输入耳鼻喉科.左耳听力(单位:米)",
                trigger: "blur"
              }]
            },
            {
              label: "耳鼻喉科.右耳听力(单位:米)",
              prop: "ebYetl",
              rules: [{
                required: true,
                message: "请输入耳鼻喉科.右耳听力(单位:米)",
                trigger: "blur"
              }]
            },
            {
              label: "耳鼻喉科.嗅觉(1-正常,0-迟钝)",
              prop: "ebXj",
              rules: [{
                required: true,
                message: "请输入耳鼻喉科.嗅觉(1-正常,0-迟钝)",
                trigger: "blur"
              }]
            },
            {
              label: "耳鼻喉科医师意见(1-合格，2-专业受限，3-不合格)",
              prop: "ebYsyj",
              rules: [{
                required: true,
                message: "请输入耳鼻喉科医师意见(1-合格，2-专业受限，3-不合格)",
                trigger: "blur"
              }]
            },
            {
              label: "耳鼻喉科.耳鼻咽喉",
              prop: "ebEbyh",
              rules: [{
                required: true,
                message: "请输入耳鼻喉科.耳鼻咽喉",
                trigger: "blur"
              }]
            },
            {
              label: "口腔科.唇腭(1-正常,2-其他)",
              prop: "kqCe",
              rules: [{
                required: true,
                message: "请输入口腔科.唇腭(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "口腔科.是否口吃(1-否,0-是)",
              prop: "kqSfkc",
              rules: [{
                required: true,
                message: "请输入口腔科.是否口吃(1-否,0-是)",
                trigger: "blur"
              }]
            },
            {
              label: "口腔科.牙齿(1-正常,2-其他)",
              prop: "kqYc",
              rules: [{
                required: true,
                message: "请输入口腔科.牙齿(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "口腔科医师意见(1-合格，2-专业受限，3-不合格）",
              prop: "kqYsyj",
              rules: [{
                required: true,
                message: "请输入口腔科医师意见(1-合格，2-专业受限，3-不合格）",
                trigger: "blur"
              }]
            },
            {
              label: "口腔科.其他",
              prop: "kqQt",
              rules: [{
                required: true,
                message: "请输入口腔科.其他",
                trigger: "blur"
              }]
            },
            {
              label: "胸透(1-正常,2-其他)",
              prop: "xt",
              rules: [{
                required: true,
                message: "请输入胸透(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "胸透医师意见(1-合格，2-专业受限，3-不合格）",
              prop: "xtYsyj",
              rules: [{
                required: true,
                message: "请输入胸透医师意见(1-合格，2-专业受限，3-不合格）",
                trigger: "blur"
              }]
            },
            {
              label: "胸透.其他",
              prop: "xtQt",
              rules: [{
                required: true,
                message: "请输入胸透.其他",
                trigger: "blur"
              }]
            },
            {
              label: "肝功能.转氨酶(1-正常,2-其他)",
              prop: "ggZam",
              rules: [{
                required: true,
                message: "请输入肝功能.转氨酶(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "肝功能.乙肝表面抗原(1-正常,2-其他)",
              prop: "ggYgky",
              rules: [{
                required: true,
                message: "请输入肝功能.乙肝表面抗原(1-正常,2-其他)",
                trigger: "blur"
              }]
            },
            {
              label: "肝功医师意见(1-合格，2-专业受限，3-不合格）",
              prop: "ggYsyj",
              rules: [{
                required: true,
                message: "请输入肝功医师意见(1-合格，2-专业受限，3-不合格）",
                trigger: "blur"
              }]
            },
            {
              label: "肝功能.其他",
              prop: "ggQt",
              rules: [{
                required: true,
                message: "请输入肝功能.其他",
                trigger: "blur"
              }]
            },
            {
              label: "体检专业受限代码1",
              prop: "zysxdm1",
              rules: [{
                required: true,
                message: "请输入体检专业受限代码1",
                trigger: "blur"
              }]
            },
            {
              label: "体检专业受限代码2",
              prop: "zysxdm2",
              rules: [{
                required: true,
                message: "请输入体检专业受限代码2",
                trigger: "blur"
              }]
            },
            {
              label: "体检专业受限代码3",
              prop: "zysxdm3",
              rules: [{
                required: true,
                message: "请输入体检专业受限代码3",
                trigger: "blur"
              }]
            },
            {
              label: "体检专业受限代码4",
              prop: "zysxdm4",
              rules: [{
                required: true,
                message: "请输入体检专业受限代码4",
                trigger: "blur"
              }]
            },
            {
              label: "体检专业受限代码5",
              prop: "zysxdm5",
              rules: [{
                required: true,
                message: "请输入体检专业受限代码5",
                trigger: "blur"
              }]
            },
            {
              label: "体检专业受限代码6",
              prop: "zysxdm6",
              rules: [{
                required: true,
                message: "请输入体检专业受限代码6",
                trigger: "blur"
              }]
            },
            {
              label: "体检结论代码(1-合格，2-专业受限，3-不合格)",
              prop: "tjjldm",
              rules: [{
                required: true,
                message: "请输入体检结论代码(1-合格，2-专业受限，3-不合格)",
                trigger: "blur"
              }]
            },
            {
              label: "备注",
              prop: "bz",
              span: 24,
              type: "textarea",
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.tjxx_add, false),
          viewBtn: this.vaildData(this.permission.tjxx_view, false),
          delBtn: this.vaildData(this.permission.tjxx_delete, false),
          editBtn: this.vaildData(this.permission.tjxx_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      // 导入
      handleImport() {
        this.excelBox = true;
      },

      uploadAfter(res, done, loading) {
        if (res === "success") {
          this.excelBox = false;
          this.refresh();
          done();
        } else if (res === undefined) {
          this.$message.error("上传内容格式有误！");
          loading();
        } else {
          this.$message.warning("请上传 .xls,.xlsx 标准格式文件");
          loading();
        }
      },

      handleTemplate() {
        window.open(`/ksgl/tjxx/downImportTemplate`);
      },
    }
  };
</script>

<style>
</style>
