<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refresh"
    >
      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   icon="el-icon-edit"
                   plain
                   v-if="permission.tdd_delete"
                   @click="handleRevisedsed">批量修正
        </el-button>
        <el-button type="primary"
                   size="small"
                   icon="el-icon-edit"
                   plain
                   v-if="permission.tdd_delete"
                   @click="handleOneKeyRevised">一键修正
        </el-button>
        <el-button type="primary"
                   size="small"
                   icon="el-icon-edit"
                   plain
                   v-if="permission.tdd_delete"
                   @click="handleOneKeyRestore">一键还原
        </el-button>
      </template>
      <template v-for="z in slotname" :slot="z" slot-scope="scope" >
        <div class="slotcontent" v-for="(e,a) in scope.row[z]" :key="a" >
          <div class="slotbody" v-for="(item,index) in e" :key="index">
            <div style="border-bottom: 1px solid #EBEEF5;background-color: #fafafa;padding: 5px">
              <p style="width: 120px">{{ item.options.label }}</p>
            </div>
            <div style="padding: 5px">
              <p style="width: 120px">{{item.values}}</p>
            </div>
          </div>
        </div>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button icon="el-icon-magic-stick" :size="scope.size" type="text" @click.stop="revised(scope.row)">修正</el-button>
      </template>
    </avue-crud>
    <el-dialog
        title="修正"
        :visible.sync="dialogVisible"
        append-to-body
        width="30%"
        :before-close="handleClose">
      <avue-form v-model="xzform" :option="xzoption"></avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="torevised()">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, getDetail, oneKeyRestore, oneKeyRevised, remove, revised, selecRrevisedPage, update} from "@/api/ksgl/tdd";
import {mapGetters} from "vuex";

export default {
  components:{
  },
  data() {
    return {
      form: {},
      xzform: {},
      query: {},
      xylist: [],
      zymclist: [],
      xzrowid: null,
      xzrow: null,
      loading: true,
      dialogVisible: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      timer:'',
      slotname:[],
      selectionList: [],
      data: [],
    };
  },
  created() {
    this.onLoad(this.page);
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tdd_add, false),
        viewBtn: this.vaildData(this.permission.tdd_view, false),
        delBtn: this.vaildData(this.permission.tdd_delete, false),
        editBtn: this.vaildData(this.permission.tdd_edit, false)
      };
    },
    /*ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },*/
    option(){
      return {
        height: 'auto',
        calcHeight: 210,
        searchShow: true,
        // searchShowBtn: false,
        searchMenuSpan: 4,
        tip: false,
        border: true,
        labelWidth: 150,
        menuWidth: 120,
        menuFixed:"right",
        indexFixed: false,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        filterBtn: false,
        // refreshBtn: false,
        columnBtn: true,
        selection: true,
        column:[
          {
            label: "年份",
            prop: "nf",
            hide: true,
            search: true,
            editDisplay:false,
            type: 'select',
            props: {
              label: 'nf',
              value: 'nf'
            },
            dicUrl: '/ksgl/tdd/allnf'
          },
          {
            label: "层次",
            prop: "cc",
            search: true,
            editDisplay:false,
            type: 'select',
            props: {
              label: 'name',
              value: 'name'
            },
            dicUrl: '/code/ccdm/all'
          },
          {
            label: "招生类型",
            prop: "zslx",
            editDisplay:false,
            type: 'select',
            props: {
              label: 'zslx',
              value: 'zslx'
            },
            dicUrl: '/code/codezslx/all'
          },
          {
            label: "省份",
            prop: "sfmc",
            // search: true,
            editDisplay:false,
            type: 'select',
            props: {
              label: 'name',
              value: 'name'
            },
            dicUrl: '/code/codedqb/allsf'
          },
          {
            label: "科类(修正后)",
            prop: "klmcxz",
            editDisplay:false,
          },
          {
            label: "投档单位",
            prop: "tddwmc",
            editDisplay:false,
          },
          {
            label: "专业(修正后)",
            prop: "zymc",
            editDisplay:false,
          },
          {
            label: "专业代码",
            prop: "lqzydm",
            editDisplay:false,
          },
          {
            label: "专业代号",
            prop: "lqzy",
            editDisplay:false,
          },
          {
            label: "录取人数",
            prop: "lqrs",
          },
          {
            label: "批次名称(修正前)",
            prop: "pcmc",
            editDisplay:false,
          },
          {
            label: "批次名称(修正后)",
            prop: "pcmcxz",
            editDisplay:false,
          },
        ],
      }
    },
    xzoption(){
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: "批次名称",
            prop: "pcmcxz",
            span: 24,
          },

        ]
      }
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
    },
    revised(row){
      this.xzrow = row;
      this.dialogVisible = true;
    },
    torevised(){
      if (this.xzrow) {
        this.selectionList.push(this.xzrow);
      }
      revised({tdd:this.selectionList,tddxz:this.xzform}).then((res)=>{
        if (res.data.code === 200) {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.dialogVisible = false;
          this.onLoad(this.page);
        }else {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
        }

      })
    },
    handleRevisedsed() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据进行修正?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            console.log(this.selectionList)
            // 使用 map 方法将每个对象的 id 抽取出来组成新的数组
            // const ids = this.selectionList.map(item => item.id);
            // 使用 join 方法将数组中的 id 组成一个字符串，逗号分割
            // const idString = ids.join(',');
            // console.log(idString); // 输出: "1,2,3"
            return this.revised();
          })
    },
    handleOneKeyRevised() {
      var confirmMsg = this.selectionList.length === 0 ? "您当前未勾选，是否默认将当前条件下的所有科类进行修正？" : "是否确认将选中科类进行修正？";
      this.$confirm(confirmMsg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        console.log('handleOneKeyRevised',this.selectionList)
        return this.toOneKeyRevised();
      })
    },
    handleOneKeyRestore() {
      var confirmMsg = this.selectionList.length === 0 ? "您当前未勾选，是否默认将当前条件下的所有科类进行还原？" : "是否确认将选中科类进行还原？";
      this.$confirm(confirmMsg, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        console.log('handleOneKeyRestore',this.selectionList)
        return this.toOneKeyRestore();
      })
    },
    toOneKeyRevised(){
      oneKeyRevised({list:this.selectionList,type:'pcxz'}).then((res)=>{
        if (res.data.code === 200) {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.onLoad(this.page);
        }else {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
        }

      })
    },
    toOneKeyRestore(){
      oneKeyRestore({list:this.selectionList,type:'pcxz'}).then((res)=>{
        if (res.data.code === 200) {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.onLoad(this.page);
        }else {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
        }

      })
    },

    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.type = 'pcxz';
      selecRrevisedPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });

    },
    refresh() {
      this.onLoad(this.page);
    },
    // 导入
    handleImport() {
      this.excelBox = true;
    },

    uploadAfter(res, done, loading) {
      if (res === "success") {
        this.excelBox = false;
        this.refresh();
        done();
      } else if (res === undefined) {
        this.$message.error("上传内容格式有误！");
        loading();
      } else {
        this.$message.warning("请上传 .zip 标准格式文件");
        loading();
      }
    },
    handleClose(done) {
      this.dialogVisible = false;
      done();
    },
  }
};
</script>

<style>
</style>
