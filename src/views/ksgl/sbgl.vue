<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refresh"
    >
      <template slot="menuLeft">
<!--        <el-button type="success"
                   size="mini"
                   icon="el-icon-download"
                   @click="handleExport">导出
        </el-button>-->
        <el-button type="success"
                   size="mini"
                   icon="el-icon-upload2"
                   @click="handleImport">导入
        </el-button>
        <el-button type="primary" plain style="float: right;" @click="onsbclear()">清空三表</el-button>
        <el-button type="primary" plain style="float: right;" @click="showxzsb = true;">下载三表</el-button>
        <el-button type="primary" plain style="float: right;" @click="opendialog('daorusanbiao','导入三表')">导入三表</el-button>
        <el-button type="primary" plain style="float: right;" @click="opendialog('daoruzhaopian','导入照片','zsxt_photo')">导入照片</el-button>
      </template>
      <template v-for="z in slotname" :slot="z" slot-scope="scope" >
        <div class="slotcontent" v-for="(e,a) in scope.row[z]" :key="a" >
          <div class="slotbody" v-for="(item,index) in e" :key="index">
            <div style="border-bottom: 1px solid #EBEEF5;background-color: #fafafa;padding: 5px">
              <p style="width: 120px">{{ item.options.label }}</p>
            </div>
            <div style="padding: 5px">
              <p style="width: 120px">{{item.values}}</p>
            </div>
          </div>
        </div>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button icon="el-icon-view" :size="scope.size" type="text" @click.stop="revised(scope.row)">查看</el-button>
      </template>
      <template slot="photo" slot-scope="scope">

        <p v-if="scope.row.photo" style="color: #00a5ec;cursor: pointer;text-align: center;" @click="view(scope.row,'zsxt_photo')">查看(上传)</p>
        <p v-else style="color: #f60000;cursor: pointer;text-align: center;">缺失</p>
      </template>
      <template slot="bmxx" slot-scope="scope">
        <p v-if="scope.row.photo" style="color: #00a5ec;cursor: pointer;text-align: center;" @click="view(scope.row,'zsxt_bmxx')">查看(上传)</p>
        <p v-else style="color: #f60000;cursor: pointer;text-align: center;">缺失</p>
<!--        <span style="color: #00a5ec;cursor: pointer;" @click="view(scope.row,'bmxx')">查看(生成)</span>-->
      </template>
      <template slot="tjxx" slot-scope="scope">
        <p v-if="scope.row.photo" style="color: #00a5ec;cursor: pointer;text-align: center;" @click="view(scope.row,'zsxt_tjxx')">查看(上传)</p>
        <p v-else style="color: #f60000;cursor: pointer;text-align: center;">缺失</p>
      </template>
      <template slot="cjyzyxx" slot-scope="scope">
        <p v-if="scope.row.photo" style="color: #00a5ec;cursor: pointer;text-align: center;" @click="view(scope.row,'zsxt_cjyzyxx')">查看(上传)</p>
        <p v-else style="color: #f60000;cursor: pointer;text-align: center;">缺失</p>
      </template>
      <template slot="sfz" slot-scope="scope">
        <p v-if="scope.row.photo" style="color: #00a5ec;cursor: pointer;text-align: center;" @click="view(scope.row,'zsxt_sfz')">查看(上传)</p>
        <p v-else style="color: #f60000;cursor: pointer;text-align: center;">缺失</p>
      </template>
      <template slot="fjb" slot-scope="scope">
        <p v-if="scope.row.photo" style="color: #00a5ec;cursor: pointer;text-align: center;" @click="view(scope.row,'zsxt_fjb')">查看(上传)</p>
        <p v-else style="color: #f60000;cursor: pointer;text-align: center;">缺失</p>
      </template>
    </avue-crud>
    <el-dialog title="数据导入"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
      <avue-form :option="excelOption"
                 v-model="excelForm"
                 :upload-after="uploadAfter">
        <template slot="menuForm">
          <el-button type="primary" @click="handleTemplate()">
            点击下载模板<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
        title="编辑"
        :visible.sync="dialogVisible"
        append-to-body
        width="80%"
        :before-close="handleClose">
      <avue-form v-model="xzform" :option="xzoption"></avue-form>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="照片" name="zsxt_photo">
          <div class="zhaopianbox">
            <p>
              录取照片
            </p>
            <div>
<!--                <img style="width: 90px;" :src="`${baseurl}/file/view/zsxt_photo_${tmform.ksh}`" >-->
              <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_photo_${tmform.ksh}`">
                <div slot="error" class="image-slot">
                  <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png" >
                </div>
              </el-image>
            </div>
            <span style="cursor: pointer;color: #00a5ec;" @click="opendialog('daoruzhaopian','导入照片','zsxt_photo')">替换</span>
          </div>
        </el-tab-pane>
        <el-tab-pane label="报名表" name="zsxt_bmxx">
          <div v-if="sbhtml && sbhtml.type == 'automatic'" v-html="sbhtml.data" :key="sbindex"></div>
          <div v-if="sbhtml && sbhtml.type == 'manual'" :key="sbindex">
            <img :src="sbhtml ? sbhtml.data : ''"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="成绩志愿表" name="zsxt_cjyzyxx">
          <div v-if="sbhtml && sbhtml.type == 'automatic'" v-html="sbhtml.data" :key="sbindex"></div>
          <div v-if="sbhtml && sbhtml.type == 'manual'" :key="sbindex">
            <img :src="sbhtml ? sbhtml.data : ''"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="体检表" name="zsxt_tjxx">
          <div v-if="sbhtml && sbhtml.type == 'automatic'" v-html="sbhtml.data" :key="sbindex"></div>
          <div v-if="sbhtml && sbhtml.type == 'manual'" :key="sbindex">
            <img :src="sbhtml ? sbhtml.data : ''"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="身份证" name="zsxt_sfz">
          <div class="zhaopianbox">
            <p>
              身份证
            </p>
            <div>
              <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_sfz_${tmform.ksh}`">
                <div slot="error" class="image-slot">
                  <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png" >
                </div>
              </el-image>
            </div>
            <span style="cursor: pointer;color: #00a5ec;" @click="opendialog('daoruzhaopian','导入照片','zsxt_sfz')">替换</span>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件表" name="zsxt_fjb" :key="sbindex">
          <div v-if="sbhtml && sbhtml.type == 'automatic'" v-html="sbhtml.data" :key="sbindex"></div>
          <div v-if="sbhtml && sbhtml.type == 'manual'" :key="sbindex">
            <img :src="sbhtml ? sbhtml.data : ''"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="未报到文件" name="zsxt_fourth">
          <div v-if="sbhtml && sbhtml.type == 'automatic'" v-html="sbhtml.data" :key="sbindex"></div>
          <div v-if="sbhtml && sbhtml.type == 'manual'" :key="sbindex">
            <img :src="sbhtml ? sbhtml.data : ''"/>
          </div>
        </el-tab-pane>
      </el-tabs>
<!--      <span slot="footer" class="dialog-footer">-->
<!--        <el-button @click="dialogVisible = false">取 消</el-button>-->
<!--        <el-button type="primary" @click="torevised()">确 定</el-button>-->
<!--      </span>-->
    </el-dialog>
    <el-dialog
        title="下载三表"
        :visible.sync="showxzsb"
        append-to-body
        width="60%"
        :before-close="handleClosexzsb">
      <el-form ref="form" :model="sbform" label-width="80px">
        <el-form-item label="下载类型">
          <el-checkbox-group v-model="sbform.xzlx">
            <el-checkbox label="zsxt_bmxx">报名表</el-checkbox>
            <el-checkbox label="zsxt_tjxx">体检表</el-checkbox>
            <el-checkbox label="zsxt_cjyzyxx">成绩与志愿表</el-checkbox>
            <el-checkbox label="zsxt_sfz" >身份证</el-checkbox>
            <el-checkbox label="zsxt_fjb">附件表</el-checkbox>
            <el-checkbox label="zsxt_fourth">未报到文件</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="文件处理">
          <el-radio-group v-model="sbform.wjcl">
            <el-radio label="original">原始文件</el-radio>
            <el-radio label="pdf">转化为PDF</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showxzsb = false">取 消</el-button>
        <el-button @click="downSb" type="primary">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
        :title="dialogtitle"
        :visible.sync="dialogVisiblesb"
        :direction="direction"
        append-to-body
        :before-close="handleClosesb">
      <div style="padding: 0 20px;">
        <daoruzhaopian @onclose="onclose" v-if="opendialogtype.daoruzhaopian" :type="fileType"></daoruzhaopian>
        <daorusanbiao @onclose="onclose" v-if="opendialogtype.daorusanbiao"></daorusanbiao>
        <photoimprot @onclose="onclose" v-if="opendialogtype.photoimprot" :file-type="fileType"></photoimprot>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, exportSbData, getDetail, getList, getone, remove, sbclear, sbview, update} from "@/api/ksgl/tdd";
import {mapGetters} from "vuex";
import daoruzhaopian from "@/page/zsxtreception/improtmodul/daoruzhaopian";
import daorusanbiao from "@/page/zsxtreception/improtmodul/daorusanbiao";
import photoimprot from "@/page/zsxtreception/improtmodul/photoimprot";


export default {
  components:{
    daoruzhaopian,
    daorusanbiao,
    photoimprot
  },
  data() {
    return {
      form: {},
      baseurl: this.baseUrl,
      sbform: {
        xzlx:[],
        wjcl: '',
      },
      xzform: {},
      tmform: {

      },
      query: {},
      xylist: [],
      zymclist: [],
      xzrowid: null,
      loading: true,
      dialogVisible: false,
      showxzsb: false,
      dialogtitle: '',
      dialogtype: '',
      dialogVisiblesb: false,
      opendialogtype: {
        daoruluqv: false,
        jiemiluqv: false,
        daorujihua: false,
        daorubaodao: false,
        daoruzhaopian: false,
        daorusanbiao: false,
        shujvzhuaqv: false,
        shujvqingkong: false,
        photoimprot: false,
      },
      fileType: null,
      direction: 'rtl',
      activeName: 'first',
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "文件上传",
            prop: "file",
            type: "upload",
            drag: true,
            loadText: "文件上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "info",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/ksgl/tdd/importBdsj",
          },
        ],
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      timer:'',
      slotname:[],
      selectionList: [],
      data: [],
      sbhtml: null,
      sbindex: 0,
    };
  },
  created() {
    this.onLoad(this.page);
  },
  watch: {
    activeName:{
      handler(val){
        this.getsbview(this.xzform,this.activeName)
      }
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tdd_add, false),
        viewBtn: this.vaildData(this.permission.tdd_view, false),
        delBtn: this.vaildData(this.permission.tdd_delete, false),
        editBtn: this.vaildData(this.permission.tdd_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    option(){
      return {
        height: 'auto',
        calcHeight: 210,
        searchShow: true,
        searchMenuSpan: 4,
        tip: false,
        border: true,
        labelWidth: 150,
        menuWidth: 120,
        menuFixed:"right",
        indexFixed: false,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        filterBtn: false,
        // refreshBtn: false,
        columnBtn: true,
        selection: true,
        column:[
          {
            label: "年份",
            prop: "nf",
            search:true,
            type: 'select',
            props: {
              label: 'nf',
              value: 'nf'
            },
            dicUrl: '/ksgl/tdd/allnf',
            rules: [{
              required: true,
              message: "请输入年份",
              trigger: "blur"
            }]
          },
          {
            label: "层次",
            prop: "cc",
            search:true,
            type: 'select',
            props: {
              label: 'cc',
              value: 'cc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=cc',
          },
          {
            label: "招生类型",
            prop: "zslx",
            search:true,
            type: 'select',
            props: {
              label: 'zslx',
              value: 'zslx'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=zslx',
          },
          {
            label: "省份",
            prop: "sfmc",
            search:true,
            type: 'select',
            props: {
              label: 'sfmc',
              value: 'sfmc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=sfmc',
          },
          {
            label: "考生号",
            prop: "ksh",
            search: true,

          },
          {
            label: "姓名",
            prop: "xm",
            search: true,

          },
          {
            label: "证件号码",
            prop: "zjhm",
            search: true,

          },
          {
            label: "批次",
            prop: "pcmc",
            search:true,
            type: 'select',
            props: {
              label: 'pcmc',
              value: 'pcmc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=pcmc',
          },
          {
            label: "科类",
            prop: "klmc",
            search:true,
            type: 'select',
            props: {
              label: 'klmc',
              value: 'klmc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=klmc',
          },
          {
            label: "学院",
            prop: "xymc",
            search:true,
            type: 'select',
            props: {
              label: 'xymc',
              value: 'xymc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=xymc',
          },
          {
            label: "专业",
            prop: "zymc",
            search:true,
            type: 'select',
            props: {
              label: 'zymc',
              value: 'zymc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=zymc',
          },
          {
            label: "班级",
            prop: "bjmc",
            search:true,
            type: 'select',
            props: {
              label: 'zymc',
              value: 'zymc'
            },
            dicUrl: '/ksgl/tdd/selectByField?field=zymc',
          },
          {
            label: "学号",
            prop: "xh",
          },
          {
            label: "报到状态",
            prop: "bdzt",
          },
          {
            label: "录取照片",
            prop: "photo",
            slot: true,
          },
          {
            label: "报名表",
            prop: "bmxx",
            slot: true,
          },
          {
            label: "体检表",
            prop: "tjxx",
            slot: true,
          },
          {
            label: "成绩与志愿表",
            prop: "cjyzyxx",
            slot: true,
          },
          {
            label: "身份证",
            prop: "sfz",
            slot: true,
          },
          {
            label: "附件表",
            prop: "fjb",
            slot: true,
          },
          {
            label: "未报到文件",
            prop: "fourth",
            slot: true,
          },
        ],
      }
    },
    xzoption(){
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: "层次",
            prop: "cc",
            readonly: true,
            span: 8,
          },
          {
            label: "招生类型",
            prop: "zslx",
            readonly: true,
            span: 8,

          },
          {
            label: "省份",
            prop: "sfmc",
            readonly: true,
            span: 8,

          },
          {
            label: "考生号",
            prop: "ksh",
            readonly: true,
            span: 8,

          },
          {
            label: "姓名",
            prop: "xm",
            readonly: true,
            span: 8,

          },
          {
            label: "证件号码",
            prop: "zjhm",
            search: true,
            readonly: true,
            span: 8,

          },
          {
            label: "批次",
            prop: "pcmc",
            readonly: true,
            span: 8,

          },
          {
            label: "科类",
            prop: "klmc",
            readonly: true,
            span: 8,

          },
          {
            label: "学院",
            prop: "xymc",
            readonly: true,
            span: 8,

          },
          {
            label: "专业",
            prop: "zymc",
            readonly: true,
            span: 8,

          },
          {
            label: "班级",
            prop: "bjmc",
            span: 8,

          },
          {
            label: "学号",
            prop: "xh",
            readonly: true,
            span: 8,

          },
          {
            label: "报到状态",
            prop: "bdzt",
            readonly: true,
            span: 8,

          },
        ]
      }
    },
  },
  methods: {
    view(row,type){
      this.xzform = row;
      getone(row.id).then((res) => {
        this.tmform = res.data.data;
          this.activeName = type;
          this.dialogVisible = true;
      });

    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    onsbclear(){
      this.$confirm('此操作将永久清空该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sbclear().then(res=>{
          if(res.success){
            this.$message({
              type: 'success',
              message: '清空成功!'
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清空'
        });
      });
    },
    getsbview(row,type){
      let data = {
        fileType: type,
        // ksh: row.ksh,
        id: row.id,
      }
      sbview(data).then(res=>{
        console.log(`sbhtml`,res.data)
        this.sbhtml = res.data;
        this.sbindex++;
      })
    },
    revised(row){
      this.xzform = row;
      getone(row.id).then((res) => {
          console.log(`getone`,res.data)
        this.tmform = res.data.data
          this.activeName = 'zsxt_photo'
          this.dialogVisible = true;
      });
      // this.activeName = 'zsxt_photo'
      // this.dialogVisible = true;
    },
    onclose(data){
      this.dialogVisiblesb = false;
      this.opendialogtype[this.dialogtype] = false;
    },
    opendialog(type,title,fileType){
      this.dialogVisiblesb = true;
      this.opendialogtype[type] = true;
      this.dialogtitle = title;
      this.dialogtype = type;
      this.fileType = fileType;
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });

    },
    refresh() {
      this.onLoad(this.page);
    },
    // 导入
    handleImport() {
      this.excelBox = true;
    },
    handleTemplate() {
      window.open(`/ksgl/tdd/downBdsjTemplate`);
    },
    uploadAfter(res, done, loading) {
      if (res === "success") {
        this.excelBox = false;
        this.refresh();
        done();
      } else if (res === undefined) {
        this.$message.error("上传内容格式有误！");
        loading();
      } else {
        this.$message.warning("请上传 .zip 标准格式文件");
        loading();
      }
    },
    handleClosesb(done) {
      this.dialogVisible = false;
      this.opendialogtype[this.dialogtype] = false;
      done();
    },
    handleClosexzsb(done) {
      this.showxzsb = false;
      done();
    },
    handleClose(done) {
      this.dialogVisible = false;
      done();
    },
    downSb(){
      const context = this.query;
      context.id = this.ids;
      exportSbData(context,{wjcl:this.sbform.wjcl, xzlx: this.sbform.xzlx.join(",")}).then((res) => {
      })
      this.showxzsb = false;
    },
  }
};
</script>

<style scoped>
.zhaopianbox{
  width: 130px;
  text-align: center;
  margin-right: 20px;
  margin-bottom: 20px;
}
img{
    width: 100%;
}
</style>
