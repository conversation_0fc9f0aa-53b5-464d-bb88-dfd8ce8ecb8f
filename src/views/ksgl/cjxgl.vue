<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @refresh-change="refresh"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   icon="el-icon-edit"
                   plain
                   v-if="permission.cjxgl_delete"
                   @click="handleDisposal">分数整理
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, disposal, getDetail, getList, remove, update} from "@/api/ksgl/cjxgl";
import {getAll as getAllCjxdm} from "@/api/zsxt/cjxdm";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        columnData: [],
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.cjxgl_add, false),
          viewBtn: this.vaildData(this.permission.cjxgl_view, false),
          delBtn: this.vaildData(this.permission.cjxgl_delete, false),
          editBtn: this.vaildData(this.permission.cjxgl_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      option(){
        return {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: true,
          selection: true,
          column: [
            {
              label: "年份",
              prop: "nf",
              search:true,
              hide:true,
              addDisplay:false,
              editDisplay:false,
              type: 'select',
              props: {
                label: 'nf',
                value: 'nf'
              },
              dicUrl: '/ksgl/tdd/allnf',
            },
            {
              label: "省份",
              prop: "sfmc",
              addDisplay:false,
              editDisplay:false,
            },
            {
              label: "语文",
              prop: "cjyw",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "数学",
              prop: "cjsx",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "外语",
              prop: "cjwy",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "政治",
              prop: "cjzhzh",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "地理",
              prop: "cjdl",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "物理",
              prop: "cjwl",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "化学",
              prop: "cjhx",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "生物",
              prop: "cjsw",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "技术",
              prop: "cjjsh",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "综合",
              prop: "cjzh",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
            {
              label: "高考成绩总分",
              prop: "cjzf",
              type: 'select',
              props: {
                label: 'label',
                value: 'code'
              },
              dicData: this.columnData,
            },
          ]
        }
      },
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleDisposal() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择需要整理分数的省份");
          return;
        }
        this.$confirm("分数整理会修改已有分数!","是否确认进行分数整理?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.loading = true;
            return disposal(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          let self = this;
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            getAllCjxdm({sfmc:this.form.sfmc}).then(data=>{
              var colList = [{label:"考成成绩(cj)",code:"cj"},{label:"特征成绩(tzcj)",code:"tzcj"},{label:"投档成绩(tdcj)",code:"tdcj"},{label:"基准成绩(zyytjzcj)",code:"zyytjzcj"}]
              data.data.forEach(item=>{
                colList.push(item)
              })
              this.columnData = colList;
            })
          });

        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh() {
        this.onLoad(this.page);
      },
    }
  };
</script>

<style>
</style>
