<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refresh"
    >
      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   icon="el-icon-edit"
                   plain
                   v-if="permission.tdd_delete"
                   @click="handleRevisedsed">批量编辑
        </el-button>
        <el-button type="success"
                   size="mini"
                   icon="el-icon-upload2"
                   @click="handleImport">导入
        </el-button>
      </template>
      <template v-for="z in slotname" :slot="z" slot-scope="scope" >
        <div class="slotcontent" v-for="(e,a) in scope.row[z]" :key="a" >
          <div class="slotbody" v-for="(item,index) in e" :key="index">
            <div style="border-bottom: 1px solid #EBEEF5;background-color: #fafafa;padding: 5px">
              <p style="width: 120px">{{ item.options.label }}</p>
            </div>
            <div style="padding: 5px">
              <p style="width: 120px">{{item.values}}</p>
            </div>
          </div>
        </div>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button icon="el-icon-magic-stick" :size="scope.size" type="text" @click.stop="revised(scope.row.id)">编辑</el-button>
      </template>
    </avue-crud>
    <el-dialog title="数据导入"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
      <avue-form :option="excelOption"
                 v-model="excelForm"
                 :upload-after="uploadAfter">
        <template slot="menuForm">
          <el-button type="primary" @click="handleTemplate()">
            点击下载模板<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog
        title="编辑"
        :visible.sync="dialogVisible"
        append-to-body
        width="30%"
        :before-close="handleClose">
      <avue-form v-model="xzform" :option="xzoption"></avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="torevised()">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, edit, getDetail, getList, remove, update} from "@/api/ksgl/tdd";
import {mapGetters} from "vuex";

export default {
  components:{
  },
  data() {
    return {
      form: {},
      xzform: {},
      query: {},
      xylist: [],
      zymclist: [],
      xzrowid: null,
      loading: true,
      dialogVisible: false,
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "文件上传",
            prop: "file",
            type: "upload",
            drag: true,
            loadText: "文件上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "info",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/ksgl/tdd/importBdsj",
          },
        ],
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      timer:'',
      slotname:[],
      selectionList: [],
      data: [],
    };
  },
  created() {
    this.onLoad(this.page);
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tdd_add, false),
        viewBtn: this.vaildData(this.permission.tdd_view, false),
        delBtn: this.vaildData(this.permission.tdd_delete, false),
        editBtn: this.vaildData(this.permission.tdd_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    option(){
      return {
        height: 'auto',
        calcHeight: 210,
        searchShow: true,
        searchMenuSpan: 4,
        tip: false,
        border: true,
        labelWidth: 150,
        menuWidth: 120,
        menuFixed:"right",
        indexFixed: false,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        filterBtn: false,
        // refreshBtn: false,
        columnBtn: true,
        selection: true,
        column:[
          {
            label: "省份",
            prop: "sfmc",
            editDisplay:false,
          },
          {
            label: "层次",
            prop: "cc",
            editDisplay:false,
          },
          {
            label: "招生类型",
            prop: "zslx",
            editDisplay:false,
          },

          {
            label: "学院",
            prop: "xymc",
            editDisplay:false,
          },
          {
            label: "专业",
            prop: "zymc",
            editDisplay:false,
          },
          {
            label: "考生号",
            prop: "ksh",
            search: true,
            editDisplay:false,
          },
          {
            label: "姓名",
            prop: "xm",
            search: true,
            editDisplay:false,
          },
          {
            label: "身份证",
            prop: "zjhm",
            search: true,
            editDisplay:false,
          },
          {
            label: "报到状态",
            prop: "bdzt",
            editDisplay:false,
          },
          {
            label: "备注",
            prop: "bdztbz",
          },
        ],
      }
    },
    xzoption(){
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: "报到状态",
            prop: "bdzt",
            span: 24,
            type: "select",
            dicMethod: "post",
            dicUrl: '/sytSysDict/list?code=ZSXT_BDZT',
            props: {
              label: "name",
              value: "value",
            },
          },
          {
            label: "报到备注",
            prop: "bdztbz",
            span: 24,
            type: "textarea",
          },

        ]
      }
    },
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
    },
    revised(id){
      this.xzrowid = id;
      console.log(this.xzrowid)
      this.dialogVisible = true;
    },
    torevised()  {
      console.log(this.xzform)
      edit(this.xzform,this.xzrowid).then((res)=>{
        if (res.data.code === 200) {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.dialogVisible = false;
          this.onLoad(this.page);
        }else {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
        }

      })
    },
    handleRevisedsed() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据进行修正?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            console.log(this.selectionList)
            // 使用 map 方法将每个对象的 id 抽取出来组成新的数组
            const ids = this.selectionList.map(item => item.id);
            // 使用 join 方法将数组中的 id 组成一个字符串，逗号分割
            const idString = ids.join(',');
            console.log(idString); // 输出: "1,2,3"
            return this.revised(idString);
          })
      // .then(() => {
      //   this.onLoad(this.page);
      //   this.$message({
      //     type: "success",
      //     message: "操作成功!"
      //   });
      //   this.$refs.crud.toggleSelection();
      // });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });

    },
    refresh() {
      this.onLoad(this.page);
    },
    // 导入
    handleImport() {
      this.excelBox = true;
    },
    handleTemplate() {
      window.open(`/ksgl/tdd/downBdsjTemplate`);
    },
    uploadAfter(res, done, loading) {
      if (res === "success") {
        this.excelBox = false;
        this.refresh();
        done();
      } else if (res === undefined) {
        this.$message.error("上传内容格式有误！");
        loading();
      } else {
        this.$message.warning("请上传 .zip 标准格式文件");
        loading();
      }
    },
    handleClose(done) {
      this.dialogVisible = false;
      done();
    },
  }
};
</script>

<style>
</style>
