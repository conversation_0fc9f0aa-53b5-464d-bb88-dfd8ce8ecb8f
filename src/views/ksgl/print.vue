<!--<template>-->
<!--    <div>-->
<!--        <button @click="startPrint">开始打印</button>-->
<!--        <div v-if="progress.status === 'progress'">打印进度: {{ progress.progress }}%</div>-->
<!--        <div v-if="progress.status === 'completed'">打印完成</div>-->
<!--        <div v-if="progress.status === 'error'">错误: {{ progress.error }}</div>-->
<!--    </div>-->
<!--</template>-->

<!--<script>-->
<!--import ReconnectingWebSocket from 'reconnecting-websocket';-->
<!--import {getAll} from "@/api/ksgl/tdd";-->


<!--export default {-->
<!--    data() {-->
<!--        return {-->
<!--            query: this.$route.query.query,-->
<!--            tddList: [],-->
<!--            ws: null,-->
<!--            progress: {}-->
<!--        };-->
<!--    },-->
<!--    /*created() {-->

<!--    },*/-->
<!--    methods: {-->
<!--        startPrint() {-->
<!--            this.ws = new ReconnectingWebSocket('ws://localhost:9094/noAuth/ws/pdf');-->

<!--            this.ws.addEventListener('open', () => {-->
<!--                console.log('WebSocket connection established');-->
<!--                this.query = this.query ? JSON.parse(this.$route.query.query) : {};-->
<!--                getAll(this.query).then(res=>{-->
<!--                    // console.log(res.data)-->
<!--                    const data = {-->
<!--                        tddList: res.data.data-->
<!--                    };-->
<!--                    this.ws.send(JSON.stringify(data));-->
<!--                })-->
<!--            });-->

<!--            this.ws.addEventListener('message', (event) => {-->
<!--                if (typeof event.data === 'string') {-->
<!--                    const message = JSON.parse(event.data);-->
<!--                    this.handleWebSocketMessage(message);-->
<!--                } else {-->
<!--                    this.handleBinaryMessage(event.data);-->
<!--                }-->
<!--            });-->

<!--            this.ws.addEventListener('close', () => {-->
<!--                console.log('WebSocket connection closed');-->
<!--            });-->

<!--            this.ws.addEventListener('error', (error) => {-->
<!--                console.log('WebSocket error: ', error);-->
<!--            });-->
<!--        },-->
<!--        handleWebSocketMessage(message) {-->
<!--            this.progress = message;-->
<!--        },-->
<!--        handleBinaryMessage(data) {-->
<!--            const blob = new Blob([data], { type: 'application/pdf' });-->
<!--            const url = URL.createObjectURL(blob);-->
<!--            const iframe = document.createElement('iframe');-->
<!--            iframe.style.display = 'none';-->
<!--            iframe.src = url;-->
<!--            iframe.onload = () => {-->
<!--                console.log('Iframe loaded, starting print');-->
<!--                iframe.contentWindow.print();-->
<!--                document.body.removeChild(iframe);-->
<!--                URL.revokeObjectURL(url);-->
<!--            };-->
<!--            document.body.appendChild(iframe);-->
<!--        },-->
<!--        beforeDestroy() {-->
<!--            if (this.ws) {-->
<!--                this.ws.close();-->
<!--            }-->
<!--        },-->

<!--    }-->
<!--};-->
<!--</script>-->
