[
    {
        label: "年份",
        prop: "nf",
        search:true,
        rules: [{
            required: true,
            message: "请输入年份",
            trigger: "blur"
        }]
    },
    {
        label: "考生号",
        prop: "ksh",
        search:true,
        rules: [{
            required: true,
            message: "请输入考生号",
            trigger: "blur"
        }]
    },
    {
        label: "姓名",
        prop: "xm",
        search:true,
        rules: [{
            required: true,
            message: "请输入姓名",
            trigger: "blur"
        }]
    },
    {
        label: "投档单编号",
        prop: "bh",
        rules: [{
            required: true,
            message: "请输入投档单编号",
            trigger: "blur"
        }]
    },
    {
        label: "报名单位",
        prop: "bmdw",
        rules: [{
            required: true,
            message: "请输入报名单位",
            trigger: "blur"
        }]
    },
    {
        label: "毕业类别代码",
        prop: "bylbdm",
        rules: [{
            required: true,
            message: "请输入毕业类别代码",
            trigger: "blur"
        }]
    },
    {
        label: "考生成绩",
        prop: "cj",
        rules: [{
            required: true,
            message: "请输入考生成绩",
            trigger: "blur"
        }]
    },
    {
        label: "出生年月",
        prop: "csny",
        rules: [{
            required: true,
            message: "请输入出生年月",
            trigger: "blur"
        }]
    },
    {
        label: "考生户口所在地区",
        prop: "dqdm",
        rules: [{
            required: true,
            message: "请输入考生户口所在地区",
            trigger: "blur"
        }]
    },
    {
        label: "当前投档单位代码",
        prop: "dqtddw",
        rules: [{
            required: true,
            message: "请输入当前投档单位代码",
            trigger: "blur"
        }]
    },
    {
        label: "是否服从定向调剂",
        prop: "dxtj",
        rules: [{
            required: true,
            message: "请输入是否服从定向调剂",
            trigger: "blur"
        }]
    },
    {
        label: "会考等级",
        prop: "hkdj",
        rules: [{
            required: true,
            message: "请输入会考等级",
            trigger: "blur"
        }]
    },
    {
        label: "考生会考考号",
        prop: "hkkh",
        rules: [{
            required: true,
            message: "请输入考生会考考号",
            trigger: "blur"
        }]
    },
    {
        label: "计划性质",
        prop: "jhxz",
        rules: [{
            required: true,
            message: "请输入计划性质",
            trigger: "blur"
        }]
    },
    {
        label: "考生家庭地址",
        prop: "jtdz",
        rules: [{
            required: true,
            message: "请输入考生家庭地址",
            trigger: "blur"
        }]
    },
    {
        label: "科类代码",
        prop: "kldm",
        rules: [{
            required: true,
            message: "请输入科类代码",
            trigger: "blur"
        }]
    },
    {
        label: "考生奖励或处分",
        prop: "ksjlhcf",
        rules: [{
            required: true,
            message: "请输入考生奖励或处分",
            trigger: "blur"
        }]
    },
    {
        label: "考生类别代码",
        prop: "kslbdm",
        rules: [{
            required: true,
            message: "请输入考生类别代码",
            trigger: "blur"
        }]
    },
    {
        label: "考试类型代码",
        prop: "kslxdm",
        rules: [{
            required: true,
            message: "请输入考试类型代码",
            trigger: "blur"
        }]
    },
    {
        label: "考生特长",
        prop: "kstc",
        rules: [{
            required: true,
            message: "请输入考生特长",
            trigger: "blur"
        }]
    },
    {
        label: "考生特征",
        prop: "kstz",
        rules: [{
            required: true,
            message: "请输入考生特征",
            trigger: "blur"
        }]
    },
    {
        label: "考生状态",
        prop: "kszt",
        rules: [{
            required: true,
            message: "请输入考生状态",
            trigger: "blur"
        }]
    },
    {
        label: "录取方式",
        prop: "lqfs",
        rules: [{
            required: true,
            message: "请输入录取方式",
            trigger: "blur"
        }]
    },
    {
        label: "录取专业",
        prop: "lqzy",
        rules: [{
            required: true,
            message: "请输入录取专业",
            trigger: "blur"
        }]
    },
    {
        label: "考生联系电话",
        prop: "lxdh",
        rules: [{
            required: true,
            message: "请输入考生联系电话",
            trigger: "blur"
        }]
    },
    {
        label: "民族代码",
        prop: "mzdm",
        rules: [{
            required: true,
            message: "请输入民族代码",
            trigger: "blur"
        }]
    },

    {
        label: "批次代码",
        prop: "pcdm",
        rules: [{
            required: true,
            message: "请输入批次代码",
            trigger: "blur"
        }]
    },
    {
        label: "锁定标志",
        prop: "sdbz",
        rules: [{
            required: true,
            message: "请输入锁定标志",
            trigger: "blur"
        }]
    },
    {
        label: "考生身份证号",
        prop: "sfzh",
        rules: [{
            required: true,
            message: "请输入考生身份证号",
            trigger: "blur"
        }]
    },
    {
        label: "收件人",
        prop: "sjr",
        rules: [{
            required: true,
            message: "请输入收件人",
            trigger: "blur"
        }]
    },
    {
        label: "投挡成绩",
        prop: "tdcj",
        rules: [{
            required: true,
            message: "请输入投挡成绩",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码",
        prop: "tddw",
        rules: [{
            required: true,
            message: "请输入投档单位代码",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码1",
        prop: "tddwdm1",
        rules: [{
            required: true,
            message: "请输入投档单位代码1",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码2",
        prop: "tddwdm2",
        rules: [{
            required: true,
            message: "请输入投档单位代码2",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码3",
        prop: "tddwdm3",
        rules: [{
            required: true,
            message: "请输入投档单位代码3",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码4",
        prop: "tddwdm4",
        rules: [{
            required: true,
            message: "请输入投档单位代码4",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码5",
        prop: "tddwdm5",
        rules: [{
            required: true,
            message: "请输入投档单位代码5",
            trigger: "blur"
        }]
    },
    {
        label: "投档单位代码6",
        prop: "tddwdm6",
        rules: [{
            required: true,
            message: "请输入投档单位代码6",
            trigger: "blur"
        }]
    },
    {
        label: "退挡原因",
        prop: "tdyydm",
        rules: [{
            required: true,
            message: "请输入退挡原因",
            trigger: "blur"
        }]
    },
    {
        label: "投档志愿",
        prop: "tdzy",
        rules: [{
            required: true,
            message: "请输入投档志愿",
            trigger: "blur"
        }]
    },
    {
        label: "体检合格",
        prop: "tjhg",
        rules: [{
            required: true,
            message: "请输入体检合格",
            trigger: "blur"
        }]
    },
    {
        label: "体检结论代码",
        prop: "tjjldm",
        rules: [{
            required: true,
            message: "请输入体检结论代码",
            trigger: "blur"
        }]
    },
    {
        label: "特征成绩",
        prop: "tzcj",
        rules: [{
            required: true,
            message: "请输入特征成绩",
            trigger: "blur"
        }]
    },
    {
        label: "外语口试",
        prop: "wyks",
        rules: [{
            required: true,
            message: "请输入外语口试",
            trigger: "blur"
        }]
    },
    {
        label: "外语听力",
        prop: "wytl",
        rules: [{
            required: true,
            message: "请输入外语听力",
            trigger: "blur"
        }]
    },
    {
        label: "外语语种代码",
        prop: "wyyzdm",
        rules: [{
            required: true,
            message: "请输入外语语种代码",
            trigger: "blur"
        }]
    },
    {
        label: "性别代码",
        prop: "xbdm",
        rules: [{
            required: true,
            message: "请输入性别代码",
            trigger: "blur"
        }]
    },
    {
        label: "考生系统单位",
        prop: "xtdw",
        rules: [{
            required: true,
            message: "请输入考生系统单位",
            trigger: "blur"
        }]
    },
    {
        label: "应试卷种代码",
        prop: "ysjzdm",
        rules: [{
            required: true,
            message: "请输入应试卷种代码",
            trigger: "blur"
        }]
    },
    {
        label: "预投专业",
        prop: "ytzy",
        rules: [{
            required: true,
            message: "请输入预投专业",
            trigger: "blur"
        }]
    },
    {
        label: "院校导入成绩",
        prop: "yxdrcj",
        rules: [{
            required: true,
            message: "请输入院校导入成绩",
            trigger: "blur"
        }]
    },
    {
        label: "邮政编码",
        prop: "yzbm",
        rules: [{
            required: true,
            message: "请输入邮政编码",
            trigger: "blur"
        }]
    },
    {
        label: "照顾分",
        prop: "zgf",
        rules: [{
            required: true,
            message: "请输入照顾分",
            trigger: "blur"
        }]
    },
    {
        label: "准考证号",
        prop: "zkzh",
        rules: [{
            required: true,
            message: "请输入准考证号",
            trigger: "blur"
        }]
    },
    {
        label: "政审意见",
        prop: "zsyj",
        rules: [{
            required: true,
            message: "请输入政审意见",
            trigger: "blur"
        }]
    },
    {
        label: "毕业中学代码",
        prop: "zxdm",
        rules: [{
            required: true,
            message: "请输入毕业中学代码",
            trigger: "blur"
        }]
    },
    {
        label: "毕业中学名称",
        prop: "zxmc",
        rules: [{
            required: true,
            message: "请输入毕业中学名称",
            trigger: "blur"
        }]
    },
    {
        label: "专业代号1",
        prop: "zydh1",
        rules: [{
            required: true,
            message: "请输入专业代号1",
            trigger: "blur"
        }]
    },
    {
        label: "专业代号2",
        prop: "zydh2",
        rules: [{
            required: true,
            message: "请输入专业代号2",
            trigger: "blur"
        }]
    },
    {
        label: "专业代号3",
        prop: "zydh3",
        rules: [{
            required: true,
            message: "请输入专业代号3",
            trigger: "blur"
        }]
    },
    {
        label: "专业代号4",
        prop: "zydh4",
        rules: [{
            required: true,
            message: "请输入专业代号4",
            trigger: "blur"
        }]
    },
    {
        label: "专业代号5",
        prop: "zydh5",
        rules: [{
            required: true,
            message: "请输入专业代号5",
            trigger: "blur"
        }]
    },
    {
        label: "专业代号6",
        prop: "zydh6",
        rules: [{
            required: true,
            message: "请输入专业代号6",
            trigger: "blur"
        }]
    },
    {
        label: "专业合格",
        prop: "zyhg",
        rules: [{
            required: true,
            message: "请输入专业合格",
            trigger: "blur"
        }]
    },
    {
        label: "体检专业受限代码1",
        prop: "zysxdm1",
        rules: [{
            required: true,
            message: "请输入体检专业受限代码1",
            trigger: "blur"
        }]
    },
    {
        label: "体检专业受限代码2",
        prop: "zysxdm2",
        rules: [{
            required: true,
            message: "请输入体检专业受限代码2",
            trigger: "blur"
        }]
    },
    {
        label: "体检专业受限代码3",
        prop: "zysxdm3",
        rules: [{
            required: true,
            message: "请输入体检专业受限代码3",
            trigger: "blur"
        }]
    },
    {
        label: "体检专业受限代码4",
        prop: "zysxdm4",
        rules: [{
            required: true,
            message: "请输入体检专业受限代码4",
            trigger: "blur"
        }]
    },
    {
        label: "体检专业受限代码5",
        prop: "zysxdm5",
        rules: [{
            required: true,
            message: "请输入体检专业受限代码5",
            trigger: "blur"
        }]
    },
    {
        label: "体检专业受限代码6",
        prop: "zysxdm6",
        rules: [{
            required: true,
            message: "请输入体检专业受限代码6",
            trigger: "blur"
        }]
    },
    {
        label: "专业投档附加成绩",
        prop: "zyytfjcj",
        rules: [{
            required: true,
            message: "请输入专业投档附加成绩",
            trigger: "blur"
        }]
    },
    {
        label: "专业投档基准成绩",
        prop: "zyytjzcj",
        rules: [{
            required: true,
            message: "请输入专业投档基准成绩",
            trigger: "blur"
        }]
    },
    {
        label: "专业志愿调剂",
        prop: "zyzytj",
        rules: [{
            required: true,
            message: "请输入专业志愿调剂",
            trigger: "blur"
        }]
    },
    {
        label: "政治面目代码",
        prop: "zzmmdm",
        rules: [{
            required: true,
            message: "请输入政治面目代码",
            trigger: "blur"
        }]
    },
    {
        label: "专业名称",
        prop: "zymc",
        rules: [{
            required: true,
            message: "请输入专业名称",
            trigger: "blur"
        }]
    },
    {
        label: "班级名称",
        prop: "bjmc",
        rules: [{
            required: true,
            message: "请输入班级名称",
            trigger: "blur"
        }]
    },
    {
        label: "学号",
        prop: "xh",
        rules: [{
            required: true,
            message: "请输入学号",
            trigger: "blur"
        }]
    },
    {
        label: "院系名称",
        prop: "dwmc",
        rules: [{
            required: true,
            message: "请输入院系名称",
            trigger: "blur"
        }]
    },
    {
        label: "录取通知书号",
        prop: "lqtzsh",
        rules: [{
            required: true,
            message: "请输入录取通知书号",
            trigger: "blur"
        }]
    },
    {
        label: "班级代码",
        prop: "bjdm",
        rules: [{
            required: true,
            message: "请输入班级代码",
            trigger: "blur"
        }]
    },
    {
        label: "毕业类别名称",
        prop: "bylbmc",
        rules: [{
            required: true,
            message: "请输入毕业类别名称",
            trigger: "blur"
        }]
    },
    {
        label: "地区名称",
        prop: "dqmc",
        rules: [{
            required: true,
            message: "请输入地区名称",
            trigger: "blur"
        }]
    },
    {
        label: "院系代码",
        prop: "dwdm",
        rules: [{
            required: true,
            message: "请输入院系代码",
            trigger: "blur"
        }]
    },
    {
        label: "科类名称",
        prop: "klmc",
        rules: [{
            required: true,
            message: "请输入科类名称",
            trigger: "blur"
        }]
    },
    {
        label: "考生类别名称",
        prop: "kslbmc",
        rules: [{
            required: true,
            message: "请输入考生类别名称",
            trigger: "blur"
        }]
    },
    {
        label: "考生类型名称",
        prop: "kslxmc",
        rules: [{
            required: true,
            message: "请输入考生类型名称",
            trigger: "blur"
        }]
    },
    {
        label: "民族名称",
        prop: "mzmc",
        rules: [{
            required: true,
            message: "请输入民族名称",
            trigger: "blur"
        }]
    },
    {
        label: "批次名称",
        prop: "pcmc",
        rules: [{
            required: true,
            message: "请输入批次名称",
            trigger: "blur"
        }]
    },
    {
        label: "退挡原因名称",
        prop: "tdyymc",
        rules: [{
            required: true,
            message: "请输入退挡原因名称",
            trigger: "blur"
        }]
    },
    {
        label: "体检结论名称",
        prop: "tjjlmc",
        rules: [{
            required: true,
            message: "请输入体检结论名称",
            trigger: "blur"
        }]
    },
    {
        label: "外语语种名称",
        prop: "wyyzmc",
        rules: [{
            required: true,
            message: "请输入外语语种名称",
            trigger: "blur"
        }]
    },
    {
        label: "性别名称",
        prop: "xbmc",
        rules: [{
            required: true,
            message: "请输入性别名称",
            trigger: "blur"
        }]
    },
    {
        label: "专业代码",
        prop: "zydm",
        rules: [{
            required: true,
            message: "请输入专业代码",
            trigger: "blur"
        }]
    },
    {
        label: "政治面貌名称",
        prop: "zzmmmc",
        rules: [{
            required: true,
            message: "请输入政治面貌名称",
            trigger: "blur"
        }]
    },
    {
        label: "生源地代码",
        prop: "syddm",
        rules: [{
            required: true,
            message: "请输入生源地代码",
            trigger: "blur"
        }]
    },
    {
        label: "生源地名称",
        prop: "sydmc",
        rules: [{
            required: true,
            message: "请输入生源地名称",
            trigger: "blur"
        }]
    },
    {
        label: "校区",
        prop: "xq",
        rules: [{
            required: true,
            message: "请输入校区",
            trigger: "blur"
        }]
    },
    {
        label: "备注",
        prop: "bz",
        span: 24,
        type: "textarea",
    },
]
