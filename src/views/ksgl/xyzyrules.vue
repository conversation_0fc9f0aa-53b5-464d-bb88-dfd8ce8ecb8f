<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @refresh-change="refresh"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.xyzyrules_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   size="mini"
                   icon="el-icon-upload2"
                   @click="handleImport">导入
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="数据导入"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
      <avue-form :option="excelOption"
                 v-model="excelForm"
                 :upload-after="uploadAfter">
        <template slot="menuForm">
          <el-button type="primary" @click="handleTemplate()">
            点击下载模板<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/ksgl/xyzyrules";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        excelBox: false,
        excelForm: {},
        excelOption: {
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "文件上传",
              prop: "file",
              type: "upload",
              drag: true,
              loadText: "文件上传中，请稍等",
              span: 24,
              propsHttp: {
                res: "info",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/ksgl/xyzyrules/import",
            },
          ],
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          searchIndex:3,
          searchIcon:true,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "年份",
              prop: "nf",
              search: true,
              searchLabelWidth: 45,
              addDisplay: false,
              eidtDisplay: false,
              type: 'select',
              props: {
                label: 'nf',
                value: 'nf'
              },
              dicUrl: '/ksgl/tdd/allnf'
            },
            /*{
              label: "省份名称",
              prop: "sfmc",
              addDisplay: false,
              eidtDisplay: false,
            },*/
            {
              label: "学院",
              prop: "xymc",
              search: true,
              rules: [{
                required: true,
                message: "请输入学院名称",
                trigger: "blur"
              }]
            },
            {
              label: "专业名称",
              prop: "zymc",
              search: true,
              rules: [{
                required: true,
                message: "请输入专业名称",
                trigger: "blur"
              }]
            },
            {
              label: "层次",
              prop: "ccmc",
              search: true,
              rules: [{
                required: true,
                message: "请输入层次",
                trigger: "blur"
              }]
            },
            {
              label: "招考方向",
              prop: "zkfx",
            },
            {
              label: "专业类别",
              prop: "zylb",
            },
            {
              label: "国际专业代码",
              prop: "gjzydm",
            },
            {
              label: "学制",
              prop: "xz",
            },
            {
              label: "校内专业名称",
              prop: "xnzymc",
              rules: [{
                required: true,
                message: "请输入校内专业名称",
                trigger: "blur"
              }]
            },
            {
              label: "校编码",
              prop: "xbdm",
            },
            {
              label: "校区",
              prop: "xq",
            },
            {
              label: "是否师范",
              prop: "sfsf",
            },
            {
              label: "体检受限",
              prop: "tjsx",
            },
            {
              label: "报到地点",
              prop: "bddd",
            },
            {
              label: "入学日期",
              prop: "rxrq",
            },
            /*{
              label: "录取人数",
              prop: "enrollNum",
              addDisplay: false,
              eidtDisplay: false,
            },*/

          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.xyzyrules_add, false),
          viewBtn: this.vaildData(this.permission.xyzyrules_view, false),
          delBtn: this.vaildData(this.permission.xyzyrules_delete, false),
          editBtn: this.vaildData(this.permission.xyzyrules_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh() {
        this.onLoad(this.page);
      },
      // 导入
      handleImport() {
        this.excelBox = true;
      },

      uploadAfter(res, done, loading) {
        if (res === "success") {
          this.excelBox = false;
          this.refresh();
          done();
        } else if (res === undefined) {
          this.$message.error("上传内容格式有误！");
          loading();
        } else {
          /*const blob = new Blob([res], {type: "application/octet-stream"});
          if (window.navigator.msSaveOrOpenBlob) {
            console.log('msSaveOrOpenBlob:' + res);
            //msSaveOrOpenBlob方法返回bool值
            navigator.msSaveBlob(blob, "错误信息.xlsx"); //本地保存
          } else {
            console.log('link:' + res);
            const link = document.createElement("a"); //a标签下载
            link.href = window.URL.createObjectURL(blob);
            link.download = "错误信息.xlsx";
            link.click();
            window.URL.revokeObjectURL(link.href);
          }*/
          this.$message.warning("上传内容格式有误！");
          loading();
        }
      },

      handleTemplate() {
        window.open(`/ksgl/xyzyrules/downImportTemplate`);
      },
    }
  };
</script>

<style>
</style>
