import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const ListParam = (data) => {
    return request({
        url: baseUrl + '/platform/qywx/param/list',
        method: 'get',
        params: {
            ...data
        }
    });
}


export const SaveParam = (data) => request({
    url: baseUrl + '/platform/qywx/param/save',
    method: 'post',
    data: {
        ...data
    }
});

export const GetParam = (data) => request({
    url: baseUrl + '/platform/qywx/param/get',
    method: 'get',
    param: {
        ...data
    }
});

export const DeleteParam = (data) => request({
    url: baseUrl + '/platform/qywx/param/delete',
    method: 'delete',
    data: {
        ...data
    }
});


export const ListApp = (data) => {
    return request({
        url: baseUrl + '/platform/qywx/app/list',
        method: 'get',
        params: {
            ...data
        }
    });
}

export const SaveApp = (data) => request({
    url: baseUrl + '/platform/qywx/app/save',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteApp = (data) => request({
    url: baseUrl + '/platform/qywx/app/delete',
    method: 'delete',
    data: {
        ...data
    }
});


export const ListUser = (data) => {
    return request({
        url: baseUrl + '/platform/qywx/user/list',
        method: 'get',
        params: {
            ...data
        }
    });
}

export const SaveUser = (data) => request({
    url: baseUrl + '/platform/qywx/user/save',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteUser = (data) => request({
    url: baseUrl + '/platform/qywx/user/delete',
    method: 'delete',
    data: {
        ...data
    }
});

export const SendMessage = (data) => request({
    url: baseUrl + '/platform/qywx/user/sendMessage',
    method: 'put',
    data: {
        ...data
    }
})

export const ListUserBind = (data) => {
    return request({
        url: baseUrl + '/platform/qywx/user_bind/list',
        method: 'get',
        params: {
            ...data
        }
    });
}

export const SaveUserBind = (data) => request({
    url: baseUrl + '/platform/qywx/user_bind/save',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteUserBind = (data) => request({
    url: baseUrl + '/platform/qywx/user_bind/delete',
    method: 'delete',
    data: {
        ...data
    }
});

export const GetUserBindPreBindInfo = () => request({
    url: baseUrl + '/platform/qywx/user_bind/preBindInfo',
    method: 'get'
})