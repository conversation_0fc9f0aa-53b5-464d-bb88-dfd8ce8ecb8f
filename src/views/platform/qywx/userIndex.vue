<template>
    <div>
        <basic-container>
            <avue-crud ref="crud"
                       v-model="form"
                       :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="pageList"
                       :page.sync="page"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-save="saveHandle"
                       @row-update="updateHandle"
                       @row-del="deleteHandle"
                       @search-change="searchChange"
                       @refresh-change="refresh"
            >
                <template v-slot:menuLeft="scope">
                    <el-button type="primary" icon="el-icon-message" size="small" plain @click="openMessage">消息测试</el-button>
                    <el-button type="primary" icon="el-icon-upload2" size="small" plain @click="handleImport">导入</el-button>
                </template>
            </avue-crud>
        </basic-container>

        <el-dialog title="消息测试"
                   :visible.sync="messageDialog"
                   :modal-append-to-body="false"
                   :modal="false"
                   width="600px">
            <el-alert
                    :title="messageAlert"
                    type="info">
            </el-alert>
            <el-form ref="messageForm" :model="messageForm" :rules="messageFormRules">
                <el-form-item label="微信用户ID" prop="wxUserId">
                    <el-input v-model="messageForm.wxUserId"></el-input>
                </el-form-item>
                <el-form-item label="消息内容" prop="content">
                    <el-input type="textarea" v-model="messageForm.content"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="doMessageSend" :loading="messageFormSubmitLoading">发送</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog title="用户数据导入"
                   :modal-append-to-body="false"
                   :visible.sync="excelBox"
                   :destroy-on-close="true"
                   width="600px"
                   :close-on-click-modal="false">
            <avue-form :option="excelOption"
                       v-model="excelForm"
                       :upload-after="uploadAfter">
                <template slot="menuForm">
                    <el-button type="primary" @click="handleTemplate()">
                        点击下载<i class="el-icon-download el-icon--right"></i>
                    </el-button>
                </template>
            </avue-form>
        </el-dialog>
    </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteUser, ListUser, SaveUser, SendMessage} from "./qywxApi";

export default {
    data() {
        return {
            form: {},
            tableLoading: false,
            pageList: [],
            page: {
                //pageSizes: [10, 20, 30, 40],默认
                currentPage: 1,
                total: 0,
                pageSize: 10
            },
            optionDetail: {
                index: true,
                size: "mini",
                dialogWidth: 580,
                dialogClickModal: false,
                // searchShowBtn: false,
                searchShow: false,
                // menuWidth: 150,
                align: "center",
                // editBtnText: "修改",
                // delBtnText: '删除',
                border: true,
                column: [
                    {
                        label: 'sysId',
                        prop: "sysId",
                        search: true,
                        rules: [{required: true, trigger: blur}]
                    },
                    {
                        label: 'wxId',
                        prop: "wxId",
                        search: true,
                        rules: [{required: true, trigger: blur}]
                    },
                    {
                        label: '说明',
                        prop: "description",
                        type: "textarea",
                        span: 24,
                    },
                    {
                        label: '创建时间',
                        prop: "created",
                        readonly: true,
                        addDisplay: false,
                        editDisplay: false,
                        editDisabled: true,
                        addDisabled: true,
                        type: "date",
                        format: "yyyy-MM-dd hh:mm:ss",
                    },
                    {
                        label: '更新时间',
                        prop: "updated",
                        readonly: true,
                        addDisplay: false,
                        editDisplay: false,
                        editDisabled: true,
                        addDisabled: true,
                        type: "date",
                        format: "yyyy-MM-dd hh:mm:ss",
                    },
                ]
            },
            messageDialog: false,
            messageForm: {
                wxUserId: null,
                content: null
            },
            messageFormRules: {
                wxUserId: [
                    {required: true},
                ],
                content: [
                    {required: true}
                ]
            },
            messageFormSubmitLoading: false,
            messageAlert: null,
            excelBox: false,
            excelForm: {},
            excelOption: {
                submitBtn: false,
                emptyBtn: false,
                column: [
                    {
                        label: "模板上传",
                        prop: "file",
                        type: "upload",
                        drag: true,
                        loadText: "模板上传中，请稍等",
                        span: 24,
                        propsHttp: {
                            res: "info",
                        },
                        tip: "请上传 .xlsx 标准格式文件",
                        action: "/platform/qywx/user/importData",
                    },

                ],
            }
        }
    },
    computed: {
        ...mapGetters(["userInfo"])
    },
    created() {
        this.refresh()
    },
    methods: {
        saveHandle(row, done) {
            SaveUser(row).then(() => {
                done();
                this.refresh();
            })
        },
        updateHandle(row, index, done) {
            SaveUser(row).then(() => {
                done();
                this.refresh();
            })
        },
        deleteHandle(row) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                DeleteUser({id: row.id}).then(() => {
                    this.refresh();
                })
            }).catch(() => {
                console.log('已取消删除操作')
            });
        },
        // 列表查询
        onLoad(param) {
            this.tableLoading = true
            ListUser(param).then(res => {
                this.tableLoading = false
                if (res.data.status) {
                    const data = res.data.data;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.pageList = data.records;
                }
            })
        },
        sizeChange(val) {
            this.page.currentPage = 1;
            this.page.pageSize = val;
            this.onLoad(this.pageParam())
        },
        currentChange(val) {
            this.page.currentPage = val;
            this.onLoad(this.pageParam())
        },
        pageParam() {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize
            }
        },
        searchChange(param, done) {
            let pageParam = this.pageParam()
            pageParam.param = param;
            this.onLoad(pageParam)
            done()
        },
        refresh() {
            this.onLoad(this.pageParam());
        },
        openMessage() {
            this.messageDialog = true;
        },
        doMessageSend() {
            this.$refs.messageForm.validate((valid) => {
                if (valid) {
                    // alert('submit!');
                    this.messageAlert = null;
                    this.messageFormSubmitLoading = true;
                    SendMessage(this.messageForm).then(res => {
                        let data = res.data;
                        if (!data.status) {
                            this.messageAlert = data.msg;
                        }else {
                            this.messageAlert = "发送成功";
                        }
                    }).finally(() => this.messageFormSubmitLoading = false);
                } else {
                    // console.log('error submit!!');
                    return false;
                }
            });
        },
        handleImport() {
            this.excelBox = true;
        },
        uploadAfter(res, done, loading) {
            console.log(res);
            this.excelBox = false;
            done();
            // if (res === "success") {
            //     this.excelBox = false;
            //     this.refresh();
            //     done();
            // } else if (res === undefined) {
            //     this.$message.error("上传内容格式有误！");
            //     loading();
            // } else {
            //     this.$message.warning("请上传 .xls,.xlsx 标准格式文件");
            //     loading();
            // }
        },
        handleTemplate() {
            window.open(`/platform/qywx/user/downloadImportTemplate`);
        },
    }
}
</script>
