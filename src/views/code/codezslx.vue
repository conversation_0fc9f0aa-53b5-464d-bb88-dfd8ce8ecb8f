<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @refresh-change="refresh"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.codezslx_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   size="mini"
                   icon="el-icon-upload2"
                   @click="handleImport">导入
        </el-button>
        <el-button type="success"
                   size="mini"
                   icon="el-icon-download"
                   @click="handleExport">导出
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="数据导入"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
      <avue-form :option="excelOption"
                 v-model="excelForm"
                 :upload-after="uploadAfter">
        <template slot="menuForm">
          <el-button type="primary" @click="handleTemplate()">
            点击下载模板<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, exportDataAsync, getDetail, getList, remove, update} from "@/api/code/codezslx";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        excelBox: false,
        excelForm: {},
        excelOption: {
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "文件上传",
              prop: "file",
              type: "upload",
              drag: true,
              loadText: "文件上传中，请稍等",
              span: 24,
              propsHttp: {
                res: "info",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/code/codezslx/import",
            },
          ],
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "年份",
              prop: "nf",
              search:true,
              addDisplay: false,
              editDisplay: false,
              type: 'select',
              props: {
                label: 'nf',
                value: 'nf'
              },
              dicUrl: '/ksgl/tdd/allnf',
              rules: [{
                required: true,
                message: "请输入年份",
                trigger: "blur"
              }]
            },
            {
              label: "层次",
              prop: "cc",
              type: "select",
              dicMethod: "get",
              dicUrl: '/code/ccdm/all',
              props: {
                label: "name",
                value: "name",
              },
              rules: [{
                required: true,
                message: "请选择层次",
                trigger: "blur"
              }]
            },
            {
              label: "招生类型",
              prop: "zslx",
              rules: [{
                required: true,
                message: "请输入招生类型",
                trigger: "blur"
              }]
            },
            {
              label: "录取数",
              prop: "lqs",
              addDisplay: false,
              editDisplay: false,
              rules: [{
                required: true,
                message: "请输入录取数",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.codezslx_add, false),
          viewBtn: this.vaildData(this.permission.codezslx_view, false),
          delBtn: this.vaildData(this.permission.codezslx_delete, false),
          editBtn: this.vaildData(this.permission.codezslx_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh() {
        this.onLoad(this.page);
      },
      handleImport() {
        this.excelBox = true;
      },

      uploadAfter(res, done, loading) {
        console.log('uploadAfter',res)
        if (res === "success") {
          this.excelBox = false;
          this.refresh();
          done();
        } else if (res === undefined) {
          this.$message.error("上传内容格式有误！");
          loading();
        } else {
          this.$message.warning("上传内容格式有误！");
          loading();
        }
      },
      handleTemplate() {
        window.open(`/code/codezslx/downImportTemplate`);
      },
      handleExport() {
        const context = this.query;
        exportDataAsync(context).then((res) => {})
      },
    }
  };
</script>

<style>
</style>
