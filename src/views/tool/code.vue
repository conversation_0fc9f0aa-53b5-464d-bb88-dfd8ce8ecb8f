<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 :permission="permissionList"
                 @size-change="sizeChange"
                 @selection-change="selectionChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">

        <template slot="menuLeft">
          <el-button type="danger"
                     size="small"
                     icon="el-icon-delete"
                     plain
                     @click="handleDelete">删 除
          </el-button>
          <el-button type="primary"
                     size="small"
                     plain
                     icon="el-icon-refresh"
                     @click="handleBuild">代码生成
          </el-button>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button type="text"
                     size="small"
                     icon="el-icon-document-copy"
                     @click.stop="handleCopy(scope.row)">复制
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {build, copy, edit, getList, remove} from "@/api/tool/code";

export default {
  inject: ["reload"],
  data() {
    return {
      form: {},
      dataLink: [],
      tableLoading: false,
      dictList: [],
      pageList: [],
      selectionList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 880,
        selection: true,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 150,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: "数据源",
            prop: "datasourceId",
            search: true,
            span: 24,
            type: "select",
            dicUrl: "/syt/datasource/select",
            props: {
              label: "name",
              value: "id"
            },
            rules: [{
              required: true,
              message: "请选择数据源",
              trigger: "blur"
            }]
          },
          {
            label: "模块名",
            prop: "codeName",
            search: true,
            rules: [{
              required: true,
              message: "请输入模块名",
              trigger: "blur"
            }]
          },
          {
            label: "服务名",
            prop: "serviceName",
            search: true,
            rules: [{
              required: true,
              message: "请输入服务名",
              trigger: "blur"
            }]
          },
          {
            label: "表名",
            prop: "tableName",
            rules: [{
              required: true,
              message: "请输入表名",
              trigger: "blur"
            }]
          },
          {
            label: "表前缀",
            prop: "tablePrefix",
            hide: true,
            rules: [{
              required: true,
              message: "请输入表前缀",
              trigger: "blur"
            }]
          },
          {
            label: "主键名",
            prop: "pkName",
            hide: true,
            rules: [{
              required: true,
              message: "请输入主键名",
              trigger: "blur"
            }]
          },
          {
            label: "包名",
            prop: "packageName",
            overHidden: true,
            rules: [{
              required: true,
              message: "请输入包名",
              trigger: "blur"
            }]
          },
          {
            label: "基础业务",
            prop: "baseMode",
            type: 'radio',
            dicData: [
              {
                label: "否",
                value: 1
              },
              {
                label: "是",
                value: 2
              }
            ],
            hide: true,
            rules: [{
              required: true,
              message: "请选择基础业务",
              trigger: "blur"
            }]
          },
          {
            label: "包装器",
            prop: "wrapMode",
            type: 'radio',
            dicData: [
              {
                label: "否",
                value: 1
              },
              {
                label: "是",
                value: 2
              }
            ],
            hide: true,
            rules: [{
              required: true,
              message: "请选择包装器",
              trigger: "blur"
            }]
          },
          {
            label: "后端生成路径",
            prop: "apiPath",
            span: 24,
            hide: true,
            rules: [{
              required: true,
              message: "请输入后端生成路径",
              trigger: "blur"
            }]
          },
          {
            label: "前端生成路径",
            prop: "webPath",
            span: 24,
            hide: true,
            rules: [{
              required: true,
              message: "请输入前端生成路径",
              trigger: "blur"
            }]
          }
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.dataSource_add, false),
        // viewBtn: this.vaildData(this.permission.dataSource_view, false),
        // delBtn: this.vaildData(this.permission.dataSource_delete, false),
        // editBtn: this.vaildData(this.permission.dataSource_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.onLoad(this.pageParam());
    // this.initDict();
  },
  methods: {
    beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
      } else {
      }
      done();
    },
    saveHandle(row, done, loading) {

      edit(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        done();
      }).catch(res => {
        loading();
        this.$message.error(res.info);
      });
    },
    // 编辑
    editHandle(row, done, loading) {
      edit(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
      }).catch(res => {
        done();
        this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        remove({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove({"id": this.ids});
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      getList(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
    },
    initDict() {
      GetListDict().then(res => {
        this.dictList = res.data;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
    handleBuild() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("是否生成选中模块的代码?", {
        title: "代码生成确认",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return build(this.ids);
          })
          .then(() => {
            this.onLoad(this.pageParam());
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
    },
    handleCopy(row) {
      copy(row.id).then(() => {
        this.onLoad(this.pageParam());
        this.$message({
          type: "success",
          message: "复制成功!"
        });
      });
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
</style>
