<template>
    <div class="wrap">
        <div class="title">
            <div style="display: flex;">
                <div style="height: 30px;line-height: 30px;">
                    <span style="font-size: 14px;font-weight: bold;">
                        年份:
                    </span>
                    <el-select v-model="nfvalue" size="mini" @change="nfchange" style="margin-left: 5px;" placeholder="请选择">
                        <el-option
                            v-for="item in nfoptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div style="height: 30px;line-height: 30px;margin-left: 20px;">
                    <span style="font-size: 14px;font-weight: bold;">
                        招生类型:
                    </span>
                    <el-select v-model="zslxvalue" multiple size="mini" @change="nfchange" style="margin-left: 5px;" placeholder="请选择">
                        <el-option
                            v-for="item in zslxoptions"
                            :key="item.id"
                            :label="item.zslx"
                            :value="item.zslx">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div>
                <div style="font-size: 14px;height: 40px;line-height: 40px;font-weight: bold;">
                    统计维度:
                </div>
                <div style="margin-left: 20px;">
                    <el-checkbox :indeterminate="isIndeterminate" v-model="checktjwdAll" @change="handleCheckTjwdAllChange">全选</el-checkbox>
                    <div style="margin: 15px 0;"></div>
                    <el-checkbox-group v-model="checkedtjwd" @change="handleCheckedTjwdChange">
                        <el-checkbox v-for="(item,index) in tjwdtions" :label="item.label" :key="index">{{item.label}}</el-checkbox>
                    </el-checkbox-group>
                    <div style="font-size: 14px;height: 40px;line-height: 40px;">
                        统计顺序：{{checkedtjwd.join('_')}}
                    </div>
                </div>
                <div style="font-size: 14px;height: 40px;line-height: 40px;font-weight: bold;">
                    统计内容:
                </div>
                <div style="margin-left: 20px;">
                    <el-checkbox :indeterminate="isIndeterminatetjnr" v-model="checktjnrAll" @change="handleCheckTjnrAllChange">全选</el-checkbox>
                    <div style="margin: 15px 0;"></div>
                    <el-checkbox-group v-model="checkedtjnr" @change="handleCheckedTjnrChange">
                        <el-checkbox v-for="(item,index) in tjnrtions" :label="item.label" :key="index">{{item.label}}</el-checkbox>
                    </el-checkbox-group>
                    <div style="font-size: 14px;height: 40px;line-height: 40px;">
                        统计顺序：{{checkedtjnr.join('_')}}
                    </div>
                </div>
            </div>
            <div style="display: flex;justify-content: end;padding: 15px 10px;">
                <el-button type="primary" @click="handleClcik" >确定</el-button>
            </div>
        </div>
        <div v-if="data.length > 0" class="content">
            <div style="width: 100%;height: 100%;background-color: #fff;padding: 0px 15px;">
                <el-button size="small" type="primary" @click="handledc" style="margin-top: 10px;margin-bottom: 10px;">导出</el-button>
                <avue-crud
                    ref="crud"
                    :data="data"
                    :option="crudoption"
                    :page.sync="page"
                    @selection-change="selectionChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                >
                </avue-crud>
            </div>
        </div>
    </div>
</template>

<script>
import {mapGetters} from "vuex";
import {getList, statexport, sytSysDict, tjlistpage} from "@/api/code/codezslx";
// import {deepClone} from "@/util/util";
import FileSaver from 'file-saver';

export default {
    components:{

    },
    data() {
        return {
            nfoptions: [],
            zslxoptions: [],
            tjwdtions: [],
            checkedtjwd: [],
            checkedtjwdval: [],
            checktjwdAll: false,
            tjnrtions: [],
            checkedtjnr: [],
            checkedtjnrval: [],
            checktjnrAll: false,
            direction: 'rtl',
            nfvalue: 2024,
            zslxvalue: null,
            isIndeterminate: true,
            isIndeterminatetjnr: true,
            type: {},
            params:{},
            tabtype: {},
            datalist: [],
            data: [],
            crudoptioncolumn: [],
            page: {
                currentPage: 1,
                total: 5,
                pageSize: 7,
            },
        };
    },
    created() {
        const now = new Date();
        const currentYear = now.getFullYear();
        const specifiedNumber = currentYear;
        this.nfoptions = this.generateNumberArray(specifiedNumber);
        this.getData();
        this.getsytSysDict();
    },
    computed: {
        ...mapGetters(["permission"]),
        crudoption(){
            return {
                indexTitle: '序号',
                menuAlign: "center",
                sizeValue: 'mini',
                align: 'center',
                indexWidth: 30,
                selection: false,
                tip: false,
                stripe: true,
                delBtn: false,
                header: false,
                editBtn: false,
                addBtn: false,
                searchBtn: false,
                emptyBtn: false,
                refreshBtn: false,
                searchShowBtn: false,
                columnBtn: false,
                menu: false,
                column: this.crudoptioncolumn,
            }
        }
    },
    methods: {
        onLoad(page, params) {
            this.loading = true;
            tjlistpage(page.currentPage, page.pageSize, Object.assign(params, {})).then(res => {
                const data = res.data.data;
                this.data = data.records;
                this.page.total = data.total;
                this.loading = false;
            });
        },
        handleClcik(){
            // this.crudoptioncolumn = [];
            let columnArray = [];
            this.checkedtjwdval.forEach((item)=>{
                columnArray.push({
                    label: item.label,
                    prop: item.value,
                })
            })
            this.checkedtjnrval.forEach((item)=>{
                if(item.extend){
                    let children = [];
                    let extendlist = item.extend.split('#');
                    let extendval = item.value.split('#');
                    extendlist.forEach((ex,i)=>{
                        children.push(
                            {
                                label: ex,
                                prop: extendval[i],
                            }
                        )
                    })
                    columnArray.push(
                        {
                            label: item.label,
                            children: children,
                        }
                    )
                }else {
                    columnArray.push(
                        {
                            label: item.label,
                            prop: item.value,
                        }
                    )
                }
            })

            let groupByColumns = this.checkedtjwdval;
            // let columns = this.crudoptioncolumn;
            this.crudoptioncolumn = columnArray;
            // console.log(`this.crudoptioncolumn`, this.crudoptioncolumn);
            this.params = {
                year: this.nfvalue,
                zslx: this.zslxvalue,
                groupByColumns: JSON.stringify(groupByColumns),
                columns: JSON.stringify(columnArray),
            }
            this.onLoad(this.page,this.params)
            this.$forceUpdate();
        },
        handledc(){
            let data = this.params;
            statexport(data).then(response => {
                const disposition = response.headers['content-disposition'];
                const encodedFileName = disposition.split(';')[1].trim().split('=')[1];
                const fileName = decodeURIComponent(encodedFileName); // 解码文件名
                // 创建Blob对象
                const blob = new Blob([response.data], { type: 'application/octet-stream' });
                // 使用FileSaver.js保存文件，传入获取到的文件名
                FileSaver.saveAs(blob, fileName);
            });

        },
        handleCheckTjnrAllChange(value){
            // this.checkedtjnr = value ? this.tjnrtions : [];
            this.checkedtjnr = [];
            this.checkedtjnrval = [];
            if(value){
                this.tjnrtions.forEach(item=>{
                    this.checkedtjnr.push(item.label)
                    this.checkedtjnrval.push(item)
                })
            }
            this.isIndeterminatetjnr = false;
        },
        handleCheckedTjnrChange(value){
            let checkedCount = value.length;
            this.checktjnrAll = checkedCount === this.tjnrtions.length;
            this.isIndeterminatetjnr = checkedCount > 0 && checkedCount < this.tjnrtions.length;
            if(value.length>0) {
                this.checkedtjnrval = []
                value.forEach(val=>{
                    this.tjnrtions.forEach(item => {
                        if(item.label == val){
                            this.checkedtjnrval.push(item)
                        }
                    })
                })
            }
        },
        handleCheckTjwdAllChange(value){
            // this.checkedtjwd = value ? this.tjwdtions : [];
            this.checkedtjwd = [];
            this.checkedtjwdval = [];
            if(value){
                this.tjwdtions.forEach(item=>{
                    this.checkedtjwd.push(item.label)
                    this.checkedtjwdval.push(item)

                })
            }
            this.isIndeterminate = false;
        },
        handleCheckedTjwdChange(value){
            let checkedCount = value.length;
            this.checktjwdAll = checkedCount === this.tjwdtions.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.tjwdtions.length;
            if(value.length>0) {
                this.checkedtjwdval = []
                value.forEach(val=>{
                    this.tjwdtions.forEach(item => {
                        if(item.label == val){
                            this.checkedtjwdval.push(item)
                        }
                    })
                })
            }
        },
        nfchange(){
            this.getData();
        },
        getsytSysDict(){
            sytSysDict('CUSSTAT_COLUMN').then(res => {
                const data = res.data;
                this.tjwdtions = [];
                data.forEach(item=>{
                    this.tjwdtions.push({
                        label: item.name,
                        value: item.value
                    })
                })
            });
            sytSysDict('CUSSTAT_CONTENT').then(res => {
                const data = res.data;
                this.tjnrtions = [];
                data.forEach(item=>{
                    this.tjnrtions.push({
                        label: item.name,
                        value: item.value,
                        extend: item.extend
                    })
                })
            });
        },
        getData(){
            getList(1, 99,{nf:this.nfvalue}).then(res => {
                const data = res.data.data;
                this.zslxoptions = data.records;
            });
        },
        generateNumberArray(num) {
            const result = [];
            for (let i = num; i > num -10; i--) {
                result.push({
                    label: i,
                    value: i,
                });
            }

            return result;
        },

        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page,this.params);
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page,this.params);
        },
        pageParam(queryParam) {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize,
                queryParam: queryParam
            }
        },
        selectionChange(list) {
            this.listhumancode = list;
        },
    }
};
</script>

<style>
.title{
    width: 98%;
    /*height: 100%;*/
    margin: auto;
    padding: 15px;
    background-color: #fff;
}
.wrap{
    height: 100%;
    width: 100%;
    /*border: 1px solid #00a5ec;*/
}
.content{
    width: 100%;
    padding: 15px;
}
.crudbox{
    height: 150px;
    width: 260px;
    border: 1px solid #ECECEC;
    border-radius: 3px;
    position: relative;
    margin-bottom: 10px;
    margin-right: 10px;
}
.crudtitle{
    height: 70px;
    line-height: 70px;
    font-size: 18px;
    font-weight: bold;
    margin-left: 20px;
}
.crudtip{
    height: 20px;
    width: 60px;
    background-color: #448EF7;
    position: absolute;
    right: 0;
    top: 20px;
    color: #fff;
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    border-radius: 10px 0 0 10px;
}
.crudcontent{
    height: 50px;
    margin-top: 10px;
    width: 100%;
    display: flex;
}
.dialog-footer{
    text-align: center;
}
.my-html-element{
    color: red;
}
</style>
