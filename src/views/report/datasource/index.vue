<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 :permission="permissionList"
                 @size-change="sizeChange"
                 @selection-change="selectionChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
        <template slot-scope="{row,index,type}" slot="menuForm">
          <el-button type="primary" icon="el-icon-check" size="small" plain @click="testConnect">测试
          </el-button>
        </template>
<!--        <template slot="menuLeft">
          <el-button type="danger"
                     size="small"
                     icon="el-icon-delete"
                     plain
                     v-if="permission.dataSource_delete"
                     @click="handleDelete">删 除
          </el-button>
        </template>-->
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {GetListDict} from "@/api/sysDict";
import {DeleteDataSource, EditDataSource, ListDataSource, testConnection} from "@/api/dataSource";
import {toBase64} from "js-base64";

export default {
  inject: ["reload"],
  data() {
    return {
      form: {},
      dataLink: [],
      tableLoading: false,
      dictList: [],
      pageList: [],
      selectionList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 580,
        selection: true,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 150,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: '名称',
            prop: "sourceName",
            search: true,
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          }, {
            label: '类型',
            prop: "sourceType",
            type: "select",
            dicMethod: "post",
            dicUrl: '/sytSysDict/list?code=SOURCE_TYPE',
            props: {
              label: "name",
              value: "value",
            },
            rules: [{required: true, message: "请选择类型", trigger: blur}],
            change: ({value, column}) => {
              let self = this;
              let column2 = self.optionDetail.group[0].column;
              column2.splice(0, column2.length);
              self.dictList.forEach(dict => {
                if (value === dict.value) {
                  let dictArr = JSON.parse(dict.extend);
                  self.dataLink = dictArr;
                  dictArr.forEach(d => {
                    if (d.label) {
                      let col = {};
                      if (d.type) {
                        col.type = d.type;
                        col.dicData = d.dicData;
                      }
                      col.label = d.label;
                      col.prop = d.labelValue;
                      self.form[col.prop] = self.form[col.prop] ? self.form[col.prop] : d.value;
                      col.span = 24;
                      column2.push(col)
                    }
                  })
                }
              });
            },
          },
          /*{
              label: '编号',
              prop: "sourceCode",
              search: true,
              rules: [{required: true, message: "请输入编号", trigger: blur}]
          },*/
          {
            label: '描述',
            prop: "sourceDesc",
            type: "textarea",
            span: 24,
          },
          {
            label: '创建人',
            prop: "createUser",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '创建时间',
            prop: "createTime",
            addDisplay: false,
            editDisplay: false,
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
          },
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.dataSource_add, false),
        // viewBtn: this.vaildData(this.permission.dataSource_view, false),
        // delBtn: this.vaildData(this.permission.dataSource_delete, false),
        // editBtn: this.vaildData(this.permission.dataSource_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.onLoad(this.pageParam());
    this.initDict();
  },
  methods: {
    beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
        let obj = JSON.parse(this.form.sourceConfig);
        this.dataLink = obj;
        for (var objKey in obj) {
          this.form[objKey] = obj[objKey];
        }
      } else {
      }
      done();
    },
    saveHandle(row, done, loading) {
      const newList = {};
      this.dataLink.forEach((item) => {
        newList[item.labelValue] = row[item.labelValue]
      })
      row.sourceConfig = JSON.stringify(newList);
      row.sourceConfig = toBase64(row.sourceConfig);
      row.jdbcUrl = toBase64(row.jdbcUrl);
      row.username = toBase64(row.username);
      row.password = toBase64(row.password);
      row.driverName = toBase64(row.driverName);

      EditDataSource(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        done();
      }).catch(res => {
        loading();
        this.$message.error(res.info);
      });
    },
    // 编辑
    editHandle(row, done, loading) {
      const newList = {};
      this.dataLink.forEach((item) => {
        newList[item.labelValue] = row[item.labelValue]
      })
      row.sourceConfig = JSON.stringify(newList);
      row.sourceConfig = toBase64(row.sourceConfig);
      row.jdbcUrl = toBase64(row.jdbcUrl);
      row.username = toBase64(row.username);
      row.password = toBase64(row.password);
      row.driverName = toBase64(row.driverName);

      EditDataSource(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
      }).catch(res => {
        done();
        this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteDataSource({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return DeleteDataSource({"id": this.ids});
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      ListDataSource(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
    },
    initDict() {
      GetListDict().then(res => {
        this.dictList = res.data;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
    testConnect() {
      const obj = this.form;
      const newList = {};
      this.dataLink.forEach((item) => {
        newList[item.labelValue] = obj[item.labelValue]
      })
      let param = this.form;
      param.sourceConfig = JSON.stringify(newList);
      param.sourceConfig = toBase64(param.sourceConfig);
      param.jdbcUrl = toBase64(param.jdbcUrl);
      param.username = toBase64(param.username);
      param.password = toBase64(param.password);
      param.driverName = toBase64(param.driverName);
      testConnection(param).then((res) => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "测试成功"});
        } else {
          this.$message({type: "error", message: "数据源链接失败"});
        }
      })
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
</style>
