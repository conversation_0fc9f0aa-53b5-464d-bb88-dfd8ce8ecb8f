<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
        <template slot="view" slot-scope="scope">
          <el-button @click="isShowCaseResult(scope.row)">查看</el-button>
        </template>
        <template slot="dynSentenceForm" slot-scope="scope">
          <div class="codemirror" style="height: 150px;overflow: scroll;">
            <!--                        <codemirror v-model.trim="form.dynSentence" :options="optionsSql"/>-->
            <monaco-editor
                v-model.trim="form.dynSentence"
                language="sql"
                style="height: 500px"
            />
          </div>
        </template>
        <template slot="optForm" slot-scope="scope">
          <el-tabs v-model.trim="tabsActiveName" type="card" @tab-click="handleClickTabs" style="width: 1050px">
            <!--            <el-tab-pane label="查询参数" name="first">
                          <el-button v-if="dataSetParamDtoList.length == 0" type="text" size="small" @click="addRow()">添加</el-button>
                          <el-table :data="dataSetParamDtoList" border style="width: 100%">
                            <el-table-column align="center" label="序号" type="index" min-width="80"/>
                            <el-table-column label="参数名">
                              <template slot-scope="scope">
                                <el-input v-model.trim="dataSetParamDtoList[scope.$index].paramName"
                                          :disabled="dataSetParamDtoList[scope.$index].paramName == 'pageSize' || dataSetParamDtoList[scope.$index].paramName == 'pageNumber'"/>
                              </template>
                            </el-table-column>
                            <el-table-column label="描述" align="center">
                              <template slot-scope="scope">
                                <el-input v-model.trim="dataSetParamDtoList[scope.$index].paramDesc"/>
                              </template>
                            </el-table-column>
                            <el-table-column label="数据类型">
                              <template slot-scope="scope">
                                <el-input v-model.trim="dataSetParamDtoList[scope.$index].paramType"/>
                              </template>
                            </el-table-column>
                            <el-table-column label="示例值">
                              <template slot-scope="scope">
                                <el-input v-model.trim="dataSetParamDtoList[scope.$index].sampleItem"/>
                              </template>
                            </el-table-column>
                            <el-table-column label="校验" width="220">
                              <template slot-scope="scope">
                                <el-checkbox v-model="dataSetParamDtoList[scope.$index].mandatory" @change="Mandatory(scope.$index)">必选
                                </el-checkbox>
                                <el-button type="primary" icon="el-icon-plus" @click="permissionClick(scope.row, scope.$index)">
                                  高级规则
                                </el-button>
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" width="200" align="center">
                              <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    size="small"
                                    @click.native.prevent="
                                        cutOutRow(scope.$index, dataSetParamDtoList)
                                      "
                                >删除
                                </el-button>
                                <el-button
                                    type="text"
                                    size="small"
                                    @click="addRow(scope.row)"
                                >追加
                                </el-button>
                              </template>
                            </el-table-column>
                          </el-table>
            &lt;!&ndash;              <el-checkbox v-model="isShowPagination" @change="changePagination">加入分页参数</el-checkbox>&ndash;&gt;
                        </el-tab-pane>-->
            <el-tab-pane label="数据预览(必选)" name="first">
              <div style="max-height: 400px; overflow: auto;">
                <vue-json-editor v-model="cols" :show-btns="false" :mode="'code'" lang="zh" @json-change="onJsonChange"
                                 @json-save="onJsonSave"/>
              </div>
            </el-tab-pane>
            <el-tab-pane label="字段设置" name="second">
              <div style="max-height: 400px; overflow: auto;">
                <p>说明：上下拖拽行实现字段排序,点击行编辑</p>
                <avue-crud class="fieldConfig" ref="crud1" :data="previewData" :option="previewOption"
                           @sortable-change="sortableChange" @row-click="handleRowClick"></avue-crud>
              </div>
            </el-tab-pane>
            <el-tab-pane label="下钻参数" name="third">
              <el-button v-if="dataSetDrillParamDtoList.length == 0" type="text" size="small" @click="addDrillRow()">
                添加
              </el-button>
              <el-table :data="dataSetDrillParamDtoList" border style="width: 100%">
                <el-table-column align="center" label="序号" type="index" width="80"/>
                <el-table-column label="参数名" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.trim="dataSetDrillParamDtoList[scope.$index].paramName"
                              :disabled="dataSetDrillParamDtoList[scope.$index].paramName == 'pageSize' || dataSetDrillParamDtoList[scope.$index].paramName == 'pageNumber'"/>
                  </template>
                </el-table-column>
                <el-table-column label="描述" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.trim="dataSetDrillParamDtoList[scope.$index].paramDesc"/>
                  </template>
                </el-table-column>
                <!--                <el-table-column label="数据类型">
                                  <template slot-scope="scope">
                                    <el-input v-model.trim="dataSetDrillParamDtoList[scope.$index].paramType"/>
                                  </template>
                                </el-table-column>-->
                <el-table-column label="示例值" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.trim="dataSetDrillParamDtoList[scope.$index].sampleItem"/>
                  </template>
                </el-table-column>
                <!--                <el-table-column label="校验" width="220">
                                  <template slot-scope="scope">
                                    <el-checkbox v-model="dataSetDrillParamDtoList[scope.$index].mandatory" @change="Mandatory(scope.$index)">必选
                                    </el-checkbox>
                                    <el-button type="primary" icon="el-icon-plus" @click="permissionClick(scope.row, scope.$index)">
                                      高级规则
                                    </el-button>
                                  </template>
                                </el-table-column>-->
                <el-table-column label="操作" width="200" align="center">
                  <template slot-scope="scope">
                    <el-button
                        type="text"
                        size="small"
                        @click.native.prevent="
                            cutOutDrillRow(scope.$index, dataSetDrillParamDtoList)
                          "
                    >删除
                    </el-button>
                    <el-button
                        type="text"
                        size="small"
                        @click="addDrillRow(scope.row)"
                    >追加
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </template>
        <template slot="setTypeForm" slot-scope="scope">
          <el-select v-model="form.setType" filterable placeholder="请选择" @blur="setTypeBlur">
            <el-option
                v-for="item in setTypes"
                :key="item.SETTYPE"
                :label="item.SETTYPE"
                :value="item.SETTYPE"
            ></el-option>
          </el-select>
        </template>
      </avue-crud>
    </basic-container>
    <el-dialog :title="caseResultTitle" :modal="false" :visible.sync="dialogCaseResult" width="30%">
      <vue-json-editor v-model="caseResultContent" :show-btns="false" :mode="'code'" lang="zh" class="my-editor"
                       @json-change="onJsonChange" @json-save="onJsonSave"/>
      <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogCaseResult = false">关闭</el-button>
            </span>
    </el-dialog>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteDataSet, EditDataSet, ListDataSet, ListSetType, testTransformSet} from "@/api/dataSet";
import {codemirror} from 'vue-codemirror' // 引入codeMirror全局实例
import 'codemirror/mode/sql/sql.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/lib/codemirror.css' // 核心样式
import 'codemirror/theme/cobalt.css' // 引入主题后还需要在 options 中指定主题才会生效
import vueJsonEditor from 'vue-json-editor'
import MonacoEditor from "@/components/MonacoEditor";
import {GetDataSource} from "@/api/dataSource";
import {toBase64} from "js-base64";

export default {
  inject: ["reload"],
  components: {codemirror, vueJsonEditor, MonacoEditor},
  data() {
    return {
      form: {},
      tableLoading: false,
      pageList: [],
      page: {
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      tabsActiveName: 'first',
      isItemFilterType: '', // 选中的转换类型id
      dataSetTransformDtoList: [
        {
          transformType: 'js',
          transformScript: `function dataTransform(data){
                      //自定义脚本内容
                      return data;
                    }`,
        },
      ],
      transformScript: `function dataTransform(data){
              //自定义脚本内容
              return data;
            }`,
      itemFilterScriptId: '',
      setTypes: [],
      cols: [],
      dialogCaseResult: false,
      caseResultTitle: '',
      caseResultContent: null,
      testMassageCode: null,
      dataSetParamDtoList: [],//查询参数table
      dataSetDrillParamDtoList: [],//下钻参数table
      optionsSql: {
        mode: 'text/x-sql',
        tabSize: 2, // 缩进格式
        // theme: 'cobalt', // monokai主题，对应主题库 JS 需要提前引入
        lineNumbers: true, // 显示行号
        line: true,
        styleActiveLine: true, // 高亮选中行
        hintOptions: {
          completeSingle: true, // 当匹配只有一项的时候是否自动补全
        },
      },
      /*optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 1200,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 150,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: '分类',
            prop: "setType",
            align: "center",
            search: true,
            filterable: true,
            formslot: true,
            rules: [{required: true, message: "请选择或输入分类", trigger: blur}],
          },
          {
            label: '数据源',
            prop: "sourceCode",
            search: true,
            type: "select",
            dicMethod: "post",
            dicUrl: '/report/dataSource/list',
            props: {
              label: "sourceName",
              value: "id",
            },
            change: ({value, column}) => {
              if (value) {
                GetDataSource({id:value}).then(res=>{
                  console.log(this.optionDetail.column);
                  let o = res.data.info;

                  let tableAlias = this.findObject(this.optionDetail.column, "tableAlias");
                  let filterField = this.findObject(this.optionDetail.column, "filterField");
                  if ('http' === o.sourceType) {
                    tableAlias.addDisplay = false;
                    tableAlias.editDisplay = false;
                    filterField.addDisplay = false;
                    filterField.editDisplay = false;
                  } else {

                  }
                })
              }
            }
            // rules: [{required: true, message: "请选择数据源", trigger: blur}]
          },
          {
            label: '名称',
            prop: "setName",
            search: true,
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: '类型',
            prop: "type",
            type: "select",
            width: 60,
            align: "center",
            search: true,
            filterable: true,
            dicData: [
              {label: "图表", value: "图表"},
              {label: "下钻", value: "下钻"},
              {label: "筛选", value: "筛选"},
            ],
            rules: [{required: true, message: "请选择类型", trigger: blur}],
            hide: true
          },
          {
            label: '主表别名',
            prop: "tableAlias",
            hide: true,
            // search: true,
            // rules: [{required: true, message: "请输入主表别名", trigger: blur}]
          },
          {
            label: '筛选字段',
            prop: "filterField",
            hide: true,
            // search: true,
            // rules: [{required: true, message: "请输入筛选字段", trigger: blur}]
          },
          {
            label: '描述',
            prop: "setDesc",
            type: "textarea",
            span: 24,
          },
          {
            label: '查看',
            prop: "view",
            slot: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '操作人',
            prop: "createUser",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '操作时间',
            prop: "createTime",
            addDisplay: false,
            editDisplay: false,
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: '查询SQL或请求体',
            prop: "dynSentence",
            type: "textarea",
            span: 24,
            formslot: true,
            hide: true,
            rules: [{required: true, message: "请输入查询SQL或请求体", trigger: blur}]
          },
          {
            label: '操作',
            prop: "opt",
            formslot: true,
            hide: true,
          },
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      },*/
      previewData: [],
      previewOption: {
        sortable: true,
        menu: false,
        cellBtn: false,
        columnBtn: false,
        cancelBtn: false,
        refreshBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '列名',
            prop: 'field',
          },
          {
            label: '搜索条件',
            prop: 'search',
            type: "radio",
            dicData: [
              {
                label: "否",
                value: "否"
              },
              {
                label: "是",
                value: "是"
              }
            ],
            cell: true
          }, {
            label: '列排序',
            prop: 'sortable',
            type: "radio",
            dicData: [
              {
                label: "否",
                value: "否"
              },
              {
                label: "是",
                value: "是"
              }
            ],
            cell: true
          }, {
            label: '列隐藏',
            prop: 'hide',
            type: "radio",
            dicData: [
              {
                label: "否",
                value: "否"
              },
              {
                label: "是",
                value: "是"
              }
            ],
            cell: true
          },
          /*         {
                     label:'关联下钻类型',
                     prop:'linkdrill',
                     type: "radio",
                     dicData: [
                       {
                         label: "否",
                         value: "否"
                       },
                       {
                         label: "是",
                         value: "是"
                       }
                     ],
                     cell:true
                   },*/
        ]
      },
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    optionDetail(){
      return {
        index: true,
        size: "mini",
        dialogWidth: 1200,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 150,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: '分类',
            prop: "setType",
            align: "center",
            search: true,
            filterable: true,
            formslot: true,
            rules: [{required: true, message: "请选择或输入分类", trigger: blur}],
          },
          {
            label: '名称',
            prop: "setName",
            search: true,
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: '类型',
            prop: "type",
            type: "select",
            width: 60,
            align: "center",
            search: true,
            filterable: true,
            dicData: [
              {label: "图表", value: "图表"},
              {label: "下钻", value: "下钻"},
              {label: "筛选", value: "筛选"},
            ],
            rules: [{required: true, message: "请选择类型", trigger: blur}],
            hide: true
          },
          {
            label: '数据源',
            prop: "sourceCode",
            search: true,
            type: "select",
            dicMethod: "post",
            dicUrl: '/report/dataSource/list',
            props: {
              label: "sourceName",
              value: "id",
            },
            change: ({value, column}) => {
              if (value) {
                GetDataSource({id:value}).then(res=>{
                  console.log(this.optionDetail.column);
                  let o = res.data.info;
                  let tableAlias = this.findObject(this.optionDetail.column, "tableAlias");
                  let filterField = this.findObject(this.optionDetail.column, "filterField");
                  let apiUrl = this.findObject(this.optionDetail.column, "apiUrl");
                  if ('http' === o.sourceType) {
                    tableAlias.addDisplay = false;
                    tableAlias.editDisplay = false;
                    filterField.addDisplay = false;
                    filterField.editDisplay = false;
                    apiUrl.addDisplay = true;
                    apiUrl.editDisplay = true;
                  } else {
                    tableAlias.addDisplay = true;
                    tableAlias.editDisplay = true;
                    filterField.addDisplay = true;
                    filterField.editDisplay = true;
                    apiUrl.addDisplay = false;
                    apiUrl.editDisplay = false;
                  }
                })
              }
            }
            // rules: [{required: true, message: "请选择数据源", trigger: blur}]
          },
          {
            label: '主表别名',
            prop: "tableAlias",
            hide: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '筛选字段',
            prop: "filterField",
            hide: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '接口地址',
            prop: "apiUrl",
            hide: true,
            span: 24,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '描述',
            prop: "setDesc",
            type: "textarea",
            span: 24,
          },
          {
            label: '查看',
            prop: "view",
            slot: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '操作人',
            prop: "createUser",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '操作时间',
            prop: "createTime",
            addDisplay: false,
            editDisplay: false,
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
          },
          {
            label: '查询SQL或请求体',
            prop: "dynSentence",
            type: "textarea",
            span: 24,
            formslot: true,
            hide: true,
            rules: [{required: true, message: "请输入查询SQL或请求体", trigger: blur}]
          },
          {
            label: '操作',
            prop: "opt",
            formslot: true,
            hide: true,
          },
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.pageParam());
  },
  methods: {
    sortableChange(oldindex, newindex, row, list) {
      this.previewData = []
      this.$nextTick(() => {
        this.previewData = list;
      })
    },
    handleRowClick(row, event, column) {
      row.$cellEdit = true
    },
    setTypeBlur(e) {
      this.$set(this.form, "setType", e.target.value)
    },
    isShowCaseResult(item) {
      this.dialogCaseResult = true
      this.caseResultTitle = item.setName
      this.caseResultContent = JSON.parse(item.caseResult)
    },
    handleClickTabs(tab, event) {
      // this.$refs['crud'].validate((valid) => {
      //   if (valid) {
      let self = this;
      if (tab.paneName === 'first') {
        const params = {
          sourceCode: this.form.sourceCode,
          dynSentence: toBase64(this.form.dynSentence),
          dataSetParamDtoList: this.dataSetParamDtoList,
          dataSetDrillParamDtoList: this.dataSetDrillParamDtoList,
          dataSetTransformDtoList: this.dataSetTransformDtoList,
          tableAlias: this.form.tableAlias,
          filterField: this.form.filterField,
        }
        if (params.dynSentence && params.dynSentence !== '') {
          testTransformSet(params).then(res => {
            if (res.data.code === '00000') {
              this.$message({type: "success", message: "操作成功"});
              // this.cols = res.data.info.data;
              var arr = [];
              if (res.data.info.data.length > 5) {
                arr = res.data.info.data.slice(0, 5);
              } else {
                arr = res.data.info.data;
              }
              self.cols = arr;
              this.testMassageCode = res.data.code;
            }
          }).catch(res => {
            self.cols = [];
            this.$message.error(res.info);
          });
        } else {
          this.$message.error("请输入查询SQL或请求体");
        }
      } else if (tab.paneName === 'second') {
        if (self.cols.length > 0) {
          if (this.previewData.length > 0) {
            console.log(this.compareArrays(this.previewData,Object.keys(self.cols[0])))
            if(this.compareArrays(this.previewData,Object.keys(self.cols[0]))){
              return
            }else {
              this.previewData = []
              let previewArr = [];
              for (let arrKey in self.cols[0]) {
                let ob = {};
                ob.field = arrKey;
                ob.$cellEdit = false;
                previewArr.push(ob);
              }
              this.previewData = previewArr;
            }
            console.log(`this.previewData`,this.previewData)
            // this.compareArrays(this.previewData,Object.keys(self.cols[0]))
            // return
          } else {
            let previewArr = [];
            for (let arrKey in self.cols[0]) {
              let ob = {};
              ob.field = arrKey;
              ob.$cellEdit = false;
              previewArr.push(ob);
            }
            this.previewData = previewArr;
          }

        }
      }
      // }
      // })

    },
    findDuplicates(arr1,arr2) {
      const duplicates = [];

      for (let i = 0; i < arr1.length - 1; i++) {
        for (let j = 0; j < arr2.length; j++) {
          if (arr1[i].field === arr2[j] && !duplicates.includes(arr1[i])) {
            duplicates.push(arr1[i]);
          }
        }
      }

      return duplicates;
    },
    compareArrays(arr1, arr2) {
      if (arr1.length !== arr2.length) {
        return false;
      }

      for (let i = 0; i < arr1.length; i++) {
        if (arr1[i].field !== arr2[i]) {
          return false;
        }
      }

      return true;
    },
    onJsonChange(value) {
    },
    onJsonSave(value) {
    },
    beforeOpen(done, type) {
      if (['view', 'edit'].includes(type)) {
        this.testMassageCode = '00000';
        if (this.form.caseResult) {
          console.log(`JSON.parse(this.form.caseResult)`,JSON.parse(this.form.caseResult))
          this.cols = JSON.parse(this.form.caseResult);
        } else {
          this.cols = [];
        }
        if (this.form.fieldConfig) {

          this.previewData = JSON.parse(this.form.fieldConfig);
        } else {
          this.previewData = [];
        }
        this.dataSetTransformDtoList = this.form.dataSetTransformDtoList;
        this.dataSetParamDtoList = this.form.dataSetParamDtoList ? this.form.dataSetParamDtoList : [];
        this.dataSetDrillParamDtoList = this.form.dataSetDrillParamDtoList;
      } else {
        this.cols = [];
        this.previewData = [];
        this.caseResultContent = [];
        this.dataSetTransformDtoList = [];
        this.dataSetParamDtoList = [];
        this.dataSetDrillParamDtoList = [];
      }
      done();
    },
    saveHandle(row, done, loading) {
      this.editHandle(row, done, loading);
    },
    // 编辑
    editHandle(row, done, loading) {
      row.dataSetTransformDtoList = this.dataSetTransformDtoList;
      row.dataSetParamDtoList = this.dataSetParamDtoList ? this.dataSetParamDtoList : [];
      row.dataSetDrillParamDtoList = this.dataSetDrillParamDtoList;
      row.caseResult = JSON.stringify(this.cols);
      row.fieldConfig = JSON.stringify(this.previewData);
      row.dynSentence = toBase64(row.dynSentence);
      EditDataSet(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
        done();
      }).catch(res => {
        console.log(res.info)
        // this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteDataSet({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      ListDataSet(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
      ListSetType().then(res => {
        this.setTypes = res.data.info;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
    // 必选
    Mandatory(val) {
      if (!this.dataSetParamDtoList[val].mandatory) {
        this.dataSetParamDtoList[val].requiredFlag = 0
      } else {
        this.dataSetParamDtoList[val].requiredFlag = 1
      }
    },
    permissionClick(row, index) {
      this.title = '自定义高级规则'
      if (this.isRowData.sampleItem != '') {
        this.isRowData = row
        const fnCont = `function verification(data){
          //自定义脚本内容
          return true;
        }`
        this.validationRules = row.validationRules
            ? row.validationRules
            : fnCont
        this.dialogPermissionVisible = true
      }
    },
    // 删除
    cutOutRow(index, rows) {
      rows.splice(index, 1);
    },
    // 追加
    addRow(index, row) {
      this.dataSetParamDtoList.push({
        paramName: "",
        paramDesc: "",
        paramType: "",
        sampleItem: "",
        mandatory: true,
        requiredFlag: 1,
        validationRules: `function verification(data){\n\t//自定义脚本内容\n\treturn true;\n}`
      });
    },
    // 删除
    cutOutDrillRow(index, rows) {
      rows.splice(index, 1);
    },
    // 追加
    addDrillRow(index, row) {
      this.dataSetDrillParamDtoList.push({
        paramName: "",
        paramDesc: "",
        paramType: "",
        sampleItem: "",
        mandatory: true,
        requiredFlag: 1,
        validationRules: `function verification(data){\n\t//自定义脚本内容\n\treturn true;\n}`
      });
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

::v-deep .fieldConfig .avue-crud__menu {
  display: none !important;
}

.avue-view {
  height: 100%;
}
</style>
