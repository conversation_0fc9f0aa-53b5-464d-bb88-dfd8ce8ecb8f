<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh"
                 :upload-after="uploadAfter">
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteDecisionItem, EditDecisionItem, ListDecisionItem} from "@/api/decisionItem";
import {GetRoleInfo} from "@/api/settings";

export default {
  inject: ["reload"],
  data() {
    return {
      form: {},
      tableLoading: false,
      pageList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      optionDetail: {
        index: true,
        size: "mini",
        dialogWidth: 580,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 150,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: '链接',
            prop: "url",
            rules: [{required: true, message: "请输入链接", trigger: blur}]
          },
          {
            label: '名称',
            prop: "sourceName",
            search: true,
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: "图标",
            prop: "icon",
            type: "upload",
            listType: "picture-img",
            accept:'image/png, image/jpeg',
            tip: '只能上传jpg/png',
            uploadError: (error, column) => {
              if(error == '文件太大不符合'){
                this.$message.error('图片大小超过限制')
              }
              else{
                this.$message.error(error)
              }
            },
            uploadBefore:(file, done, loading,column) =>{
              const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
              const isFlag =
                  suffixName === "jpg" || suffixName === "jpeg" || suffixName === "png";
              if (!isFlag) {
                this.$message({
                  message: "格式不支持!",
                  type: "error",
                });
                loading()  //阻断上传
              }
              else{
                done()  //继续上传
              }
            },
            span: 24,
            showColumn: false,
            propsHttp: {
              home: window.location.origin,
              res: "info"
            },
            // tip: "只能上传jpg/png用户头像，且不超过500kb",
            action: "/file/upload"
          },
          {
            label: "排序",
            prop: "sort",
            type: "number",
          },
          {
            label: '备注',
            prop: "bz",
            span: 24,
            type: "textarea"
          },
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"])
  },
  created() {
    this.onLoad(this.pageParam());
    this.getRoleInfo();
  },
  methods: {
    getRoleInfo() {
      GetRoleInfo().then(res => {
        this.optionDetail.column.push({
              label: "授权角色",
              prop: "roleId",
              type: "select",
              placeholder: "请选择 授权角色-可多选",
              span: 24,
              drag: true,
              multiple: true,
              dicData: res.data.info,
              props: {
                label: "rolename",
                value: "id"
              }
              // rules: [{ required: true, message: "授权角色不能为空" }]
            },
            {
              label: "缩略图",
              prop: "reportImage",
              type: "upload",
              listType: "picture-img",
              accept:'image/png, image/jpeg',
              tip: '只能上传jpg/png',
              uploadError: (error, column) => {
                if(error == '文件太大不符合'){
                  this.$message.error('图片大小超过限制')
                }
                else{
                  this.$message.error(error)
                }
              },
              uploadBefore:(file, done, loading,column) =>{
                const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
                const isFlag =
                    suffixName === "jpg" || suffixName === "jpeg" || suffixName === "png";
                if (!isFlag) {
                  this.$message({
                    message: "格式不支持!",
                    type: "error",
                  });
                  loading()  //阻断上传
                }
                else{
                  done()  //继续上传
                }
              },
              span: 24,
              showColumn: false,
              propsHttp: {
                home: window.location.origin,
                res: "info"
              },
              // tip: "只能上传jpg/png用户头像，且不超过500kb",
              action: "/file/upload"
            }
        );
      });
    },
    saveHandle(row, done, loading) {
      EditDecisionItem(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        done();
      }).catch(res => {
        loading();
        this.$message.error(res.info);
      });
    },
    // 编辑
    editHandle(row, done, loading) {
      EditDecisionItem(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
      }).catch(res => {
        done();
        this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteDecisionItem({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      ListDecisionItem(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
    uploadAfter(res, done) {
      if (res) {
        this.uploadObj = res;
        done();
        this.$message.success("上传成功");
      }
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
</style>
