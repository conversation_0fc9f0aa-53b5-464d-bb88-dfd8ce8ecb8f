<template>
  <div>
    <basic-container>
      <avue-crud ref="crud"
                 v-model="form"
                 :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 :before-open="beforeOpen"
                 @row-save="saveHandle"
                 @row-update="editHandle"
                 @row-del="deleteHandle"
                 @search-change="searchChange"
                 @refresh-change="refresh">
        <template slot="reportRroup" slot-scope="scope">
          <el-tag style="margin-right: 5px"
                  v-for="item in scope.row.reportRroup.split(',')"
                  :key="item"
                  size="medium">{{item}}
          </el-tag>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button icon="el-icon-magic-stick" :size="scope.size" type="text" @click.stop="design(scope.row)">设计</el-button>
          <el-button icon="el-icon-view" :size="scope.size" type="text" @click.stop="view(scope.row)">预览</el-button>
          <el-button icon="el-icon-view" :size="scope.size" type="text" @click.stop="copy(scope.row)">复制</el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from "vuex";
import {CopyReport, DeleteReport, EditReport, QueryPageReport} from "@/api/report";
import {GetRoleInfo} from "@/api/settings";

export default {
  inject: ["reload"],
  data() {
    return {
      form: {},
      dataLink: [],
      tableLoading: false,
      pageList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10
      },
      data: [],
      roleInfo:[],

    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    optionDetail(){
      return {
        index: true,
        size: "mini",
        // dialogWidth: 580,
        // dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menuWidth: 230,
        align: "center",
        editBtnText: "修改",
        delBtnText: '删除',
        border: true,
        column: [
          {
            label: '名称',
            prop: "reportName",
            search: true,
            rules: [{required: true, message: "请输入名称", trigger: blur}]
          },
          {
            label: '类型',
            prop: "reportType",
            type: "select",
            search: true,
            dicMethod:"post",
            dicUrl:'/sytSysDict/list?code=REPORT_TYPE',
            props: {
              label: "name",
              value: "value",
            },
            rules: [{required: true, message: "请选择报表类型", trigger: blur}],
          },
          /*{
            label: '类型',
            prop: "reportType",
            search: true,
            type: "select",
            dicData: [
              {label: "大屏", value: "大屏"},
              {label: "报表", value: "报表"},
            ],
            rules: [{required: true, message: "请选择报表类型", trigger: blur}]
          },*/
          {
            label: '分组',
            prop: "reportRroup",
            search: true,
            type: "select",
            multiple: true,
            slot: true,
            dicData: [
              {label: "前台", value: "前台"},
              {label: "后台", value: "后台"},
              {label: "画像", value: "画像"},
            ],
            rules: [{required: true, message: "请选择分组名称", trigger: blur}]
          },
          {
            label: "可见角色",
            placeholder: "请选择可见角色 - 可多选",
            // rules: [{ required: true, message: "请选择可见角色" }],
            prop: "roleId",
            type: "select",
            drag: true,
            span: 12,
            multiple: true,
            // search:true,
            dicData: this.roleInfo,
            props: {
              label: "rolename",
              value: "id"
            }
          },
          {
            span: 12,
            label: "可见组织机构",
            placeholder: "请选择可见组织机构 - 可多选",
            prop: "orgId",
            type: "tree",
            multiple: true,
            tags:true,
            checkStrictly:true,
            dicMethod: "post",
            dicUrl: "/sytSysOrganization/treeListJsonArray",
            // dicData: this.orgList,
            props: {
              value: "id"
            }
          },
          {
            label: '描述',
            prop: "reportDesc",
            type: "textarea",
            span: 24,
          },
          {
            label: '创建人',
            prop: "createUser",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '创建时间',
            prop: "createTime",
            addDisplay: false,
            editDisplay: false,
            type: "date",
            format: "yyyy-MM-dd hh:mm:ss",
          },
          /*{
              label: "缩略图",
              prop: "reportImage",
              type: "upload",
              listType: "picture-img",
              span: 24,
              showColumn: false,
              propsHttp: {
                  home: window.location.origin,
                  res: "info"
              },
              // tip: "只能上传jpg/png用户头像，且不超过500kb",
              action: "/file/upload"
          }*/
        ],
        group: [
          {
            prop: "group1",
            column: [],
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.pageParam());
    this.getRoleInfo();
  },
  methods: {
    getRoleInfo() {
      GetRoleInfo().then(res => {
        if (res.data.code == "00000") {
          this.roleInfo = res.data.info;
        } else {
          this.$message.error("服务异常");
        }
        this.optionDetail.column.push(
            {
              label: "缩略图",
              prop: "reportImage",
              type: "upload",
              listType: "picture-img",
              accept:'image/png, image/jpeg',
              tip: '只能上传jpg/png',
              uploadError: (error, column) => {
                if(error == '文件太大不符合'){
                  this.$message.error('图片大小超过限制')
                }
                else{
                  this.$message.error(error)
                }
              },
              uploadBefore:(file, done, loading,column) =>{
                const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
                const isFlag =
                    suffixName === "jpg" || suffixName === "jpeg" || suffixName === "png";
                if (!isFlag) {
                  this.$message({
                    message: "格式不支持!",
                    type: "error",
                  });
                  loading()  //阻断上传
                }
                else{
                  done()  //继续上传
                }
              },
              span: 24,
              showColumn: false,
              propsHttp: {
                home: window.location.origin,
                res: "info"
              },
              // tip: "只能上传jpg/png用户头像，且不超过500kb",
              action: "/file/upload"
            }
        );
      });
    },
    design(row) {
      let designUrl = "/bigscreen/designer"
      if(row.reportType==="报表"){
        designUrl = "/bigscreen/layout";
      }
      var routeUrl = this.$router.resolve({
        path: designUrl,
        query: {
          reportId: row.id
        }
      });
      window.open(routeUrl.href, "_blank");
    },
    view(row) {
      let viewUrl = "/bigscreen/viewer"
      if(row.reportType==="报表"){
        viewUrl = "/bigscreen/layoutviewer";
      }
      var routeUrl = this.$router.resolve({
        path: viewUrl,
        query: {
          reportId: row.id
        }
      });
      window.open(routeUrl.href, "_blank");
    },
    beforeOpen(done, type) {
        console.log("beforeOpen====",done,type)
      if (['view', 'edit'].includes(type)) {

      } else {
      }
      done();
    },
    saveHandle(row, done, loading) {
      var reportRroups = row.reportRroup;
      if (typeof reportRroups ==='object') {
        row.reportRroup = reportRroups.join(',');
      }
      EditReport(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        done();
      }).catch(res => {
        loading();
        this.$message.error(res.info);
      });
    },
    // 编辑
    editHandle(row, done, loading) {
      var reportRroups = row.reportRroup;
      if (typeof reportRroups ==='object') {
        row.reportRroup = reportRroups.join(',');
      }
      EditReport(row).then(res => {
        if (res.data.code === '00000') {
          this.$message({type: "success", message: "操作成功"});
        }
        this.onLoad(this.pageParam())
        loading();
      }).catch(res => {
        done();
        this.$message.error(res.info);
      });
    },
    // 删除
    deleteHandle(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        DeleteReport({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
    copy(row) {
      this.$confirm("确定要复制当前报表, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        CopyReport({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "操作成功"});
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消操作')
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true
      QueryPageReport(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
        this.data = data.records;
      })
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam())
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam())
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {}
      }
    },
    searchChange(param, done) {
      var pageParam = this.pageParam()
      pageParam.queryParam = param
      this.onLoad(pageParam)
      done()
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
  }
}
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
.avue-crud >>> .el-table__row .el-button{
  padding: 3px 0px !important;
}
</style>
