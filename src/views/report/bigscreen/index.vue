<template>
  <div class="main-layout">
    <el-form ref="form" :model="params" :rules="rules" label-width="80px">
      <!-- 搜索 -->
      <el-row :gutter="10">
        <el-col :xs="24" :sm="20" :md="12" :lg="6" :xl="4">
          <el-form-item label="名称：">
            <el-input
                v-model="params.queryParam.reportName"
                size="mini"
                clearable
                placeholder="名称"
                class="filter-item"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="20" :md="4" :lg="4" :xl="4" style="margin-top: 6px;margin-left: 10px;">
          <el-button type="primary" size="mini" icon="el-icon-search" @click="search('form')"
          >查询
          </el-button
          >
          <el-button plain size="mini" icon="el-icon-refresh-left" @click="reset('form')"
          >重置
          </el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="20">
      <el-col v-for="item in list" :key="item.id" :span="6">
        <div class="bg">
          <img
              class="bg-img"
              :src="item.reportImage == null || item.reportImage == ''? require('../../../assets/images/charts.jpg') : item.reportImage"
              alt=""
          />
          <div class="content">
            <header>{{ item.reportName }}</header>
            <footer>
              {{ item.createTime | dateFilter }}
              <div class="operation">
                <!--                <el-button
                                  icon="el-icon-share"
                                  class="view"
                                  type="text"
                                  @click="share(item)"
                                />-->
                <el-button
                    icon="el-icon-view"
                    class="view"
                    type="text"
                    @click="viewDesign(item)"
                />
                <el-button
                    icon="el-icon-edit"
                    class="edit"
                    type="text"
                    @click="openDesign(item)"
                />
              </div>
            </footer>
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="block">
      <el-pagination
          :total="totalCount"
          :page-sizes="[8, 20, 50, 100]"
          :page-size="params.pageSize"
          :current-page="params.page"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
    <!--    <Share
          :visib="visibleForShareDialog"
          :reportCode="reportCodeForShareDialog"
          @handleClose="visibleForShareDialog = false"
        />-->
  </div>
</template>

<script>
// import Share from "@/views/report/report/components/share";
import {ListReport} from "@/api/report";

export default {
  name: "Login",
  // components: { Share },
  data() {
    return {
      list: [],
      rules: {},
      totalCount: 0,
      totalPage: 0,
      params: {
        page: 1,
        pageSize: 8,
        queryParam: {
          reportName: "",
          reportRroup: "后台"
        }
      },
      // 分享
      visibleForShareDialog: false,
      reportCodeForShareDialog: ""
    };
  },
  mounted() {
  },
  created() {
    this.queryByPage();
  },
  methods: {
    // 查询
    search() {
      this.params.page = 1;
      this.queryByPage();
    },
    // 重置
    reset(formName) {
      this.$refs[formName].resetFields();
      this.params.queryParam.reportName = "";
      this.params.page = 1;
      this.queryByPage();
    },
    async queryByPage() {
      const res = await ListReport(this.params);
      if (res.data.code != "00000") return;
      this.listLoading = true;
      this.list = res.data.info.records;
      this.list.forEach(value => {
        value["reportNameCode"] =
            value.reportName + "[" + value.id + "]";
      });
      this.totalCount = res.data.info.total;
      this.totalPage = res.data.info.pages;
      this.listLoading = false;
    },
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.queryByPage();
    },
    handleCurrentChange(val) {
      this.params.page = val;
      this.queryByPage();
    },
    // 分享
    share(val) {
      this.reportCodeForShareDialog = val.id;
      this.visibleForShareDialog = true;
    },
    openDesign(val) {
      var routeUrl = this.$router.resolve({
        path: "/bigscreen/designer",
        query: {
          reportId: val.id
        }
      });
      window.open(routeUrl.href, "_blank");
    },
    viewDesign(val) {
      let viewUrl = "/bigscreen/viewer"
      if(val.reportType==="报表"){
        viewUrl = "";
      }
      var routeUrl = this.$router.resolve({
        path: viewUrl,
        query: {reportId: val.id}
      });
      window.open(routeUrl.href, "_blank");
    }
  },
    filters: {
        dateFilter: function (value) {
            return value.replace(/[A-Z]/g, ' ').slice(0,value.indexOf("."))
        }
    }
};
</script>

<style scoped lang="scss">
.main-layout {
  padding: 20px;
  position: relative;
  height: auto;
  background: #fff;

  header {
    font-size: 24px;
    text-align: center;
    line-height: 80px;
  }

  .bg {
    width: 100%;
    height: 170px;
    position: relative;
    overflow: hidden;
    margin: 10px 0;
    border: 12px solid white;
  }

  .bg .bg-img {
    position: absolute;
    width: 100%;
    height: 100%;
    filter: blur(6px);
    z-index: 2;
  }

  .content {
    width: 100%;
    height: 100%;
    position: absolute;
    color: #fff;
    left: 0;
    top: 0;
    z-index: 3;
  }

  footer {
    width: 100%;
    font-size: 14px;
    padding: 16px;
    line-height: 30px;
    position: absolute;
    z-index: 3;
    bottom: 0;
      box-sizing: border-box;

    .operation {
      float: right;

      .view,
      .edit {
        color: #fff;
        font-size: 14px;
      }
    }
  }
}
</style>
