<template>
    <div>
          <grid-layout
                v-if="show"
                class="gridlayout"
                :style="{
                width: maxwith,
                backgroundColor: dashboard.backgroundColor,
                'background-image': 'url(' + dashboard.backgroundImage + ')',
                'background-position': '0% 0%',
                'background-size': '100% 100%',
                'background-repeat': 'initial',
                'background-attachment': 'initial',
                'background-origin': 'initial',
                'background-clip': 'initial',
                }"
                :layout.sync="widgets"
                :preventCollision="true"
                :autoSize="true"
                :col-num="dashboard.colNum"
                :row-height="dashboard.rowHeight"
                :is-draggable="false"
                :is-resizable="false"
                :is-mirrored="false"
                :vertical-compact="dashboard.verticalCompact"
                :margin="[dashboard.marginLeft, dashboard.marginTop]"
                :use-css-transforms="true"
                :responsive="true"
                :breakpoints="{lg: breakpointsLg, md: breakpointsMd, sm: breakpointsSm, xs: breakpointsXs, xxs: breakpointsXxs}"
                :cols="{lg: colsLg, md: colsMd, sm: colsSm, xs: colsXs, xxs: colsXxs}"
            >

                    <grid-item
                    :style="{
                        'border-radius': item.value.setup.borderRadius + 'px' ? item.value.setup.borderRadius + 'px' : dashboard.borderRadius + `px`,
                        'background-color': item.value.setup.backgroundColor2 !== `` ? item.value.setup.backgroundColor2 : dashboard.backgroundColor2,
                        'border': `${ item.value.setup.borderColor !== `` ? item.value.setup.borderColor : dashboard.borderColor} ${ item.value.setup.borderWeight !== 0 ? item.value.setup.borderWeight : dashboard.borderWeight}px ${ item.value.setup.borderType !== `` ? item.value.setup.borderType : dashboard.borderType}`,
                        '-webkit-box-shadow': `${dashboard.setX}px ${dashboard.setY}px ${dashboard.shadowBlur}px ${dashboard.shadowSpread}px ${dashboard.shadowColor}`,
                    }"
                    ref="widgets"
                    v-for="(item) in widgets"
                    :key="item.i"
                    class="griditem"
                    :x="item.x"
                    :y="item.y"
                    :w="item.w"
                    :h="item.h"
                    :i="item.i"
                >
                    <div
          v-if="item.value.setup.titleShow && item.type !== 'widget-title'"
          :style="{
            'text-align': item.value.setup.titleAlign,
            'line-height': dashboard.titlelineHeight + 'px',
            'border-bottom': `${item.value.setup.Bottomsize}px solid ${item.value.setup.titleBottom}`,
            marginLeft: '15px',
            marginRight: '15px',
          }"
        >
          <span
            v-if="item.value.setup.titleShow"
            :style="{
              fontSize: dashboard.titleSize + 'px',
              color: dashboard.titleColor,
              'font-family': dashboard.titleFamily,
              'font-weight': dashboard.titleFontWeight,
            }"
          >{{ item.value.setup.titleinner }}</span>
          <span
            v-if="item.value.setup.subtitleShow"
            :style="{
              fontSize: item.value.setup.subtitleSize + 'px',
              color: item.value.setup.subtitleColor,
              marginLeft: item.value.setup.subtitleLeft +'px',
            }"
          >{{ item.value.setup.subtitleinner }}</span>
          <div style="display: inline-block;" v-if="item.value.data.dataType == 'dynamicData' && item.value.data.dynamicData && item.value.data.dynamicData.seldynamicData">
            <el-select v-for="(item1,indexxxx) in item.value.data.dynamicData.seldynamicData" :key="indexxxx" :style="selectColor" v-model="value1[indexxxx]" @change="mywidget(value1[indexxxx],item1,item)"  :placeholder="item1.placeholder">
              <el-option
                  v-for="(items,indexx) in item1.setParamList"
                  :key="indexx"
                  :label="items.label"
                  :value="items.value">
                <!--                <span>{{ items.label }}11111111</span>-->
              </el-option>
            </el-select>
          </div>

          <a
            v-if="item.value.setup.totitleShow"
            :style="{
              fontSize: item.value.setup.totitleSize + 'px',
              color: item.value.setup.totitleColor,
              position: `absolute`,
              right: '15px',
            }"
            :href="item.value.setup.linkAdress"
            target="_blank"
          >{{ item.value.setup.totitleinner }}</a>
        </div>
                  <component :is="item.type" :value="item.value"  :style="{ padding: item.value.setup.padding +'px' ? item.value.setup.padding +'px': dashboard.components.padding + `px`,height: (item.type !== 'widget-title' && item.value.setup.titleShow == true ) ? `calc(100% - ${item.value.setup.titlelineHeight + (item.type == 'widget-table' ? item.value.setup.padding ? item.value.setup.padding : dashboard.components.padding  : 0) }px` : '100%','overflow-y': 'auto'}" @changeTitle="allwidget" />
                </grid-item>
            </grid-layout>
    </div>
</template>

<script>
// import widget from "../designer/widget/layitem.vue";
import {detailDashboard} from "@/api/bigscreen";
// import { widgetTools, getToolByCode } from "../designer/laytools";
import VueGridLayout from 'vue-grid-layout';
import widgetProgress from "../designer/widget/widgetProgress.vue";
import widgetHref from "../designer/widget/widgetHref.vue";
import widgetText from "../designer/widget/widgetText.vue";
import widgetBlock from "../designer/widget/widgetBlock.vue";
import WidgetMarquee from "../designer/widget/widgetMarquee.vue";
import widgetTime from "../designer/widget/widgetTime.vue";
import widgetImage from "../designer/widget/widgetImage.vue";
import widgetSlider from "../designer/widget/widgetSlider.vue";
import widgetVideo from "../designer/widget/widgetVideo.vue";
import WidgetIframe from "../designer/widget/widgetIframe.vue";
import widgetBarchart from "../designer/widget/widgetBarchart.vue";
import widgetGradientColorBarchart from "../designer/widget/bar/widgetGradientColorBarchart.vue";
import widgetLinechart from "../designer/widget/widgetLinechart.vue";
import widgetBarlinechart from "../designer/widget/widgetBarlinechart";
import WidgetPiechart from "../designer/widget/widgetPiechart.vue";
import WidgetFunnel from "../designer/widget/widgetFunnel.vue";
import WidgetGauge from "../designer/widget/widgetGauge.vue";
import WidgetPieNightingaleRoseArea from "../designer/widget/pie/widgetPieNightingaleRose";
import widgetTable from "../designer/widget/widgetTable.vue";
import widgetMap from "../designer/widget/widgetMap.vue";
import widgetPiePercentageChart from "../designer/widget/pie/widgetPiePercentageChart";
import widgetAirBubbleMap from "../designer/widget/map/widgetAirBubbleMap";
import widgetBarCirclechart from "../designer/widget/bar/widgetBarCirclechart";
// import widgetBarManychart from "../designer/widget/bar/widgetBarManychart";
import widgetHebeiMap from "../designer/widget/map/widgetHebeiMap";
import widgetBarStackChart from "../designer/widget/bar/widgetBarStackChart";
import widgetLineStackChart from "../designer/widget/line/widgetLineStackChart";
import widgetBarCompareChart from "../designer/widget/bar/widgetBarCompareChart";
import widgetBorder from "../designer/widget/widgetBorder.vue";
import widgetDecorate from "../designer/widget/widgetDecorate.vue";
import widgetTurncard from "../designer/widget/widgetTurncard.vue";
import widgetPercent from "../designer/widget/widgetPercent.vue";
// import widgetWater from "../designer/widget/widgetWater.vue";
import WidgetPictographchart from "../designer/widget/widgetPictographchart.vue";
import WidgetRadarchart from "../designer/widget/widgetRadarchart";
import WidgetDetails from "../designer/widget/widgetDetails.vue";
import widgetTitle from "../designer/widget/widgetTitle.vue";
import widgetPersonal from "../designer/widget/widgetPersonal.vue";
import widgetEpidemic from "../designer/widget/widgetEpidemic.vue";
import widgetNumberCrud from "@/views/report/bigscreen/designer/widget/widgetNumberCrud";

export default {
    name: 'WorkspaceJsonLayoutviewer',
    components: {
      GridLayout: VueGridLayout.GridLayout,
      GridItem: VueGridLayout.GridItem,
      widgetProgress,
      widgetHref,
      widgetText,
      widgetBlock,
      WidgetMarquee,
      widgetTime,
      widgetImage,
      widgetSlider,
      widgetVideo,
      WidgetIframe,
      widgetBarchart,
      widgetGradientColorBarchart,
      widgetLinechart,
      widgetBarlinechart,
      WidgetPiechart,
      WidgetFunnel,
      WidgetGauge,
      WidgetPieNightingaleRoseArea,
      widgetTable,
      widgetMap,
      widgetPiePercentageChart,
      widgetAirBubbleMap,
      widgetBarCirclechart,
      // widgetBarManychart,
      widgetHebeiMap,
      widgetBarStackChart,
      widgetLineStackChart,
      widgetBarCompareChart,
      widgetBorder,
      widgetDecorate,
      widgetTurncard,
      widgetPercent,
      // widgetWater,
      WidgetPictographchart,
      WidgetRadarchart,
      WidgetDetails,
      widgetTitle,
      widgetPersonal,
      widgetEpidemic,
      widgetNumberCrud
    },
    props: ["reportId","humancode"],
    data() {
        return {
            widgets: [],
            dashboard: {},
            reportCode: "",
            breakpointsLg: 1200,
            breakpointsMd: 996,
            breakpointsSm: 768,
            breakpointsXs: 480,
            breakpointsXxs: 0,
            colsLg: `24`,
            colsMd: `18`,
            colsSm: `12`,
            colsXs: `6`,
            colsXxs: `2`,
            value1: [],
            maxwith: '1980px',
            show: false,
            selectkey: [],
            param: {},
            contextDataParam: {},
        };
    },
    watch: {
        reportId(val) {
          if (val) {
              this.reportCode = val;
          }
          // 响应式布局
          // if( this.$route.query.reportId ) {
          //   this.breakpointsLg = 1200;
          //   this.breakpointsMd = 996;
          //   this.breakpointsSm = 768;
          //   this.breakpointsXs = 480;
          //   this.breakpointsXxs = 100;
          // } else {
          //   this.breakpointsLg = 1200;
          //   this.breakpointsMd = 996;
          //   this.breakpointsSm = 768;
          //   this.breakpointsXs = 480;
          //   this.breakpointsXxs = 100;
          // }
          this.getData(this.reportCode);
        },
    },
    computed: {
      selectColor() {
        return {
          'margin-left':  '10px'
        }
      }
    },
  created() {
    console.log(`humancode`,this.humancode)
    this.reportCode = this.$route.query.reportId;
    if (this.reportCode && this.reportCode !== "") {
      this.reportId = this.reportCode;
    }else {
      this.reportCode = this.reportId
    }
    // if( this.$route.query.reportId ) {
    //   this.breakpointsLg = 1200;
    //   this.breakpointsMd = 800;
    //   this.breakpointsSm = 600;
    //   this.breakpointsXs = 500;
    //   this.breakpointsXxs = 100;
    //   } else {
    //     this.breakpointsLg = 1200;
    //     this.breakpointsMd = 800;
    //     this.breakpointsSm = 600;
    //     this.breakpointsXs = 500;
    //     this.breakpointsXxs = 100;
    //   }
    this.getData(this.reportCode);
    this.$EventBus.$on("child-event", (data) => {
      if (data.drillSetList && data.drillSetList.length > 0) {
        //	以新页面打开方式加载下钻数据
        let { href } = this.$router.resolve({
          path: "/bigscreen/detail",
          query: {
            param: JSON.stringify(data),
          },
        });
        window.open(href, "_blank");
      }
    });
  },
  mounted() {
    let width = document.body.clientWidth;
      this.maxwith = width + 'px'
      this.$emit('over',{bool: true})
  },

    methods: {
        async getData(reportCode) {
          const data = await detailDashboard({
              reportCode: reportCode,
          });
          if (data.data.code != "00000") return;
          this.dashboard = data.data.info.dashboard.components;
          console.log(`this.dashboard`,this.dashboard)
          // 响应式布局
          let col = Math.round((this.dashboard.colNum - 2)/4);
          this.colsLg = this.dashboard.colNum;
          this.colsMd = this.colsLg - col;
          this.colsSm = this.colsMd - col;
          this.colsXs = this.colsSm - col;
          this.colsXxs= 2;
          this.widgets = data.data.info.dashboard.widgets;
          console.log(`widgets`,this.widgets)
          if(this.humancode !== undefined){
            for (let i = 0; i < this.widgets.length; i++){
              if(this.widgets[i].value.data.dataType == "dynamicData"){
                //这里和大屏下钻不同，没有contextData，定制化开发，个人画像所需要的数据集传参必须都定义为humancode。不能是xh等名称
                this.widgets[i].value.data.dynamicData.contextData.humancode = this.humancode;
              }
            }
          }
          for( let i =0; i<this.widgets.length; i++ ){
            this.widgets[i].x = this.widgets[i].value.position.x;
            this.widgets[i].y = this.widgets[i].value.position.y;
            this.widgets[i].w = this.widgets[i].value.position.w;
            this.widgets[i].h = this.widgets[i].value.position.h;
            this.widgets[i].i = `${i}`;
            // if( this.widgets[i].value.data.dynamicData ){
            //   if(this.widgets[i].value.data.dynamicData.seldynamicData){
            //     let selectdynamicData = this.widgets[i].value.data.dynamicData.seldynamicData;
            //     for ( let h =0; h<selectdynamicData.length; h++ ){
            //       this.queryEchartsData(selectdynamicData[h]).then(res =>{
            //         console.log(`queryEchartsData`,res)
            //         let name = Object.keys(res[0])[0];
            //         this.selectkey.push(name);
            //         this.$forceUpdate()
            //       })
            //     }
            // }
            // }
          }
          if(this.$store.state.isMobile){
            this.widgets.forEach((item)=>{
              item.y = item.i - 0
              console.log(item)
            })
          }
            this.show = true
          // this.$emit('over',{bool: true})
        },
        // everywidget(data){
        //   let change = data; v
        //   for( let i =0; i < change.list.length; i++ ){
        //     this.widgets[change.list[i]].value.data.dynamicData.contextData = change.param;
        //   }
        // },
        allwidget(data) {
          for( let i =0; i < data.list.length; i++ ){
              // this.widgets[data.list[i]].value.data.dynamicData.contextData = {}
              this.widgets[data.list[i]].value.data.dynamicData.contextData = {...this.widgets[data.list[i]].value.data.dynamicData.contextData,...data.data};
              this.widgets[data.list[i]].value.sort++
            console.log(`allwidget`,this.widgets[data.list[i]])
          }
          this.$forceUpdate()
        },
        mywidget(value1,item1,item) {
          // console.log('mywidget',value1)
          // console.log('mywidget',item1)
          // console.log('mywidget',item)
          this.contextDataParam[item1.chartProperties.key] = value1;
          item.value.data.dynamicData.contextData = this.contextDataParam;
          item.value.sort++
          this.$forceUpdate()
          // if( index > this.optionsData.selectdynamicData.length ){
          //     this.optionsData.selectdynamicData[index+1].contextData = this.param;
          //     this.queryEchartsData(this.optionsData.selectdynamicData[index+1]).then(res => {
          //         let name = Object.keys(res[0])[0];
          //         this.key.push(name);
          //         let list = [];
          //         for( let j =0; j<res.length; j++ ) {
          //             list.push({
          //                 label: res[j][name],
          //                 value: res[j][name]
          //             })
          //         }
          //         this.selectList[index+1] = list;
          //     })
          // }

        }
    },
};
</script>

<style lang="scss" scoped>
  .gridlayout{
    z-index: 88;
  }
</style>
