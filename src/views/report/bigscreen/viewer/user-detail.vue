<template>
	<div class="carousel-wrapper">
	    <div class="main-wrapper">
			<el-descriptions title="基本信息" border :column="3" >
              <el-descriptions-item class="descriptions_body" v-for="item in fields" :key="item" :label="item" >{{obj[item]}}</el-descriptions-item>
          </el-descriptions>
	    </div>
	  </div>
</template>

<script>
import {getDirllData} from '@/api/bigscreen'

export default {
  	props: ['crudParam',],
    data() {
      return {
        obj:{},
        fields:{},
        loadParam: {},
      }
    },
	watch: {
		crudParam(val) {
		},
	},
	computed: {
	},
	created() {
		this.loadParam.queryParam = this.crudParam
		this.onLoad(this.loadParam)
	},
	beforeUpdate() {
	},
	methods: {
		onLoad(param) {
      let columns = [];
      console.log(param)
      if (param.queryParam.drillFieldConfig) {
        let fieldConfig = JSON.parse(param.queryParam.drillFieldConfig);
        for (let i = 0; i < fieldConfig.length; i++) {
          columns.push(fieldConfig[i].field);
        }
      }
			getDirllData(param).then(res => {
        if (res.data.info.records) {
          this.obj = res.data.info.records[0];
          if (columns.length === 0) {
            for (let objKey in this.obj) {
              columns.push(objKey);
            }
          }
          this.fields = columns;
        }
			})
		},
	}
  }
</script>

<style scoped lang="scss">
.carousel-wrapper {
  width: 100%;
  height: 100%;
  background-color: #eee;
  background-repeat: no-repeat;
  background-size: cover;
}
.main-wrapper {
	width: 100%;
	background-color: #fff;
	border-radius: 5px;
	padding:20px;
	position: relative;
	margin: auto;
}
::v-deep .el-descriptions-item__label{
   width: 13%;
   background-color: #191c260d;
}
::v-deep .el-descriptions-item__content{
   width: 20%;
}
//::v-deep .el-descriptions__body {
// 	font-size:18px;
//     color: #333;
//     background-color: transparent;
// }
//::v-deep .el-descriptions__header {
// 	margin-bottom:10px;
// 	padding:15px 0px;
// 	border-bottom: 1px solid #EBEEF512;
// }
//::v-deep .el-descriptions__title {
// 	color:#333;
//     font-size: 18px;
//     font-weight: 700;
// 	border-left:5px solid #0077AA;
// 	padding-left:10px;
// }
//::v-deep .el-descriptions-item__label.is-bordered-label {
//     font-weight: 700;
//     color: #333;
//     background: transparent;
// }
//::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
//     border: 1px solid #EBEEF512;
//     padding: 12px 10px;
//     padding-bottom: 12px;
// }
//::v-deep .el-descriptions-item__label.has-colon {
// 	width:100px;
// 	color: #333;
//   background-color: cyan;
// }
//::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
// 	padding: 10px 0px;
// }
</style>

