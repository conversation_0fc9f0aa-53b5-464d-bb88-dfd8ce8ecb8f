<template>
  <div class="layout" >
    <!-- <div class="box-card-hd" >
      <span>{{title}}</span>
    </div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item v-for="(item,index) in navList" :key="index" @click.native="toNavigate(item)" >
        {{item.contextData[item.drillParamList[0]]}}
      </el-breadcrumb-item>
    </el-breadcrumb> -->
    <dynamicCrud :report-id="reportid" :crud-param="param" :query-param="queryParam" @sendparent="detail($event)" @sendparent2="detail2($event)"/>
  </div>
</template>

<script>
import dynamicCrud from '@/components/crud/dynamic-crud'
import userDetail from '@/views/report/bigscreen/viewer/user-detail.vue'


export default {
  name: "Login",
  components: {
    dynamicCrud,
    userDetail
  },
  data() {
    return {
      bigScreenStyle: {},
      widgets: [],
      crudColumn: [],
      param: {},
      queryParam: {},
      title: ``,
      Crud: ``,
      User: ``,
      navList: [],
      reportid: ``,
    };
  },
  created() {
  },
  mounted() {
    let obj = JSON.parse(this.$route.query.param);
    if (obj.drillSetList && obj.drillSetList.length > 0) {
      // this.param = obj.drillSetList[0];
      let drillSet = obj.drillSetList[0];
      drillSet.contextData = obj.contextData;
      drillSet.setCode = drillSet.drillSetCode;
      this.param = drillSet;
      // obj.drillSetList.shift();
      this.queryParam = obj;
      this.reportid = obj.drillReportid
    }
    // this.navList.push(this.param);
    // this.title = this.param.drillSetName
  },
  methods: {
    detail(data){
      this.Crud = data.shows
      this.User = data.Shwo
    },
    detail2(tit){
      console.log(tit);
      this.navList = tit.List
      this.title = tit.Title
    },
    toNavigate(item){
      // 点击导航，把学生详情隐藏，下钻下钻展示
      if( item.index !== this.navigationList.length-1){
        // this.shows = true
        // this.Show = false
        this.loadParam = item;
        this.navigationList = this.navigationList.slice(0, item.index);
      }
    },
  }
}
</script>

<style scoped lang="scss">
.box-card-hd {
  padding: 16px 24px;
  min-height: 20px;
  background-color: #304da7bf;
  border-bottom: 1px solid #002140;
  text-align: left;
  color: #fff;
  font-size: 22px;
}
.layout {
  width: 100%;
  height: 100%;
  text-align: center;
}

.layout>div:nth-child(1) {
  overflow: hidden;
}

::v-deep .el-dialog__header {
  text-align: left;
}

.bottom-text {
  width: 100%;
  color: #a0a0a0;
  position: fixed;
  bottom: 16px;
  z-index: 9999;
}

::v-deep .el-dialog__body {
  font-size: 22px;
}

::v-deep .el-dialog__body {
  padding: 10px 25px;
  max-height: calc(100vh - 80px);
}

::v-deep .el-dialog {
  border-radius: 10px;
  overflow: hidden;
  left: 0px !important;
  background-color: #002140;
  background-image: url(../../../../styles/assets/image/u23962.png);
  background-repeat: no-repeat;
  background-size: cover;
}

::v-deep .avue-dialog .el-dialog__header {
  padding: 16px 24px;
  min-height: 20px;
  background-color: #304da7bf;
  border-bottom: 1px solid #002140;
}

::v-deep	.v-modal {
  opacity: .9;
  background: #000;
}
::v-deep	::-webkit-scrollbar-track-piece{
  background: #f7f7f700 !important;
  -webkit-border-radius:0;
}

::-webkit-scrollbar-track { background-color: #eee; } /* 滚动条的滑轨背景颜色 */
::v-deep	::-webkit-scrollbar-thumb { background-color: rgba(255, 255, 255, 0.3); } /* 滑块颜色 */
::-webkit-scrollbar-button { background-color: #eee; } /* 滑轨两头的监听按钮颜色 */
::-webkit-scrollbar-corner { background-color: black; } /* 横向滚动条和纵向滚动条相交处尖角的颜色 */
</style>
