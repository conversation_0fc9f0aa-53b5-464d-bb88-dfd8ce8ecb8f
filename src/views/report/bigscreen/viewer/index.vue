<template>

    <div style="height: 100%">
        <div v-show="loading" class="avue-home">
            <div class="avue-home__main">
                <img
                        class="avue-home__loading"
                        src="@/assets/imgs/loading-spin.svg"
                        alt="loading"
                />
                <div class="avue-home__title">
                    正在加载资源
                </div>
                <div class="avue-home__sub-title">
                    初次加载资源可能需要较多时间 请耐心等待
                </div>
            </div>
        </div>
        <div class="layout" id="layout" :style="bigStyle">
<!--            <i @click="tonext(1)" style="position: fixed;top: 50%;left: 0;margin-top: -25px;font-size: 40px;color: red;z-index: 99999999" class="el-icon-arrow-left"></i>-->
<!--            <i @click="tonext(-1)" style="position: fixed;top: 50%;right: 0;margin-top: -25px;font-size: 40px;color: red;z-index: 99999999" class="el-icon-arrow-right"></i>-->
            <div :style="bigScreenStyle">
                <widget
                        v-for="(widget, index) in widgets"
                        :key="index"
                        v-model="widget.value"
                        :type="widget.type"
                        :component-param="componentParam"
                />
            </div>
        </div>
    </div>

</template>

<script>
    import widget from "../designer/widget/temp";
    import {detailDashboard} from "@/api/bigscreen";
    // import dynamicCrud from "@/components/crud/dynamic-crud";

    export default {
        name: "Login",
        // props: ["reportId"],
        components: {
            widget,
            // dynamicCrud,
        },
        data() {
            return {
                isShow: false,
                bigScreenStyle: {},
                widgets: [],
                loading: true,
                crudColumn: [],
                param: {},
                componentParam: {},
                reportId: ``,
                reportCode: "",
                equipment: ``,
                // bigStyle:{},
                layoutheight: ``,
            };
        },
        created() {
            // debugger
            this.reportCode = this.$route.query.reportId;
            if (this.reportCode && this.reportCode !== "") {
                this.reportId = this.reportCode;
            }
            //使用方式一定义时
            this.$EventBus.$on("child-event", (data) => {
                if (data.drillSetList && data.drillSetList.length > 0) {
                    // this.param = data;
                    // this.isShow = true;
                    //	以新页面打开方式加载下钻数据
                    let {href} = this.$router.resolve({
                        path: "/bigscreen/detail",
                        query: {
                            param: JSON.stringify(data),
                        },
                    });
                    window.open(href, "_blank");

                }
            });
            if (this.$route.query.param) {
                this.componentParam = JSON.parse(this.$route.query.param);
            }
            // 大屏预览页面宽度自适应优化
            // var lastClientWidth = document.body.clientWidth;
            // window.onresize = () => {
            //   // 检测宽度发生变化,可以再添加throttle
            //   if (document.body.clientWidth - lastClientWidth != 0) {
            //     lastClientWidth = document.body.clientWidth;
            //     let width = Number(
            //       this.bigScreenStyle.width.substring(
            //         0,
            //         this.bigScreenStyle.width.length - 2
            //       )
            //     );
            //     const ratioEquipment = document.body.clientWidth / width;
            //     // this.bigStyle.transform = `scale(${ratioEquipment}, ${ratioEquipment})`;
            //     this.layoutheight = this.bigStyle.height * (this.equipment / this.bigStyle.width);
            //     console.log(this.layoutheight)
            //   }
            // };
        },
        mounted() {

        },
        watch: {
            reportId(val) {
                if (val) {
                    this.reportCode = val;
                }
                if (this.$route.query.param) {
                    this.componentParam = JSON.parse(this.$route.query.param);
                }
                if (val) {
                    this.equipment = document.getElementById('layout').clientWidth;
                } else {
                    this.equipment = document.body.clientWidth;
                }
                // this.loading = false
                this.getData(this.reportCode);
            },
        },
        computed: {
            bigStyle() {
                return {
                    height: this.layoutheight + `px`
                }
            }
        },
        methods: {
            tonext(num){


                this.reportId = '1680765668602961922';
                // this.$route.query.reportId = 'cd65a1a22c494b9f831aee030ca3d932'
                this.$router.push({path: '/bigscreen/viewer',query:{reportId: '1680765668602961922'}})
            },
            async getData(reportCode) {

                const data = await detailDashboard({
                    reportCode: reportCode,
                });
                if (data.data.code != "00000") return;
                // const equipment = document.body.clientWidth;
                const ratioEquipment = this.equipment / data.data.info.dashboard.width;
                this.bigScreenStyle = {
                    width: data.data.info.dashboard.width + "px",
                    height: data.data.info.dashboard.height + "px",
                    // width: "100%",
                    // height: "100%",
                    "background-color": data.data.info.dashboard.backgroundColor,
                    "background-image":
                        "url(" + data.data.info.dashboard.backgroundImage + ")",
                    "background-position": "0% 0%",
                    "background-size": "100% 100%",
                    "background-repeat": "initial",
                    "background-attachment": "initial",
                    "background-origin": "initial",
                    "background-clip": "initial",
                    transform: `scale(${ratioEquipment}, ${ratioEquipment})`,
                    "transform-origin": "0 0",
                };
                var layout = document.getElementById('layout');
                this.layoutheight = (data.data.info.dashboard.height) * ratioEquipment;
                layout.style.height = this.layoutheight + `px`;
                this.widgets = data.data.info.dashboard.widgets;
                this.loading = false
                if (this.$route.query.param) {
                    this.componentParam = JSON.parse(this.$route.query.param);
                    for (var i = 0; i < this.widgets.length; i++) {
                        if (this.widgets[i].value.data.dataType == "dynamicData") {
                            this.widgets[i].value.data.dynamicData.contextData = this.componentParam.contextData;
                        }
                    }
                }
            },
        },
    };
</script>

<style scoped lang="scss">
    .layout {
        width: 100%;
        // height: 100%;
        text-align: center;
        overflow: hidden;
    }

    .layout > div:nth-child(1) {
        overflow: hidden;
        z-index: 99 !important;
    }

    ::v-deep .el-dialog__header {
        text-align: left;
    }

    .bottom-text {
        width: 100%;
        color: #a0a0a0;
        position: fixed;
        bottom: 16px;
        z-index: 9999;
    }

    ::v-deep .el-dialog__body {
        font-size: 22px;
    }

    ::v-deep .el-dialog__body {
        padding: 10px 25px;
        max-height: calc(100vh - 80px);
    }

    ::v-deep .el-dialog {
        border-radius: 10px;
        overflow: hidden;
        left: 0px !important;
        background-color: #002140;
        background-image: url(../../../../styles/assets/image/u23962.png);
        background-repeat: no-repeat;
        background-size: cover;
    }

    ::v-deep .avue-dialog .el-dialog__header {
        padding: 16px 24px;
        min-height: 20px;
        background-color: #304da7bf;
        border-bottom: 1px solid #002140;
    }

    ::v-deep .v-modal {
        opacity: 0.9;
        background: #000;
    }

    ::v-deep ::-webkit-scrollbar-track-piece {
        background: #f7f7f700 !important;
        -webkit-border-radius: 0;
    }

    ::-webkit-scrollbar-track {
        background-color: #eee;
    }

    /* 滚动条的滑轨背景颜色 */
    ::v-deep ::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
    }

    /* 滑块颜色 */
    ::-webkit-scrollbar-button {
        background-color: #eee;
    }

    /* 滑轨两头的监听按钮颜色 */
    ::-webkit-scrollbar-corner {
        background-color: black;
    }

    /* 横向滚动条和纵向滚动条相交处尖角的颜色 */
</style>
