<template>
    <div class="details" :style="styleColor">
            <el-row :style="nameColor" :gutter="10">
                <el-col v-for="item in textKey" :key="item" :span="12"><div class="grid-content bg-purple">{{item}}: {{textObj[item]}}</div></el-col>
            </el-row>
    </div>
</template>

<script>
import {detailBysetId} from "@/api/bigscreen";

export default {
    name: 'WidgetDetails',
    props: {
        value: Object,
        ispreview: Boolean
    },
    data() {
        return {
            textKey:{},
            textObj:{},
            options: {},
            optionsData: {},
            imageAdress: `data:image/png;base64,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`,
        };
    },
    computed: {
        transStyle() {
            return this.objToOne(this.options);
        },
        nameColor() {
            return {
                "font-size": this.transStyle.nameSize + "px",
                color: this.transStyle.nameColor,
                "font-weight": this.transStyle.nameWeight,
                "font-family":this.transStyle.nameFamily,
                "text-align": this.transStyle.nameAlign,
            }
        },
        styleColor() {
            return {
                position: this.ispreview ? "absolute" : "static",
                color: this.transStyle.color,
                "font-weight": this.transStyle.fontWeight,
                text: this.transStyle.text,
                "line-height": this.transStyle.lineHeight + "px",
                "font-family":this.transStyle.fontFamily,
                "font-size": this.transStyle.fontSize + "px",
                "letter-spacing": this.transStyle.letterSpacing + "em",
                background: this.transStyle.background,
                "text-align": this.transStyle.textAlign,
                width: this.transStyle.width + "px",
                height: this.transStyle.height + "px",
                left: this.transStyle.left + "px",
                top: this.transStyle.top + "px",
                right: this.transStyle.right + "px",
                border: this.transStyle.textBorder + "px solid #fff",
                "border-color": this.transStyle.textBorderColor,
                "border-radius": this.transStyle.textBorderRadius + "px",
            };
        },

    },
    watch: {
        value: {
            handler(val) {
                this.options = val;
                this.optionsData = val.data;
                this.setOptionsData();
            },
            deep: true
        }
    },
    mounted() {
        this.options = this.value;
        this.optionsData = this.value.data;
        this.setOptionsData();
    },
    methods: {
        setOptionsData() {
            const optionsData = this.optionsData; // 数据类型 静态 or 动态
            optionsData.dataType == "staticData"
            ? this.staticDataFn(optionsData.staticData)
            : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
        },
        staticDataFn(val) {
            const staticData = JSON.parse(val);
            this.textObj = staticData[0];
            this.textKey = Object.keys(this.textObj);
        },
        dynamicDataFn(val, refreshTime) {
            if (!val) return;
            if (this.ispreview) {
                this.getEchartData(val);
                this.flagInter = setInterval(() => {
                this.getEchartData(val);
                }, refreshTime);
            } else {
                this.getEchartData(val);
            }
            },
        getEchartData(val) {
          let columns = [];
          detailBysetId({id: val.setCode}).then(res => {
            if (res.data.code === '00000' && res.data.info.fieldConfig) {
              let fieldConfig = JSON.parse(res.data.info.fieldConfig);
              for (let i = 0; i < fieldConfig.length; i++) {
                columns.push(fieldConfig[i].field);
              }
            }
          }).catch(res => {
            this.$message.error(res.info);
          });
            const data = this.queryEchartsData(val);
            data.then((res) => {
                  const dynamicData = res;
                  this.textObj = dynamicData[0];
                  this.textKey = columns.length == 0 ? Object.keys(this.textObj) : columns;
                  this.$forceUpdate();
            });
        }
    },
};
</script>

<style lang="scss" scoped>
.details{
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.text{
    // width: 50%;
    // float: left;
    // overflow: hidden;
    border-collapse: separate;
}
.grid-content {
    white-space: nowrap;
    // overflow: hidden;
}
.row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
}
</style>
