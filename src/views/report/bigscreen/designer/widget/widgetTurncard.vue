
<template> 
<div :style="styleObj">
  <dv-digital-flop  :config="config" :key="num" />
</div>
    
</template>

<script>
export default {
  name: "WidgetTurncard",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {},
      optionsData: {},
      optionsStyle: {},
      optionsSetup: {},
      num: 1,
      config: {

      },
      config1: {
        number: [0],
        content: '',
      },
      config2: {
        number: [888],
        content: '',
      },
    };
  },
  computed: {
    styleObj() {
      if(this.optionsStyle && this.optionsStyle.w){
        return{
          width: `100%`,
          height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          background: this.optionsSetup.background,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        }
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
          background: this.optionsSetup.background
        };
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        console.log(`value`,val)
        this.options = val;
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsSetup = val.setup;
        this.config1.content = `{nt}${val.setup.unitName}`;
        // this.config1.animationFrame = this.options.setup.animationTime;
        this.config1.style = {
          fill: this.options.setup.color,
          fontSize: this.options.setup.fontSize,
          fontFamily: this.options.setup.fontFamily,
          fontWeight: this.options.setup.fontWeight,
        }
        this.config = this.config1;
        this.num++;
        this.setOptionsData();
      },
      deep: true
    }
  },
  created() {
  },
  mounted() {
    console.log(`this.value`,this.value)
    this.options = this.value;
    this.optionsData = this.value.data;
    this.optionsStyle = this.value.position;
    this.optionsSetup = this.value.setup;
    this.config1.content = `{nt}${this.options.setup.unitName}`;
    // this.config1.animationFrame = this.options.setup.animationTime;
    this.config1.style = {
      fill: this.options.setup.color,
      fontSize: this.optionsSetup.fontSize,
      fontFamily: this.options.setup.fontFamily,
      fontWeight: this.options.setup.fontWeight,
    }
    this.config = this.config1;
    this.num++
    this.setOptionsData();
    console.log(`config1`,this.config) 
  },
  beforeUpdate() {
    this.config2.content = `{nt}${this.options.setup.unitName}`;
    // this.config2.animationFrame = this.options.setup.animationTime;
    this.config2.style = {
      fill: this.options.setup.color,
      fontSize: this.options.setup.fontSize,
      fontFamily: this.options.setup.fontFamily,
      fontWeight: this.options.setup.fontWeight,
    }
    this.config = this.config2
    console.log(`config2`,this.config) 
  },
  methods: {
    // setData(){
    //   this.options = this.value;
    //   this.optionsData = this.value.data;
    //   this.config1.content = `{nt}${this.options.setup.unitName}`;
    //   this.config1.animationFrame = this.options.setup.animationTime;
    //   this.config1.style = {
    //     fill: this.options.setup.color,
    //     fontSize: this.options.setup.fontSize,
    //     fontFamily: this.options.setup.fontFamily,
    //     fontWeight: this.options.setup.fontWeight,
    //   }
    //   this.config = this.config1;
    //   this.num++
    //   this.setOptionsData();
    // },
    // 数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    },
    staticDataFn(val) {
      const staticData = JSON.parse(val);
      this.config2.number = [staticData];
      console.log(this.config2)
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.renderingFn(res);
      });
    },
    renderingFn(val) {
      this.config1.content = `{nt}${this.options.setup.unitName}`;
      this.config1.style = {
        fill: this.options.setup.color,
        fontSize: this.options.setup.fontSize,
        fontFamily: this.options.setup.fontFamily,
        fontWeight: this.options.setup.fontWeight,
      }
      this.config = this.config1;
      this.num++
      let Data = val[0].value;
      this.config2.number = [Data]
      this.$forceUpdate();
    }
  }
};
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
