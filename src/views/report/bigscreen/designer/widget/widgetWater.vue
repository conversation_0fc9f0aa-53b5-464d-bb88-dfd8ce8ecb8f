<template>
    <div style="width: 100%;height: 100%">
        <dv-water-level-pond class="text" :key="percentNum" :config="config" :style="styleColor" />
    </div>

</template>

<script>
export default {
    name: 'widgetWater',
    components: {},
    props: {
        value: Object,
        ispreview: Boolean
    },
    data() {
        return {
            config:{
                data:[66],
                shape: 'round',
                waveNum: 3,
                colors: ['#be82ef', '#3DE7C9']
            },
            percentNum: 1,
            optionsStyle: {},
            optionsSetUp: {},

        };
    },
    computed:{
        styleColor() {
            return {
                position: this.ispreview ? "absolute" : "static",
                fontSize: this.optionsSetUp.fontSize + "px",
                width: this.optionsStyle.width + "px",
                height: this.optionsStyle.height + "px",
                left: this.optionsStyle.left + "px",
                top: this.optionsStyle.top + "px",
                right: this.optionsStyle.right + "px",
            };
        }
    },
    watch:{
        value: {
            handler(val) {
                this.optionsSetUp = val.setup
                this.config.data[0] = val.setup.Number;
                this.config.waveNum = val.setup.waveNum;
                this.config.shape = val.setup.shape;
                this.config.colors[0] = val.setup.colors1;
                this.config.colors[1] = val.setup.colors2;
                this.config.waveHeight = val.setup.waveHeight;
                this.optionsData = val.data;

                this.optionsStyle = val.position;
                this.percentNum++
                this.setOptionsData();

            },
            deep: true
        }
    },
    mounted() {
        // this.config = this.value.setup;
        this.optionsSetUp = this.value.setup;
        this.optionsData = this.value.data;
        this.config.data[0] = this.value.setup.Number;
        this.config.waveNum = this.value.setup.waveNum;
        this.config.shape = this.value.setup.shape;
        this.config.colors[0] = this.value.setup.colors1;
        this.config.colors[1] = this.value.setup.colors2;
        this.config.waveHeight = this.value.setup.waveHeight;
        this.optionsStyle = this.value.position;
        this.percentNum++
        this.setOptionsData();

    },

    methods: {
        // 数据解析
        setOptionsData() {
            const optionsData = this.optionsData; // 数据类型 静态 or 动态
            if (optionsData.dataType == "dynamicData") {
                this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
            }
        },
        dynamicDataFn(val, refreshTime) {
            if (!val) return;
            if (this.ispreview) {
                this.getEchartData(val);
                this.flagInter = setInterval(() => {
                this.getEchartData(val);
                }, refreshTime);
            } else {
                this.getEchartData(val);
            }
        },
        getEchartData(val) {
            const data = this.queryEchartsData(val);
            data.then(res => {
                console.log('water',res)
                this.config.data[0] = res[0].value;
                this.percentNum++
                this.$forceUpdate();
            });
            }
        },
};
</script>

<style lang="scss" scoped>
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
::v-deep .dv-water-pond-level text{
    font-size: inherit;
}
::v-deep .dv-water-pond-level canvas{
    margin-left: 0px;
}
</style>