
<template>
  <transition :name="transStyle.transition">
    <div v-show="show" class="text" :style="styleColor">
      <p
          v-if="this.optionsSetup.dynamicShow"
          :style="{
      'text-align': this.optionsSetup.dynamictextAlign,
      'margin-top': this.optionsSetup.dynamiclineTop + 'px',
      'margin-bottom': '0',
    }"><span ref="span" :style="spanColor" @click="chartEvents" >{{ this.optionsSetup.dynamictext }}</span></p>
      <p
          v-if="this.optionsSetup.staticShow"
          :style="{
      'text-align': this.transStyle.statictextAlign,
      'margin-top': this.transStyle.staticlineTop + 'px',
      'margin-bottom': '0',
    }"><span ref="span" :style="spanColor2" @click="chartEvents({'name': optionsSetup.dynamictext })">{{ this.optionsSetup.statictext }}</span></p>
    </div>
  </transition>

</template>

<script>
import {deepClone} from "@/util/util";

export default {
  name: "WidgetText",
  components: {
  },
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {},
      optionsData: {},
      show: false,
      optionsSetup: ``,
    };
  },
  computed: {
    transStyle() {
      return this.objToOne(this.options);
    },
    spanColor() {
      return {
        color: this.optionsSetup.dynamiccolor,
        "font-family":this.optionsSetup.dynamicfontFamily + `!important`,
        "font-weight": this.optionsSetup.dynamicfontWeight,
        "font-size": this.optionsSetup.dynamicfontSize + "px",
        "letter-spacing": this.optionsSetup.dynamicletterSpacing + "px",
        "text-align": this.optionsSetup.dynamictextAlign,
        "line-height": this.optionsSetup.dynamiclineHeight + "px",

      }
    },
    spanColor2() {
      return {
        color: this.transStyle.staticcolor,
        "font-family":this.transStyle.staticfontFamily + `!important`,
        "font-weight": this.transStyle.staticfontWeight,
        "font-size": this.transStyle.staticfontSize + "px",
        "letter-spacing": this.transStyle.staticletterSpacing + "px",
        "text-align": this.transStyle.statictextAlign,
        "line-height": this.optionsSetup.staticlineHeight + "px",
      }
    },
    styleColor() {
      if(this.transStyle.w){
        return{
          position: this.ispreview ? "absolute" : "static",
          // color: this.options.setup.color,
          background: this.transStyle.background,
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          // border: this.transStyle.textBorder + "px solid #fff",
          // "border-color": this.transStyle.textBorderColor,
          "border-radius": this.transStyle.borderRadius + 'px',
		      padding:this.transStyle.padding + "px",
          // cursor: this.transStyle.pointer
        }
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          // color: this.transStyle.color,
          // "font-weight": this.transStyle.fontWeight,
          // text: this.transStyle.text,
          // "font-family":this.transStyle.fontFamily,
          // "font-size": this.transStyle.fontSize + "px",
          // "letter-spacing": this.transStyle.letterSpacing + "em",
          // background: this.transStyle.background,
          // "text-align": this.transStyle.textAlign,
          width: this.transStyle.width + "px",
          height: this.transStyle.height + "px",
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          transition: `all ${this.optionsSetup.transitionTime}s`
          // border: this.transStyle.textBorder + "px solid #fff",
          // "border-color": this.transStyle.textBorderColor,
          // "border-radius": this.transStyle.textBorderRadius + "px",
		  // padding:this.transStyle.padding + "px",
          // cursor: this.transStyle.pointer
        };
      }
    //   return {
    //     position: this.ispreview ? "absolute" : "static",
    //     color: this.transStyle.color,
    //     "font-weight": this.transStyle.fontWeight,
    //     text: this.transStyle.text,
		// "font-family":this.transStyle.fontFamily,
    //     "font-size": this.transStyle.fontSize + "px",
    //     "letter-spacing": this.transStyle.letterSpacing + "em",
    //     background: this.transStyle.background,
    //     "text-align": this.transStyle.textAlign,
    //     width: this.transStyle.width + "px",
    //     height: this.transStyle.height + "px",
    //     left: this.transStyle.left + "px",
    //     top: this.transStyle.top + "px",
    //     right: this.transStyle.right + "px",
    //     border: this.transStyle.textBorder + "px solid #fff",
    //     "border-color": this.transStyle.textBorderColor,
    //     "border-radius": this.transStyle.textBorderRadius + "px",
    //   };
    }
  },
  watch: {
    value: {
      handler(val) {
        this.options = val;
        this.optionsSetup = val.setup;
        this.optionsData = val.data;
        this.setOptionsData();

        // this.addPoint();
      },
      deep: true
    }
  },
  created() {
    this.options = this.value;
    this.optionsSetup = this.value.setup;
    this.optionsData = this.value.data;
    this.setOptionsData();
  },
  mounted() {
    setTimeout(()=>{
      this.show = true;
    },(this.optionsSetup.startTime + '000'))
    // this.addPoint();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      console.log(`params`,params)
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          console.log(`dynamicData`,dynamicData)

          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    addPoint() {
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if( dynamicData ) {
        if( dynamicData && dynamicData.drillSetList.length > 0){
          this.$refs.span.style.cursor = "pointer"
        }
      }

    },
    // 数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      if (optionsData.dataType == "dynamicData") {
        this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
      } else {
        this.staticDataFn(optionsData.staticData)
      }
    },
    staticDataFn(val) {

    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        console.log(`getEchartData`,res)
        if(res.length > 0){
          if( res[0].value === '' || (typeof res[0].value) === undefined ){
            this.optionsSetup.dynamictext = '-'
          } else {
            this.optionsSetup.dynamictext = res[0].value;
          }
        }
        console.log(`this.optionsSetup.dynamictext`,this.optionsSetup.dynamictext)
        this.addPoint();
        this.$forceUpdate();
      });
    }
  }
};
</script>

<style scoped lang="scss">
.fade-enter,.fade-leave-to{
  opacity: 0
}
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.text_p:hover{
  cursor: pointer ;
}
</style>
