
<template>
  <div class="text" :style="styleColor">
    <dv-decoration-1 :reverse="reserve" v-if="decorateShow == 'dv-decoration-1'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-1>
    <dv-decoration-2 :reverse="reserve" v-if="decorateShow == 'dv-decoration-2'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-2>
    <dv-decoration-3 :reverse="reserve" v-if="decorateShow == 'dv-decoration-3'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-3>
    <dv-decoration-4 :reverse="reserve" v-if="decorateShow == 'dv-decoration-4'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-4>
    <dv-decoration-5 :reverse="reserve" v-if="decorateShow == 'dv-decoration-5'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-5>
    <dv-decoration-6 :reverse="reserve" v-if="decorateShow == 'dv-decoration-6'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-6>
    <dv-decoration-7 :reverse="reserve" v-if="decorateShow == 'dv-decoration-7'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-7>
    <dv-decoration-8 :reverse="reserve" v-if="decorateShow == 'dv-decoration-8'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-8>
    <dv-decoration-9 :reverse="reserve" v-if="decorateShow == 'dv-decoration-9'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-9>
    <dv-decoration-10 :reverse="reserve" v-if="decorateShow == 'dv-decoration-10'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-10>
    <dv-decoration-11 :reverse="reserve" v-if="decorateShow == 'dv-decoration-11'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-11>
    <dv-decoration-12 :reverse="reserve" v-if="decorateShow == 'dv-decoration-12'" :key="decorateNumber" :color="[mainColor, lessColor]" :dur=animation></dv-decoration-12>
  </div>
</template>

<script>
export default {
  name: "widgetDecorate",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {},
      optionsData: {},
      decorateShow:'dv-decoration-1',
      reserve:false,
      decorateNumber:1,
      borderBackground:'',
      mainColor:'',
      lessColor:'',
      animation:'',

    };
  },
  computed: {
    transStyle() {
      return this.objToOne(this.options);
    },
    styleColor() {
      return {
        position: this.ispreview ? "absolute" : "static",
        background: this.transStyle.background,
        // "background-image": this.transStyle.backgroundImage,
        width: this.transStyle.width + "px",
        height: this.transStyle.height + "px",
        left: this.transStyle.left + "px",
        top: this.transStyle.top + "px",
        right: this.transStyle.right + "px",
      };
    }
  },
  watch: {
    value: {
      handler(val) {
        this.options = val;
        this.decorateShow = val.setup.borderStyle;
        this.reserve = val.setup.isReverse;
        // this.borderBackground = val.setup.borderBackground;
        this.mainColor = val.setup.mainColor;
        this.lessColor = val.setup.lessColor;
        this.animation = val.setup.animation;
        this.decorateNumber++;
        // this.optionsData = val.data;
        // this.setOptionsData();
      },
      deep: true
    }
  },
  mounted() {
    this.options = this.value;
    this.decorateShow = this.value.setup.borderStyle;
    this.reserve = this.value.setup.isReverse;
    this.optionsData = this.value.data;
    // this.borderBackground = this.value.setup.borderBackground;
    this.mainColor = this.value.setup.mainColor;
    this.lessColor = this.value.setup.lessColor;
    this.animation = this.value.setup.animation;
    // this.decorateNumber++;
    // this.setOptionsData();
  },
  methods: {
    // 数据解析
    // setOptionsData() {
    //   const optionsData = this.optionsData; // 数据类型 静态 or 动态
    //   if (optionsData.dataType == "dynamicData") {
    //     this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    //   } else {};
    // },
    // dynamicDataFn(val, refreshTime) {
    //   if (!val) return;
    //   if (this.ispreview) {
    //     this.getEchartData(val);
    //     this.flagInter = setInterval(() => {
    //       this.getEchartData(val);
    //     }, refreshTime);
    //   } else {
    //     this.getEchartData(val);
    //   }
    // },
    // getEchartData(val) {
    //   const data = this.queryEchartsData(val);
    //   data.then(res => {
    //     this.styleColor.text = res[0].value;
    //     this.$forceUpdate();
    //   });
    // }
  }
};
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
