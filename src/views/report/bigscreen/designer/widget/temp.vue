
<template>
  <div>
    <component :is="type" :value="value" :ispreview="true" />
  </div>
</template>

<script>
import widgetHref from "./widgetHref.vue";
import widgetText from "./widgetText.vue";
import widgetBlock from "./widgetBlock.vue";
import WidgetMarquee from "./widgetMarquee.vue";
import widgetTime from "./widgetTime.vue";
import widgetImage from "./widgetImage.vue";
import widgetSlider from "./widgetSlider.vue";
import widgetVideo from "./widgetVideo.vue";
import WidgetIframe from "./widgetIframe.vue";
import widgetBarchart from "./widgetBarchart.vue";
import widgetLinechart from "./widgetLinechart.vue";
import widgetBarlinechart from "./widgetBarlinechart";
import widgetGradientColorBarchart from "./bar/widgetGradientColorBarchart.vue";
import WidgetPiechart from "./widgetPiechart.vue";
import WidgetFunnel from "./widgetFunnel.vue";
import WidgetGauge from "./widgetGauge.vue";
import WidgetPieNightingaleRoseArea from "./pie/widgetPieNightingaleRose";
import widgetTable from "./widgetTable.vue";
import widgetMap from "./widgetMap.vue";
import widgetPiePercentageChart from "./pie/widgetPiePercentageChart";
import widgetAirBubbleMap from "./map/widgetAirBubbleMap";
import widgetBarCirclechart from "./bar/widgetBarCirclechart";
import widgetBarManychart from "./bar/widgetBarManychart";
import widgetHebeiMap from "./map/widgetHebeiMap";
import widgetBarStackChart from "./bar/widgetBarStackChart";
import widgetLineStackChart from "./line/widgetLineStackChart";
import widgetBarCompareChart from "./bar/widgetBarCompareChart";
import widgetBorder from "./widgetBorder.vue";
import widgetDecorate from "./widgetDecorate.vue";
import widgetTurncard from "./widgetTurncard.vue";
import widgetPercent from "./widgetPercent.vue";
import widgetWater from "./widgetWater.vue";
import WidgetPictographchart from "./widgetPictographchart.vue";
import WidgetRadarchart from "./widgetRadarchart";
import WidgetDetails from "./widgetDetails.vue";


export default {
  name: "WidgetTemp",
  components: {
    widgetHref,
    widgetText,
    widgetBlock,
    WidgetMarquee,
    widgetTime,
    widgetImage,
    widgetSlider,
    widgetVideo,
    WidgetIframe,
    widgetBarchart,
    widgetGradientColorBarchart,
    widgetLinechart,
    widgetBarlinechart,
    WidgetPiechart,
    WidgetFunnel,
    WidgetGauge,
    WidgetPieNightingaleRoseArea,
    widgetTable,
    widgetMap,
    widgetPiePercentageChart,
    widgetAirBubbleMap,
    widgetBarCirclechart,
    widgetBarManychart,
    widgetHebeiMap,
    widgetBarStackChart,
    widgetLineStackChart,
    widgetBarCompareChart,
    widgetBorder,
    widgetDecorate,
    widgetTurncard,
    widgetPercent,
    widgetWater,
    WidgetPictographchart,
    WidgetRadarchart,
    WidgetDetails,
  },
  model: {
    prop: "value",
    event: "input"
  },
  props: { 
    type: String,
    value: {
      type: [Object],
      default: () => {}
    },
    componentParam: {
      type: [Object],
      default: () => {}
    }
  },

  data() {
    return {};
  },
  watch: {

  },
  mounted() {
    if(this.value.data.dataType === `dynamicData` ) {
      console.log(this.value.data)
    }
      
  },
  updated(){
  },
  methods: {}
};
</script>

<style scoped lang="scss"></style>
