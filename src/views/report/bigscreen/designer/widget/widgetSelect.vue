<template>
    <div :style="styleColor">
        <el-select :style="selectColor" v-model="value1" @change="selectData(value1)"  placeholder="请选择">
            <el-option
            v-for="item in selectList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
            </el-option>
        </el-select>
    </div>
</template>

<script>
import { Position } from 'monaco-editor';
export default {
    name: 'widgetSelect',
    props: {
        value: Object,
        ispreview: Boolean
    },
    data() {
        return {
            selectList: [
            ],
            value1: ``,
            key: `yxmc`,
            optionsData: {},
            options: {},
            optionsSetup: ``,
        }
    },
    computed: {
        styleColor(){
            return {
                height: `100%`,
                widthd: `100%`,
                position: 'relative',
                'border-radius': this.optionsSetup.borderRadius + 'px',
            }
        },
        selectColor(){
            return {
                position: 'absolute',
                left: this.optionsSetup.Transleft + `px`,
                top: this.optionsSetup.Transtop + `px`,
            }
        }
    },
    watch: {
        value: {
            handler(val) {
                this.options = val;
                this.optionsSetup = val.setup;
                this.optionsData = val.data;
                this.setOptionsData();
            },
            deep: true
        }
    },
    mounted() {
        this.options = this.value;
        this.optionsSetup = this.value.setup;
        this.optionsData = this.value.data;
        this.editorOptions();
    },
    methods: {
        selectData(value1) {
            let param = {};
            param[this.key] = value1;
            let data = {};
            data.list = this.value.setup.linkagelist;
            data.param = param;
            this.$emit( "changeSelect", data );
        },
        editorOptions() {
            this.setOptionsData();
        },
        setOptionsData() {
            const optionsData = this.optionsData; // 数据类型 静态 or 动态
            optionsData.dataType == "staticData"
                ? this.staticDataFn(optionsData.staticData)
                : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
        },
        staticDataFn(val) {
            const staticData = JSON.parse(val);
            let name = Object.keys(staticData[0])[0];
            this.selectList = [];
            for( let i =0; i<staticData.length; i++ ){
                this.selectList.push({
                    label: staticData[i][name],
                    value: staticData[i][name]
                })
            }
        },
        dynamicDataFn(val, refreshTime) {
            if (!val) return;
            if (this.ispreview) {
                this.getEchartData(val);
                this.flagInter = setInterval(() => {
                this.getEchartData(val);
                }, refreshTime);
            } else {
                this.getEchartData(val);
            }
        },
        getEchartData(val) {
            const data = this.queryEchartsData(val);
            data.then(res => {
                this.renderingFn(res);
            });
            },
            renderingFn(val) {
                const dynamicData = val;
                let name = Object.keys(dynamicData[0])[0];
                this.selectList = [];
                for( let i =0; i<dynamicData.length; i++ ){
                    this.selectList.push({
                        label: dynamicData[i][name],
                        value: dynamicData[i][name]
                    })
                }
        }
    },
};
</script>

<style lang="scss" scoped>

</style>