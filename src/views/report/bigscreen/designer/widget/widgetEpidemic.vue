
<template>
  <div class="text" :style="styleColor">
    <div class="boxleft" :style="imgsColor">
      <img v-if="greenshow" src="../../../../../../public/img/yiqing/green.jpg">
      <img v-if="yellowshow" src="../../../../../../public/img/yiqing/yellow.jpg">
      <img v-if="redshow" src="../../../../../../public/img/yiqing/red.jpg">
      <img v-if="tanshow" src="../../../../../../public/img/yiqing/tanchuang.jpg">

    </div>
    <div class="boxright" :style="textColor">
      <p :style="spanColor">健康码状态:
        <span class="blodfront" :style="{color: color}" >{{ optionsSetup.HSJG }}</span>
        <el-tooltip class="item" effect="dark" :content="content" placement="top-end">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </p>
      <p :style="spanColor">核酸时长: <span class="blodfront" :style="{color: color}" >{{ optionsSetup.DAYS }}</span>天</p>
      <p :style="spanColor">更新时间: {{ optionsSetup.GXSJ }}</p>
      <p :style="spanColor">检测疫苗: {{ optionsSetup.JCYM }}</p>
    </div>
  </div>
</template>

<script>
import {deepClone} from "@/util/util";

export default {
  name: "WidgetEpidemic",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {},
      optionsData: {},
      optionsSetup: ``,
      greenshow: false,
      yellowshow: false,
      redshow: false,
      tanshow: false,
      content: '',
      color: '#049861'
    };
  },
  computed: {
    transStyle() {
      return this.objToOne(this.options);
    },
    imgsColor() {
      return {
        "margin-left": this.optionsSetup.imgsleft + "px",
        "margin-top": this.optionsSetup.imgsright + "px",
        "width": this.optionsSetup.imgswidth + "px",
        "height": this.optionsSetup.imgsheight + "px",

      }
    },
    textColor() {
      return {
        "margin-left": this.optionsSetup.dynamicfontLeft + "px",

      }
    },
    spanColor() {
      return {
        color: this.optionsSetup.dynamiccolor + `!important`,
        "font-family":this.optionsSetup.dynamicfontFamily + `!important`,
        "font-weight": this.optionsSetup.dynamicfontWeight + `!important`,
        "font-size": this.optionsSetup.dynamicfontSize + "px" + `!important`,
        "letter-spacing": this.optionsSetup.dynamicletterSpacing + "px" + `!important`,
        "text-align": this.optionsSetup.dynamictextAlign + `!important`,
        "line-height": this.optionsSetup.dynamiclineHeight + "px" + `!important`,
        "margin": 0
      }
    },
    styleColor() {
      if(this.transStyle.w){
        return{
          position: this.ispreview ? "absolute" : "static",
          // color: this.options.setup.color,
          background: this.transStyle.background,
          width: `100%`,
          'min-width': '330px',
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          // border: this.transStyle.textBorder + "px solid #fff",
          // "border-color": this.transStyle.textBorderColor,
          "border-radius": this.transStyle.borderRadius + 'px',
          padding:this.transStyle.padding + "px",
          // cursor: this.transStyle.pointer
        }
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          // color: this.transStyle.color,
          "font-weight": this.transStyle.fontWeight,
          text: this.transStyle.text,
          "font-family":this.transStyle.fontFamily,
          "font-size": this.transStyle.fontSize + "px",
          "letter-spacing": this.transStyle.letterSpacing + "em",
          background: this.transStyle.background,
          "text-align": this.transStyle.textAlign,
          width: this.transStyle.width + "px",
          height: this.transStyle.height + "px",
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          border: this.transStyle.textBorder + "px solid #fff",
          "border-color": this.transStyle.textBorderColor,
          "border-radius": this.transStyle.textBorderRadius + "px",
          padding:this.transStyle.padding + "px",
          // cursor: this.transStyle.pointer
        };
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.options = val;
        this.optionsSetup = val.setup;
        this.optionsData = val.data;
        this.setOptionsData();

        // this.addPoint();
      },
      deep: true
    }
  },
  mounted() {
    this.options = this.value;
    this.optionsSetup = this.value.setup;
    this.optionsData = this.value.data;
    this.setOptionsData();
    // this.addPoint();
  },
  methods: {
    getcontent(data){
      if(data == 0){
        this.content = '状态正常';
      }
      if(data == 10){
        this.content = '白名单人员（等同状态正常）';
      }
      if(data == 11){
        this.content = '可能去过含有中高风险地区弹窗';
      }
      if(data == 20){
        this.content = '入境人员进京且完成社区隔离或行程轨迹对应风险等级均为低';
      }
      if(data == 30){
        this.content = '环京通勤人员，做核酸满足14天内阴性条件';
      }
      if(data == 1){
        this.content = '在社区系统中登记进行隔离中';
      }
      if(data == 21){
        this.content = '非健康宝注册用户（用户需在北京健康宝小程序端进行注册并查询状态）';
      }
      if(data == 31){
        this.content = '填报的行程轨迹对应风险等级存在中或高风险';
      }
      if(data == 4){
        this.content = '含有陆地口岸管控人员名单，限制进京的人员信息';
      }
      if(data == 40){
        this.content = '属于通勤人员，但核酸不满足14天内阴性证明条件';
      }
      if(data == 41){
        this.content = '入境人员进京但未在社区登记解除隔离';
      }
      if(data == 2){
        this.content = '被国家或北京两级卫健委列入需集中隔离或密切接触人员';
      }
      if(data == 88){
        this.content = '需要用户转到北京健康报小程序，进行行程轨迹申报操作';
      }
      if(data == null){
        this.content = '需要用户转到北京健康报小程序，进行行程轨迹申报操作';
      }
      if(data == 3){
        this.content = '可能含有中高风险地区管控人员名单，需要去所在社区进行登记';
      }
      if(data == 51){
        this.content = '属于入京人员，填报的行程轨迹是第风险地区人，人员但是无核酸的情况';
      }
    },
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    addPoint() {
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if( dynamicData ) {
        if( dynamicData && dynamicData.drillSetList.length > 0){
          this.$refs.span.style.cursor = "pointer"
        }
      }

    },
    // 数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      if (optionsData.dataType == "dynamicData") {
        this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
      } else {
        this.staticDataFn(optionsData.staticData)
      }
    },
    staticDataFn(val) {
      let data = val[0]
      console.log(`data`,data)

      if(data.COLOR == 0 || data.COLOR == 10 || data.COLOR == 20 || data.COLOR == 30 || data.COLOR == 40 || data.COLOR == '0' || data.COLOR == '10' || data.COLOR == '20' || data.COLOR == '30' || data.COLOR == '40' ){
        this.optionsSetup.HSJG = '绿码'
        this.color = '#58AD61'
        this.greenshow = true;
        this.yellowshow = false;
        this.redshow = false;
        this.tanshow = false;
      }
      if(data.COLOR == 11 || data.COLOR == '11'){
        this.color = '#FDC112'
        this.yellowshow = true;
        this.greenshow = false;
        this.redshow = false;
        this.tanshow = false;
        this.optionsSetup.HSJG = '本地黄弹窗'
      }
      if(data.COLOR == 1 || data.COLOR == '1'){
        this.color = '#FDC112'
        this.yellowshow = true;
        this.greenshow = false;
        this.redshow = false;
        this.tanshow = false;
        this.optionsSetup.HSJG = '黄码'
      }
      if(data.COLOR == 2 || data.COLOR == '2'){
        this.color = '#D81E06'
        this.redshow = true;
        this.greenshow = false;
        this.yellowshow = false;
        this.tanshow = false;
        this.optionsSetup.HSJG = '红码'
      }
      if(data.COLOR == 21 || data.COLOR == 31 || data.COLOR == 4 || data.COLOR == 41 || data.COLOR == 88 || data.COLOR == null || data.COLOR == 3 || data.COLOR == 51 || data.COLOR == '21' || data.COLOR == '31' || data.COLOR == '4' || data.COLOR == '41' || data.COLOR == '88' || data.COLOR == '3' || data.COLOR == '51'){
        this.yellowshow = false;
        this.greenshow = false;
        this.redshow = false;
        this.tanshow = true;
        this.optionsSetup.HSJG = '弹窗'
        this.color = '#D81E06'
      }
      this.getcontent(data.COLOR)
      this.optionsSetup.DAYS = data.DAYS;
      this.optionsSetup.GXSJ = data.GXSJ;
      this.optionsSetup.JCYM = data.XGYM;

    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const datas = this.queryEchartsData(val);
      datas.then(res => {
        if( res.length > 0){
          let data = res[0]
          console.log(`data`,data)
          if(data.COLOR == 0 || data.COLOR == 10 || data.COLOR == 20 || data.COLOR == 30 || data.COLOR == 40 || data.COLOR == '0' || data.COLOR == '10' || data.COLOR == '20' || data.COLOR == '30' || data.COLOR == '40' ){
            this.optionsSetup.HSJG = '绿码'
            this.color = '#58AD61'
            this.greenshow = true;
            this.yellowshow = false;
            this.redshow = false;
            this.tanshow = false;
          }
          if(data.COLOR == 11 || data.COLOR == '11'){
            this.color = '#FDC112'
            this.yellowshow = true;
            this.greenshow = false;
            this.redshow = false;
            this.tanshow = false;
            this.optionsSetup.HSJG = '本地黄弹窗'
          }
          if(data.COLOR == 1 || data.COLOR == '1'){
            this.color = '#FDC112'
            this.yellowshow = true;
            this.greenshow = false;
            this.redshow = false;
            this.tanshow = false;
            this.optionsSetup.HSJG = '黄码'
          }
          if(data.COLOR == 2 || data.COLOR == '2'){
            this.color = '#D81E06'
            this.redshow = true;
            this.greenshow = false;
            this.yellowshow = false;
            this.tanshow = false;
            this.optionsSetup.HSJG = '红码'
          }
          if(data.COLOR == 21 || data.COLOR == 31 || data.COLOR == 4 || data.COLOR == 41 || data.COLOR == 88 || data.COLOR == null || data.COLOR == 3 || data.COLOR == 51 || data.COLOR == '21' || data.COLOR == '31' || data.COLOR == '4' || data.COLOR == '41' || data.COLOR == '88' || data.COLOR == '3' || data.COLOR == '51'){
            this.yellowshow = false;
            this.greenshow = false;
            this.redshow = false;
            this.tanshow = true;
            this.optionsSetup.HSJG = '弹窗'
            this.color = '#D81E06'
          }
          this.getcontent(data.COLOR)
          this.optionsSetup.DAYS = data.DAYS;
          this.optionsSetup.GXSJ = data.GXSJ;
          this.optionsSetup.JCYM = data.XGYM;
        }
        // this.addPoint();
        this.$forceUpdate();
      });
    }
  }
};
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.text_p:hover{
  cursor: pointer ;
}
.boxleft{
  float: left;
}
.boxleft>img{
  height: 100%;
  width: 100%;
}
.boxright{
  float: left;
}
.blodfront{
  font-weight: bold;
  //color: #049861;
}
.el-icon-info{
  margin-left: 5px;
}
</style>
