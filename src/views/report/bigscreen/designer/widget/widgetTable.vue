<template>
  <div :style="styleObj">
    <superslide v-if="hackReset" :options="options" class="txtScroll-top">
      <div class="title">
        <div
          v-for="(item, index) in header"
          :style="headerTableStlye"
          :key="index"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="bd" ref="bd">
        <ul class="infoList" :style="{
          height: (optionsSetUp.rollVis*40)+'px',overflow: optionsSetUp.isRoll ? 'hidden !important': 'auto !important'
        }">
          <li v-for="(item, index) in list" :key="index" >
            <div
              v-for="(itemChild, idx) in header"
              :key="idx"
              :style="[bodyTableStyle, bodyTable(index),{color: tocolor(item,itemChild),'background-color': item.backgroundColor,}]"
              :title="item[itemChild.key]"
            >
              {{ item[itemChild.key] }}
            </div>
          </li>
        </ul>
      </div>
    </superslide>
  </div>
</template>
<script>
export default {
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      hackReset: true,
      options: {
        titCell: ".hd ul",
        mainCell: ".bd ul",
        effect: "topLoop",
        autoPage: true,
        autoPlay: true,
        vis: 5
      },
      header: [],
      list: [],
      optionsSetUp: {},
      optionsPosition: {},
      optionsData: {}
    };
  },
  computed: {
    styleObj() {
      const allStyle = this.optionsPosition;
      if( allStyle.w ){
        return {
          position: this.ispreview ? "absolute" : "static",
          width: `100%`,
          height: `calc(100% - ${allStyle.titlelineHeight}px)`,
          // 'border-radius': this.optionsSetup.borderRadius + 'px',
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: allStyle.width + "px",
          height: allStyle.height + "px",
          left: allStyle.left + "px",
          top: allStyle.top + "px"
        };
      }

    },
    headerTableStlye() {
      const headStyle = this.optionsSetUp;
      return {
		    height: headStyle.headHeight + "px",
		    "line-height": headStyle.headHeight + "px",
        "text-align": headStyle.textAlign,
        "font-size": headStyle.fontSize + "px",
        "border-style": headStyle.isLine ? "solid" : "none",
        "border-width": headStyle.borderWidth + "px",
        "border-color": headStyle.borderColor,
        display: headStyle.isHeader ? "block" : "none",
        color: headStyle.headColor,
        "background-color": headStyle.headBackColor
      };
    },
    bodyTableStyle() {
      const bodyStyle = this.optionsSetUp;
      return {
		    height: bodyStyle.bodyHeight + "px",
		    "line-height": bodyStyle.bodyHeight + "px",
        "text-align": bodyStyle.textAlign,
        "font-size": bodyStyle.fontSize + "px",
        "border-style": bodyStyle.isLine ? "solid" : "none",
        "border-width": bodyStyle.borderWidth + "px",
        "border-color": bodyStyle.borderColor,
        color: bodyStyle.bodyColor,
        // "background-color": bodyStyle.tableBgColor,
        "overflow": 'hidden',
        "white-space": 'nowrap',
        "text-overflow": 'ellipsis',
      };
    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsSetUp = val.setup;
        this.optionsPosition = val.position;
        this.optionsData = val.data;
        this.initData();
      },
      deep: true
    }
  },
  mounted() {
    this.optionsSetUp = this.value.setup;
    this.optionsPosition = this.value.position;
    this.optionsData = this.value.data;
    this.initData();
  },
  methods: {
    isInRange(number, lowerBound, upperBound) {
      return number >= lowerBound && number <= upperBound;
    },
    tocolor(item,itemChild) {
      if(itemChild.is){
        let color = '';
        let value = item[itemChild.key]
        if(itemChild.rowform){
          itemChild.rowform.dynamic.forEach(dyn =>{
            if(this.isInRange(value,dyn.min,dyn.max)){
              color = dyn.color;
            }
          })
        }
        return color
      }else {

        return this.optionsSetUp.bodyColor
      }
    },
    initData() {

      // this.handlerRollFn();
      // this.handlerHead();
        this.handlerData();
    },
    handlerRollFn() {
      const options = this.options;
      const rollSet = this.optionsSetUp;
      if(!rollSet.isRoll){
        this.options = {};
      }else {
        options.autoPlay = rollSet.isRoll;
        options.delayTime = rollSet.rollTime;
        options.scroll = rollSet.rollNumber;
        options.vis = rollSet.rollVis;
        this.options = options;
      }

      this.hackResetFun();
    },
    handlerHead() {
      const head = this.optionsSetUp.dynamicAddTable;
      this.header = head;
      this.list.forEach(li=>{
        this.header.forEach(heads=>{
          if(heads.rowform){
            heads.rowform.dynamic.forEach(dyn =>{
              let value = li[heads.key]
              if(heads.is && this.isInRange(value,dyn.min,dyn.max) && dyn.backcolor){
                li.backgroundColor = dyn.backcolor;
              }
            })
          }
        })
      })
      console.log(`this.list`,this.list)
      console.log(`this.header`,this.header)
    },
    handlerData() {
      const tableData = this.optionsData;
      tableData.dataType == "staticData"
        ? this.handlerStaticData(tableData.staticData)
        : this.handlerDymaicData(tableData.dynamicData, tableData.refreshTime);
    },
    handlerStaticData(data) {
      this.list = data;
      this.handlerRollFn();
      this.handlerHead();
    },
    handlerDymaicData(data, refreshTime) {
      if (!data) return;
      if (this.ispreview) {
        this.getEchartData(data);
        this.flagInter = setInterval(() => {
          this.getEchartData(data);
        }, refreshTime);
      } else {
        this.getEchartData(data);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.list = res;

        this.handlerRollFn();
        this.handlerHead();
      });
    },
    // vue hack 之强制刷新组件
    hackResetFun() {
      this.hackReset = false;
      this.$nextTick(() => {
        this.hackReset = true;
      });
    },
    // 计算 奇偶背景色
    bodyTable(index) {
      if (index % 2) {
        return {
          "background-color": this.optionsSetUp.eventColor
        };
      } else {
        return {
          "background-color": this.optionsSetUp.oldColor
        };
      }
    }
  }
};
</script>
<style lang="scss" scoped>
/* 本例子css */
.txtScroll-top {
  overflow: hidden;
  position: relative;
}
.title {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.title > div {
  width: 100%;
}
.txtScroll-top .bd {
  width: 100%;
}
.txtScroll-top .infoList li {
  display: flex;
  flex-direction: row;
}
.txtScroll-top .infoList li > div {
  height: 40px;
  line-height: 40px;
  width: 100%;
  padding-left:10px;
}
.txtScroll-top .infoList li:nth-child(n) {
  background: #1f92be11;
}
.txtScroll-top .infoList li:nth-child(2n) {
  background: #1f92be01;
}
.txtScroll-top .bd .infoList{
    margin: 0;
    padding: 0;
}
//.infoList{
//  overflow: auto !important;
//}
//::v-deep .tempWrap{
//    overflow: auto !important;
//}
</style>
