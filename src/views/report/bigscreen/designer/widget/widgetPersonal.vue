<template>
    <div ref="personal" :style="styleColor">
        <div :style="topColor">
            <p :style="pColor">{{ optionsSetup.titleinner }}</p>
        </div>
        <div :style="bottomColor">
            <div :style="{
                width: '100%',
                position: 'absolute',
                top: '20px',
                textAlign: 'center'
            }">
                <p :style="{
                    fontSize: optionsSetup.nameSize  + 'px',
                    fontWeight: optionsSetup.nameFontWeight,
                    lineHeight: optionsSetup.namelineHeight  + 'px',
                    color: optionsSetup.nameColor,
                    marginTop: optionsSetup.nameTop  + 'px',
                    marginBottom: '0px'
                }">{{ humanName }}</p>
                <p :style="{
                    fontSize: optionsSetup.xhSize  + 'px',
                    lineHeight: optionsSetup.xhlineHeight  + 'px',
                    marginTop: optionsSetup.xhTop  + 'px',
                    marginBottom: '0px',
                    color: optionsSetup.xhColor,
                }">{{ humanCode }}</p>
                <ul :style="{
                    textAlign: 'left',
                    'list-style-type': optionsSetup.listType,
                    marginTop: '30px'
                }">
                    <li v-for="(item,index) in listName" :key="index" :style="{
                        // position: 'relative',
                        lineHeight: '25px',
                        height: '25px',
                        color: optionsSetup.infoColor,
                        lineHeight: optionsSetup.infolineHeight  + 'px',
                        fontSize: optionsSetup.infoSize  + 'px',
                        marginLeft: optionsSetup.infoleft  + '%'
                        }"><span class="nowrap" :title="item"
                                 :style="{display: 'block', float: 'left',width: `130px`,'text-align': 'right'}">{{ item }}:</span><span
                            class="nowrap" :title="list[item]"
                            :style="{'margin-left':  optionsSetup.infoRsleft  + '%', width: `calc(100% - 135px - ${optionsSetup.infoRsleft}%)` , display: 'block','word-break': 'break-all', float: 'right',color: ( list.length > 1 && list[item] !== null && list[item].indexOf('异常') !== -1) ? itemcolor : optionsSetup.infoColor }">{{ list[item]  }}</span>
                    </li>
                </ul>
            </div>
        </div>
        <div v-if="optionsSetup.imgShow" :style="imgColor">
            <img :style="{
                      width: '100%',
                      height: '100%',
                  }" :src="this.avatarUrl" alt="">
        </div>
    </div>
</template>

<script>
    import {detailBysetId, getCleartextTelmobile} from "@/api/bigscreen";
    import {GetPhoto} from "@/api/settings";

    export default {
        name: 'widgetPersonal',
        props: {
            value: Object,
            ispreview: Boolean
        },
        data() {
            return {
                list: {},
                listName: [],
                listValue: [],
                humanName: '',
                humanCode: '',
                avatarUrl: '',
                optionsData: {},
                options: {},
                optionsSetup: {},
                itemcolor: 'red',
            };
        },
        computed: {
            styleColor() {
                return {
                    height: 'calc(100% - 20px)',
                    width: '100%',
                    overflow: 'hidden',
                    position: 'relative',
                    'border-radius': this.optionsSetup.borderRadius + 'px',
                }
            },
            topColor() {
                return {
                    height: this.optionsSetup.topheight + 'px',
                    width: '100%',
                    background: `${this.optionsSetup.titlecolor} url("${this.optionsSetup.imageAdress}") ${this.optionsSetup.bgleft}px ${this.optionsSetup.bgtop}px/${this.optionsSetup.bgwidth}px ${this.optionsSetup.bgheight}px no-repeat`,
                    // backgroundSize: `100px 100px !important`,
                    'border-top-right-radius': this.optionsSetup.borderRadius + 'px',
                    'border-top-left-radius': this.optionsSetup.borderRadius + 'px',
                    // backgroundSize: `100% auto`,
                }
            },
            pColor() {
                return {
                    // margin: '10px 0 0 5px'
                    position: 'absolute',
                    top: this.optionsSetup.titletop + 'px',
                    left: this.optionsSetup.titleleft + 'px',
                    'font-size': this.optionsSetup.titleSize + 'px',
                    'font-weight': this.optionsSetup.titleFontWeight,
                    "font-family": this.optionsSetup.titleFamily + `!important`,
                    "line-height": this.optionsSetup.titlelineHeight + "px",
                    color: this.optionsSetup.titleColor,
                }
            },
            bottomColor() {
                return {
                    height: 'calc(100% - 200px)',
                    width: '100%',
                    background: this.optionsSetup.bodycolor,
                    // 'border-radius': this.optionsSetup.borderRadius + 'px',
                    position: 'relative',
                    'border-bottom-right-radius': this.optionsSetup.borderRadius + 'px',
                    'border-bottom-left-radius': this.optionsSetup.borderRadius + 'px',
                    'z-index': '99'
                }
            },
            imgColor() {
                let a = this.optionsSetup.imgLeft + '%';
                let b = this.optionsSetup.imgWidth / 2 + 'px';
                return {
                    height: this.optionsSetup.imgHeight + 'px',
                    width: this.optionsSetup.imgWidth + 'px',
                    'border-radius': this.optionsSetup.imgBorderR + '%',
                    border: '2px solid',
                    'border-color': this.optionsSetup.borderColor + '!important',
                    'border-width': this.optionsSetup.borderWidth + 'px' + '!important',
                    position: 'absolute',
                    top: this.optionsSetup.imgTop + '%',
                    left: `calc(${a} - ${b})`,
                    overflow: 'hidden',
                    'z-index': '999',
                }
            },

        },
        watch: {
            value: {
                handler(val) {
                    this.options = val;
                    this.optionsSetup = val.setup;
                    this.optionsData = val.data;
                    // this.editorOptions();
                    this.setOptionsData();
                },
                deep: true
            }
        },
        mounted() {
            this.options = this.value;
            this.optionsSetup = this.value.setup;
            this.optionsData = this.value.data;
            this.setOptionsData();
        },

        methods: {
            // 数据解析
            setOptionsData() {
                const optionsData = this.optionsData; // 数据类型 静态 or 动态
                if (optionsData.dataType == "dynamicData") {
                    this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
                } else {
                    this.staticDataFn(optionsData.staticData);
                }
            },
            staticDataFn(val) {
                let columns = [];
                console.log(`staticDataFn`, val)
                this.list = val[0];
                // let keys = Object.keys(this.list);
                this.listName = columns.length == 0 ? Object.keys(this.list) : columns;
                this.humanName = this.list[this.listName[0]]
                this.humanCode = this.list[this.listName[1]];
                // this.listValue = Object.values(this.list);
                this.$forceUpdate();
            },
            dynamicDataFn(val, refreshTime) {
                if (!val) return;
                if (this.ispreview) {
                    this.getEchartData(val);
                    this.flagInter = setInterval(() => {
                        this.getEchartData(val);
                    }, refreshTime);
                } else {
                    this.getEchartData(val);
                }
            },

            getEchartData(val) {
                let columns = [];
                detailBysetId({id: val.setCode}).then(res => {
                    if (res.data.code === '00000' && res.data.info.fieldConfig) {
                        let fieldConfig = JSON.parse(res.data.info.fieldConfig);
                        for (let i = 0; i < fieldConfig.length; i++) {
                            columns.push(fieldConfig[i].field);
                        }
                        const data = this.queryEchartsData(val);
                        data.then(res => {
                            // console.log(`getEchartData`, res)
                            this.list = res[0];
                            console.log(`this.list`, this.list);
                            if (this.list) {
                                Object.keys(this.list).forEach(res => {
                                    if (res.indexOf("telmobile1") != -1 || res.indexOf("TELMOBILE1") != -1 || res.indexOf("手机") != -1 || res.indexOf("手机号码") != -1) {
                                        getCleartextTelmobile({telmobile: this.list[res]}).then(data => {
                                            console.log(`getCleartextTelmobile`, data);
                                            this.list[res] = data.data.info;
                                        });
                                    }
                                });
                                console.log(`columns`, columns)

                                // let keys = Object.keys(this.list);
                                this.listName = columns.length == 0 ? Object.keys(this.list) : columns;
                                console.log(`list`, this.listName, this.list)
                                this.humanName = this.list[this.listName[0]]
                                this.humanCode = this.list[this.listName[1]];
                            }
                            // this.listValue = Object.values(this.list);
                            this.getPhoto();
                            this.$forceUpdate();
                        });
                    }
                }).catch(res => {
                    this.$message.error(res.info);
                });
            },
            getPhoto() {
                GetPhoto(this.humanCode).then(res => {
                    if (res.data.code === "00000") {
                        // console.log(`GetPhoto`,res);
                        // this.avatarUrl = res.data.info;
                        this.avatarUrl = '/file/view/' + this.humanCode;
                        // console.log(`this.avatarUrl`,this.avatarUrl);
                    }
                })
            },
        },
    };
</script>

<style lang="scss" scoped>
    .e {
        border: 0ch !important;
    }

    .nowrap {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
</style>
