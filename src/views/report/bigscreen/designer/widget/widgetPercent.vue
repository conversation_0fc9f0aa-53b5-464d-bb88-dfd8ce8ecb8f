<template>
<div>
  <dv-percent-pond class="text" :style="styleColor" :key="percentNum" autoresize :config="config" />
</div>
    

    

</template>

<script>
export default {
  name: 'widgetPercent',
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
        config:{
            value: ``,
            colors: `[]`,
            textColor:``,
            borderWidth:``,
            borderGap:``,
            borderRadius:``,        
        },
        percentNum: 1,
        optionsData: {},
        optionsStyle: {},        
    };
  },
  computed: {
    // transStyle() {
    //   return this.objToOne(this.options);
    // },
    styleColor() {
      return {
        position: this.ispreview ? "absolute" : "static",
        width: this.optionsStyle.width + "px",
        height: this.optionsStyle.height + "px",
        left: this.optionsStyle.left + "px",
        top: this.optionsStyle.top + "px",
      };
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = val.setup;
        this.optionsStyle = val.position;
        this.config.value = val.setup.Number;      
        this.config.textColor = val.setup.fontColors;
        this.config.borderWidth = val.setup.borderWidth;
        this.config.borderGap = val.setup.borderGap;
        this.config.borderRadius = val.setup.borderRadius;
        this.optionsData = val.data;
        this.percentNum++
        this.config.colors = `${val.setup.Colors} , ${val.setup.Colors2}`
        this.config.colors = this.config.colors.split(` , `)

        // this.setOptionsColor()
        this.editorOptions();
        // this.setOptionsData();
        // this.setCon();
        // this.config = { ...this.config }   

      },
      deep: true 
    }
  },
  created() {
    this.config = this.value.setup;
    this.optionsData = this.value.data;
    this.optionsStyle = this.value.position;
    this.config.value = this.value.setup.Number;    
    this.config.textColor = this.value.setup.fontColors;
    this.config.borderWidth = this.value.setup.borderWidth;
    this.config.borderGap = this.value.setup.borderGap;
    this.config.borderRadius = this.value.setup.borderRadius;
    this.percentNum++
    this.config.colors = `${this.value.setup.Colors} , ${this.value.setup.Colors2}`
    // this.setOptionsColor()
    this.editorOptions();
    // this.setOptionsData();
    // this.setCon();
    // this.config = { ...this.config }
    this.config.colors = this.config.colors.split(` , `)
  },
  methods: {
    editorOptions(){
      this.setOptionsData();
      this.setCon();
      // this.setOptionsColor()
    },
    setOptionsColor() {
      const optionsCollapse = this.config;
      const customColor = optionsCollapse.customColor;
      if (!customColor) return;
      const arrColor = [];
      for (let i = 0; i < customColor.length; i++) {
        arrColor.push(customColor[i].color);
      }
      this.config.colors = arrColor;
      this.options = Object.assign({}, this.options);
    },
    
    setCon(){
      // const configColors = this.config.Colors;
      // const configColors2 = this.config.Colors2;      
      // this.config.colors = [{configColors},{configColors2}]
      if(this.config.value>100){
        this.config.value = 100
      }
      if(this.config.value<0){
        this.config.value = 0
      }
    },
    // 数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      if (optionsData.dataType == "dynamicData") {
        this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
      } else {};
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.value.setup.Number = res[0].value;
        this.$forceUpdate();
      });
    }
  }
};
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
