<template>
  <transition :name="transStyle.transition">
    <div v-show="show" class="imagebox" :style="styleColor" >
      <!--    <transition name="el-zoom-in-top">-->
      <img
          :class="transStyle.startRotate ? transStyle.animationType : ''"
          :style="imgStyle"
          :src=" this.dynamicAdress !== `` ? this.dynamicAdress : imgStyle.imageAdress "
          alt=""

      />
      <!--    </transition>-->

    </div>
  </transition>

</template>
<script>
export default {
  name: "WidgetImage",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean,
  },
  data() {
    return {
      options: {},
      optionsData: {},
      dynamicAdress: ``,
      show: false,
    };
  },
  computed: {
    transStyle() {
      return this.objToOne(this.options);
    },
    styleColor() {
      if (this.transStyle.w) {
        return {
          position: this.ispreview ? "absolute" : "static",
          background: this.transStyle.background,
          "text-align": this.transStyle.textAlign,
          width: `100%`,
          height: `calc(100% - ${this.transStyle.titlelineHeight}px)`,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          background: this.transStyle.background,
          "text-align": this.transStyle.textAlign,
          width: this.transStyle.width + "px",
          height: this.transStyle.height + "px",
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          transition: `all ${this.transStyle.transitionTime}s`,
        };
      }
    },
    imgStyle() {
      return {
        imageAdress: this.transStyle.imageAdress,
        "border-radius": this.transStyle.borderRadius + "px",
        opacity: this.transStyle.transparency / 100,
        "animation-duration": `${this.transStyle.animationTime}s`,
      };
    },
  },
  watch: {
    value: {
      handler(val) {
        this.options = val;
        this.optionsData = val.data;
        this.editorOptions();
      },
      deep: true,
    },
  },
  created() {
    this.options = this.value;
    this.optionsData = this.value.data;
    this.editorOptions();
  },
  mounted() {
    setTimeout(()=>{
      this.show = true;
    },(this.transStyle.startTime + '000'))
  },
  methods: {
    editorOptions() {
      this.setOptionsData();
    },
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    },
    staticDataFn(val) {
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then((res) => {
        if (res.series && res.series.length > 0) {
          this.dynamicAdress=res.series[0].data;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">

/* 第一次点击 透明度由1变成0 ，时长5s*/
/* 第二次点击 透明度由0变成1 ，时长5s */
//.fade-enter-active,.fade-leave-active{
//  transition: opacity 3s;
//}

.fade-enter,.fade-leave-to{
  opacity: 0
}
.imagebox {
  width: 100%;
  height: 100%;
  //overflow: hidden;
}
.imagebox img {
  width: 100%;
  height: 100%;
}
.startImg {
  animation: turn 5s linear infinite;
}
.startImg2 {
  animation: turn2 5s linear infinite;
}
.startImg3 {
  animation: turn3 5s linear infinite;
}
.startImg4 {
  animation: turn4 5s linear infinite;
}
.startImg5 {
  animation: turn5 5s linear infinite;
}
@keyframes turn {
  0% {
    -webkit-transform: rotate(0deg);
  }
  25% {
    -webkit-transform: rotate(90deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(270deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes turn2 {
  0% {
    //-webkit-transform: rotate(0deg);
    height: 100%;
    bottom: 0;
    left: 0;
    position: absolute;
  }
  25% {
    //-webkit-transform: rotate(90deg);
    height: 110%;
    bottom: 0;
    left: 0;

    position: absolute;
  }
  50% {
    //-webkit-transform: rotate(180deg);
    height: 120%;
    bottom: 0;
    left: 0;

    position: absolute;
  }
  75% {
    //-webkit-transform: rotate(270deg);
    height: 110%;
    bottom: 0;
    left: 0;

    position: absolute;
  }
  100% {
    //-webkit-transform: rotate(360deg);
    height: 100%;
    bottom: 0;
    left: 0;

    position: absolute;
  }
}
@keyframes turn3 {
  0% {
    //-webkit-transform: rotate(0deg);
    opacity: 0.2;
  }
  25% {
    //-webkit-transform: rotate(90deg);
    opacity: 0.5;

  }
  50% {
    //-webkit-transform: rotate(180deg);
    opacity: 1;

  }
  75% {
    //-webkit-transform: rotate(270deg);
    opacity: 0.5;

  }
  100% {
    //-webkit-transform: rotate(360deg);
    opacity: 0.2;

  }
}
@keyframes turn4 {
  0% {
    transform:scale(1);
  }
  25% {
    transform:scale(1.1);
  }
  50% {
    transform:scale(1.2);
  }
  75% {
    transform:scale(1.1);
  }
  100% {
    transform:scale(1);
  }
}
@keyframes turn5 {
  0% {
    transform:scaleX(1);
  }
  25% {
    transform:scaleX(1.1);
  }
  50% {
    transform:scaleX(1.2);
  }
  75% {
    transform:scaleX(1.1);
  }
  100% {
    transform:scaleX(1);
  }
}
</style>
