<template>
  <div :style="styleObj">
    <v-chart v-if="dataShow" :options="options" autoresize @click="chartEvents"/>
    <div v-if="nodataShow" :style="{
      position: 'relative',
       width: `100%`,
      height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
      background: this.optionsSetup.background,
      'border-radius': this.optionsSetup.borderRadius + 'px',
      textAlign: 'center',

    }"> <img :style="{
        position: 'absolute',
        left: 'calc(50% - 64px)',
        top: 'calc(50% - 64px)',
      }" src="../../../../../../public/img/code/ksj.png"> </div>
  </div>
</template>

<script>
import {deepClone} from '@/util/util'

export default {
  name: "WidgetPiechart",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      titleParam: ``,
      dataShow: true,
      nodataShow: false,
      options: {
        title: {
          text: "",
          left: "center",
          textStyle: {
            color: "#fff"
          }
        },
        legend: {
          orient: "vertical",
          left: "left",
          textStyle: {
            color: "#fff"
          }
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            data: [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)"
              }
            }
          }
        ]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsCollapse: {}, // 图标属性
      optionsSetup: {},
      max: '',
      min: '',
      sum: 0,
      avg: '',
    };
  },
  computed: {
    styleObj() {
      if(this.optionsStyle.w){
        return{
          width: `100%`,
          height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          background: this.optionsSetup.background,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        }
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
          background: this.optionsSetup.background
        };
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsCollapse = val.collapse;
        this.optionsSetup = val.setup;
        this.editorOptions();
      },
      deep: true
    },
  },
  created() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.collapse;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      console.log(`params`,params);
      console.log(`value`,this.value);
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
        console.log(`dynamicData`,dynamicData);
      }

    },
    // 修改图标options属性
    editorOptions() {
      this.setOptionsTitle();
      this.setOptionsValue();
      this.setOptionsTooltip();
      this.setOptionsLegend();
      this.setOptionsColor();
      this.setOptionsData();
      this.setOptionsPiechartStyle();
    },
    // 饼图样式
    setOptionsPiechartStyle(){
      const optionsCollapse = this.optionsSetup;
      if (this.optionsSetup.piechartStyle == "shixin") {
        this.options.series[0]['radius'] = optionsCollapse.outsideSize + "%"
      }else if (this.optionsSetup.piechartStyle == "kongxin"){
        this.options.series[0]['radius'] = [optionsCollapse.insideSize + "%", optionsCollapse.outsideSize + "%"]
      }else {}
    },
    // 标题设置
    setOptionsTitle() {
      const optionsCollapse = this.optionsSetup;
      const title = {};
      title.show = optionsCollapse.isNoTitle;
      // if( optionsCollapse.titleText.includes("max") ) {
      //   //获取字段再文本字符串的数组下标索引
      //   let cc = optionsCollapse.titleText.indexOf("max")
      //   let ee = optionsCollapse.titleText.replace("max","")
      //   let ff = ee.split("")
      //   ff.splice(cc,0,this.max)
      //   console.log(this.max)
      //   // let hh = ff.join("")
      //   // console.log(hh)
      // }
      title.text = optionsCollapse.titleText;
      title.left = optionsCollapse.textAlign;
      title.textStyle = {
        color: optionsCollapse.textColor,
        fontSize: optionsCollapse.textFontSize,
        fontWeight: optionsCollapse.textFontWeight
      };
      title.subtext = optionsCollapse.subText;
      title.subtextStyle = {
        color: optionsCollapse.subTextColor,
        fontWeight: optionsCollapse.subTextFontWeight,
        fontSize: optionsCollapse.subTextFontSize
      };
      this.options.title = title;
    },
    // 数值设定
    setOptionsValue() {
      const optionsCollapse = this.optionsSetup;
      const series = this.options.series;
      const textValue = optionsCollapse.textValue ? "{b}：" : "" ;
      const numberValue = optionsCollapse.numberValue ? "{c}" : "";
      const percentage = optionsCollapse.percentage ? "({d})%" : "";
      const label = {
        show: optionsCollapse.isShow,
        formatter: `{a|${textValue}${numberValue} \n ${percentage}}`,
        lineHeight: 15,
        rich: {
          a: {
            padding: [-30, 15, -20, 15],
            color: optionsCollapse.subTextColor,
            fontSize: optionsCollapse.fontSize,
            fontWeight: optionsCollapse.fontWeight
          }
        },
        fontSize: optionsCollapse.fontSize,

        fontWeight: optionsCollapse.optionsCollapse
      };
      const labelLine= {
        show: optionsCollapse.isShowLine,//数据标签引导线
        length: optionsCollapse.lineLength,
        lineStyle: {
          width: 1,
          type: 'solid'
        }
      }
      for (const key in series) {
        if (series[key].type == "pie") {
          series[key].label = label;
          series[key].labelLine = labelLine;
        }
      }
    },
    // 提示语设置 tooltip
    setOptionsTooltip() {
      const optionsCollapse = this.optionsSetup;
      const tooltip = {
        trigger: "item",
        show: true,
        textStyle: {
          color: optionsCollapse.lineColor,
          fontSize: optionsCollapse.fontSize
        }
      };
      this.options.tooltip = tooltip;
    },
    // 图例操作 legend
    setOptionsLegend() {
      const optionsCollapse = this.optionsSetup;
      const legend = this.options.legend;
      legend.show = optionsCollapse.isShowLegend;
      if(optionsCollapse.lateralPosition == "left"){
        legend.left = "left"
      }else if(optionsCollapse.lateralPosition == "right"){
        legend.left = "right"
      }else{
        legend.left = "center";
      }
      if(optionsCollapse.longitudinalPosition == "top"){
        legend.top = "top"
      }else if(optionsCollapse.longitudinalPosition == "bottom"){
        legend.top = "bottom"
      }else{
        legend.top = "middle";
      }
      legend.orient = optionsCollapse.layoutFront;
      legend.textStyle = {
        color: optionsCollapse.lengedColor,
        fontSize: optionsCollapse.lengedFontSize
      };
      legend.itemWidth = optionsCollapse.lengedWidth;
	    legend.itemHeight = optionsCollapse.lengedHeight;
    },
    // 图例颜色修改
    setOptionsColor() {
      const optionsCollapse = this.optionsSetup;
      const customColor = optionsCollapse.customColor;
      if (!customColor) return;
      const arrColor = [];
      for (let i = 0; i < customColor.length; i++) {
        arrColor.push(customColor[i].color);
      }
      this.options.color = arrColor;
      this.options = Object.assign({}, this.options);
    },
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    },
    staticDataFn(val) {
      const staticData = JSON.parse(val);
      this.dataShow = true;
      this.nodataShow = false;
      for (const key in this.options.series) {
        if (this.options.series[key].type == "pie") {
          this.options.series[key].data = staticData;
        }
      }
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.renderingFn(res);
      });
    },
    renderingFn(val) {
      let list = [];
      let namelist = [];
      for ( let i =0;i<val.length;i++) {
        list.push(val[i].value)
        namelist.push(val[i].name)
      }
      this.titleParam = this.value.setup.titleParam;
      //最大值
      if( this.options.title.text.includes("max") ) {
        this.max = Math.max(...list)
        let name = namelist[list.indexOf(this.max)]
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("Maxname",name);
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("max",this.max);
        }
      }
      //最小值
      if( this.options.title.text.includes("min") ) {
        this.min = Math.min(...list)
        let name = namelist[list.indexOf(this.min)]
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("Minname",name);
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("min",this.min);
        }
      }
      //总和
      if( this.options.title.text.includes("sum") ) {
        for(let i =0;i<list.length;i++) {
          this.sum += list[i]
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("sum",this.sum);
        }
      }
      //平均值
      if( this.options.title.text.includes("svg") ) {
        for(let i =0;i<list.length;i++) {
          this.sum += list[i]
        }
        this.svg = Math.round(this.sum / list.length)
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("svg",this.svg);
        }
      }
      console.log(`pie`,val)
      if (val.length > 0 && typeof val[0].value == 'number') {
        this.dataShow = true;
        this.nodataShow = false;
      } else {
        this.dataShow = false;
        this.nodataShow = true;
      }
      for (const key in this.options.series) {
        if (this.options.series[key].type == "pie") {
          this.options.series[key].data = val;
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
