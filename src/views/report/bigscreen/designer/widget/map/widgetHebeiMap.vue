<template>
  <div :style="styleObj">
    <v-chart :options="options" autoresize/>
  </div>
</template>
<script>
import echarts from "echarts";
import "../../../../../../../node_modules/echarts/map/js/province/hebei.js";
//https://www.makeapie.com/editor.html?c=x2yaz6dfRw
//https://www.makeapie.com/editor.html?c=xMpGBbTEKU
var geoCoordMap = {
  '石家庄': [114.5024, 38.3454],
  '保定': [115.2587, 39.1775],
  '唐山': [118.1753, 39.9351],
  "秦皇岛": [119.3865, 40.1425],
  '承德': [117.5391, 41.2762],
  "张家口": [114.8840, 41.1119],
  "邯郸": [114.9906, 36.6122],
  "邢台": [114.7088, 37.3682],
  "衡水": [115.6660, 38.0351],
  "沧州": [117.4574, 38.3105],
  "廊坊": [116.6138, 39.1292],
};
var data = [
  {"name":"石家庄","value":1100},
  {"name":"保定","value":524},
  {"name":"唐山","value":14},
  {"name":"秦皇岛","value":150},
  {"name":"承德","value":75},
  {"name":"张家口","value":13},
  {"name":"邯郸","value":83},
  {"name":"邢台","value":11},
  {"name":"衡水","value":19},
  {"name":"沧州","value":15},
  {"name":"廊坊","value":69}
];
var convertData = function (data) {
  var res = [];
  for (var i = 0; i < data.length; i++) {
    var geoCoord = geoCoordMap[data[i].name];
    if (geoCoord) {
      res.push({
        name: data[i].name,
        value: geoCoord.concat(data[i].value)
      });
    }
  }
  return res;
};
var max = 6000,
  min = 10;
var maxSize4Pin = 100,
  minSize4Pin = 20;

export default {
  name: "widgetAirBubbleMap",
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {
        //backgroundColor: '#0F1C3C',
        tooltip: {
          show: true,
          formatter: function (params) {
            if (params.value.length > 1) {
              return '&nbsp;&nbsp;' + params.name + '&nbsp;&nbsp;&nbsp;' + params.value[2] + '&nbsp;&nbsp;';
            } else {
              return '&nbsp;&nbsp;' + params.name + '&nbsp;&nbsp;&nbsp;' + params.value + '&nbsp;&nbsp;';
            }
          },
        },
        geo: {
          map: '河北',
          show: true,
          roam: false,
          label: {
            emphasis: {
              show: false
            }
          },
          layoutSize: "80%",
          itemStyle: {
            normal: {
              borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#00F6FF'
              }, {
                offset: 1,
                color: '#53D9FF'
              }], false),
              borderWidth: 3,
              shadowColor: 'rgba(10,76,139,1)',
              shadowOffsetY: 0,
              shadowBlur: 60
            }
          }
        },
        series: [
          {
            type: 'map',
            map: '河北',
            aspectScale: 0.75,
            label: {
              normal: {//调整数值
                position: 'right',
                show: true,
                color: '#53D9FF',
                fontSize: 20
              },
              emphasis: {
                show: true,
              },
            },
            itemStyle: {
              normal: {
                //地图块颜色
                areaColor: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#073684' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#061E3D' // 100% 处的颜色
                  }],
                },
                borderColor: '#215495',
                borderWidth: 1,
              },
              //鼠标放置颜色加深
              emphasis: {
                areaColor: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#073684' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#2B91B7' // 100% 处的颜色
                  }],
                },
              }
            },
            data: data,
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            rippleEffect: {
              brushType: 'stroke'
            },
            showEffectOn: 'render',
            itemStyle: {
              normal: {
                //气泡颜色
                color: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [{
                    offset: 0,
                    color: 'rgba(5,80,151,0.2)'
                  }, {
                    offset: 0.8,
                    color: 'rgba(5,80,151,0.8)'
                  }, {
                    offset: 1,
                    color: 'rgba(0,108,255,0.7)'
                  }],
                  global: false
                },
              }
            },
            label: {
              normal: {
                show: true,
                color: '#fff',
                fontWeight: 'bold',
                position: 'inside',
                formatter: function (para) {
                  return '{cnNum|' + para.data.value[2] + '}'
                },
                rich: {
                  cnNum: {
                    fontSize: 13,
                    color: '#D4EEFF',
                  }
                }
              },
            },
            symbol: 'circle',
            symbolSize: function (val) {
              if (val[2] == 0) {
                return 0;
              }
              ;
              return ((maxSize4Pin - minSize4Pin) / (max - min)) * val[2] + (maxSize4Pin - ((maxSize4Pin - minSize4Pin) / (max - min)) * max) * 1.2;
            },
            data: convertData(data),
            zlevel: 1,
          }]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsCollapse: {}, // 图标属性
      optionsSetup: {}
    }
  },
  computed: {
    styleObj() {
      return {
        position: this.ispreview ? "absolute" : "static",
        width: this.optionsStyle.width + "px",
        height: this.optionsStyle.height + "px",
        left: this.optionsStyle.left + "px",
        top: this.optionsStyle.top + "px",
        background: this.optionsSetup.background
      };
    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsCollapse = val.setup;
        this.optionsSetup = val.setup;
        this.editorOptions();
      },
      deep: true
    }
  },
  mounted() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.setup;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },
  methods: {
    // 修改图标options属性
    editorOptions() {
      this.setOptionsTitle();
      this.setOptionTextValue();
      //this.setOptionDataValue();
      this.setOptionsData();
      this.setOptionAirSize();
      this.setOptionMapBlocak();
    },
    // 标题设置
    setOptionsTitle() {
      const optionsCollapse = this.optionsSetup;
      const title = {};
      title.show = optionsCollapse.isNoTitle;
      title.text = optionsCollapse.titleText;
      title.left = optionsCollapse.textAlign;
      title.textStyle = {
        color: optionsCollapse.textColor,
        fontSize: optionsCollapse.textFontSize,
        fontWeight: optionsCollapse.textFontWeight
      };
      title.subtext = optionsCollapse.subText;
      title.subtextStyle = {
        color: optionsCollapse.subTextColor,
        fontWeight: optionsCollapse.subTextFontWeight,
        fontSize: optionsCollapse.subTextFontSize
      };
      this.options.title = title;
    },
    setOptionTextValue() {
      const optionsSetup = this.optionsSetup;
      const label = this.options.series[0]['label'];
      const normal = {
        position: 'right',
        show: true,
        color: optionsSetup.fontTextColor,
        fontSize: optionsSetup.fontTextSize,
        fontWeight: optionsSetup.fontTextWeight,
      };
      label['normal'] = normal;
    },
    setOptionMapBlocak() {
      const optionsSetup = this.optionsSetup;
      const itemStyle = this.options.series[0]['itemStyle'];
      const normal = {
        //地图块颜色
        areaColor: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: optionsSetup.font0PreColor // 0% 处的颜色
          }, {
            offset: 1,
            color: optionsSetup.font100PreColor // 100% 处的颜色
          }],
        },
        borderColor: '#215495',
        borderWidth: 1,
      };
      //鼠标放置颜色加深
      const emphasis = {
        areaColor: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: '#073684' // 0% 处的颜色
          }, {
            offset: 1,
            color: optionsSetup.fontHighlightColor // 100% 处的颜色
          }],
        },
      };
      itemStyle['normal'] = normal;
      itemStyle['emphasis'] = emphasis;
    },
    setOptionAirSize() {
      maxSize4Pin = this.optionsSetup.fontmaxSize4Pin
      minSize4Pin = this.optionsSetup.fontminSize4Pin
    },
    //数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(
        optionsData.dynamicData,
        optionsData.refreshTime
        );
    },
    staticDataFn(val) {
      this.options.series[0]['data'] = val;
      const optionsSetup = this.optionsSetup;
      const label = this.options.series[1]['label'];
      const normal = {
        show: true,
        color: '#fff',
        fontWeight: 'bold',
        position: 'inside',
        formatter: function (para) {
          return '{cnNum|' + para.data.value[2] + '}'
        },
        rich: {
          cnNum: {
            fontSize: optionsSetup.fontDataSize,
            color: optionsSetup.fontDataColor,
            fontWeight: optionsSetup.fontDataWeight,
          }
        }
      };
      const data = convertData(val);
      this.options.series[1]['data']=data
      label['normal'] = normal
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.renderingFn(res);
      });
    },
    renderingFn(val) {
      this.options.series[0]['data'] = val;
      const optionsSetup = this.optionsSetup;
      const label = this.options.series[1]['label'];
      const normal = {
        show: true,
        color: '#fff',
        fontWeight: 'bold',
        position: 'inside',
        formatter: function (para) {
          return '{cnNum|' + para.data.value[2] + '}'
        },
        rich: {
          cnNum: {
            fontSize: optionsSetup.fontDataSize,
            color: optionsSetup.fontDataColor,
            fontWeight: optionsSetup.fontDataWeight,
          }
        }
      };
      const data = convertData(val);
      this.options.series[1]['data']=data
      label['normal'] = normal
    }
  }
};
</script>
<style lang="scss" scoped>
.echartMap {
  width: 100%;
  height: 100%;
}

.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
