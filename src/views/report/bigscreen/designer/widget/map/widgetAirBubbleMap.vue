<template>
  <div :style="styleObj">
    <v-chart :style="{
      width: '100%',
      height: '100%'
    }" :options="options" autoresize @click="chartEvents"/>
  </div>
</template>
<script>
import {deepClone} from '@/util/util'

import echarts from "echarts";
import "../../../../../../../node_modules/echarts/map/js/china.js";
//https://www.makeapie.com/editor.html?c=x2yaz6dfRw
//https://www.makeapie.com/editor.html?c=xMpGBbTEKU
var geoCoordMap = {
  "台湾省": [121.5135, 25.0308],
  "黑龙江省": [127.9688, 45.368],
  "内蒙古自治区": [110.3467, 41.4899],
  "吉林省": [125.8154, 44.2584],
  "北京市": [116.4551, 40.2539],
  "辽宁省": [123.1238, 42.1216],
  "河北省": [114.4995, 38.1006],
  "天津市": [117.4219, 39.4189],
  "山西省": [112.3352, 37.9413],
  "陕西省": [109.1162, 34.2004],
  "甘肃省": [103.5901, 36.3043],
  "宁夏回族自治区": [106.3586, 38.1775],
  "青海省": [101.4038, 36.8207],
  "新疆维吾尔自治区": [87.9236, 43.5883],
  "西藏自治区": [91.11, 29.97],
  "四川省": [103.9526, 30.7617],
  "重庆市": [108.384366, 30.439702],
  "山东省": [117.1582, 36.8701],
  "河南省": [113.4668, 34.6234],
  "江苏省": [118.8062, 31.9208],
  "安徽省": [117.29, 32.0581],
  "湖北省": [114.3896, 30.6628],
  "浙江省": [119.5313, 29.8773],
  "福建省": [119.4543, 25.9222],
  "江西省": [116.0046, 28.6633],
  "湖南省": [113.0823, 28.2568],
  "贵州省": [106.6992, 26.7682],
  "云南省": [102.9199, 25.4663],
  "广东省": [113.12244, 23.009505],
  "广西壮族自治区": [108.479, 23.1152],
  "海南省": [110.3893, 19.8516],
  "上海市": [121.4648, 31.2891],
};
var data = [
  {
    "name": "南海诸岛",
    "value": 1
  },
  {
    "NAME": "北京市",
    "VALUE": 524
  },
  {
    "NAME": "天津市",
    "VALUE": 14
  },
  {
    "name": "上海市",
    "value": 150
  },
  {
    "name": "重庆市",
    "value": 75
  },
  {
    "name": "河北",
    "value": 13
  },
  {
    "name": "河南",
    "value": 83
  },
  {
    "name": "云南",
    "value": 11
  },
  {
    "name": "辽宁",
    "value": 19
  },
  {
    "name": "黑龙江",
    "value": 15
  },
  {
    "name": "湖南",
    "value": 69
  },
  {
    "name": "安徽",
    "value": 260
  },
  {
    "name": "山东",
    "value": 39
  },
  {
    "name": "新疆",
    "value": 4
  },
  {
    "name": "江苏",
    "value": 31
  },
  {
    "name": "浙江",
    "value": 104
  },
  {
    "name": "江西",
    "value": 36
  },
  {
    "name": "湖北",
    "value": 1052
  },
  {
    "name": "广西",
    "value": 33
  },
  {
    "name": "甘肃",
    "value": 347
  },
  {
    "name": "山西",
    "value": 8
  },
  {
    "name": "内蒙古",
    "value": 157
  },
  {
    "name": "陕西",
    "value": 22
  },
  {
    "name": "吉林",
    "value": 4
  },
  {
    "name": "福建",
    "value": 36
  },
  {
    "name": "贵州",
    "value": 39
  },
  {
    "name": "广东",
    "value": 996
  },
  {
    "name": "青海",
    "value": 27
  },
  {
    "name": "西藏",
    "value": 31
  },
  {
    "name": "四川",
    "value": 46
  },
  {
    "name": "宁夏",
    "value": 16
  },
  {
    "name": "海南",
    "value": 22
  },
  {
    "name": "台湾",
    "value": 6
  },
  {
    "name": "香港",
    "value": 2
  },
  {
    "name": "澳门",
    "value": 9
  },
];
var convertData = function (data) {
  var res = [];
  for (var i = 0; i < data.length; i++) {
    var geoCoord = geoCoordMap[data[i].NAME];
    if (geoCoord) {
      res.push({
        name: data[i].NAME,
        value: geoCoord.concat(data[i].VALUE)
      });
    }
  }
  return res;
};

var max = 6000,
  min = 10;
var maxSize4Pin = 100,
  minSize4Pin = 20;

export default {
  name: "widgetAirBubbleMap",
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {
        //backgroundColor: '#0F1C3C',
        tooltip: {
          show: true,
          formatter: function (params) {
            if (params.value.length > 1) {
              return '&nbsp;&nbsp;' + params.name + '&nbsp;&nbsp;&nbsp;' + params.value[2] + '&nbsp;&nbsp;';
            } else {
              return '&nbsp;&nbsp;' + params.name + '&nbsp;&nbsp;&nbsp;' + params.value + '&nbsp;&nbsp;';
            }
          },
        },
        geo: {
          map: 'china',
          show: true,
          roam: false,
          label: {
            emphasis: {
              show: false
            }
          },
          layoutSize: "80%",
          itemStyle: {
            normal: {
              borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#00F6FF'
              }, {
                offset: 1,
                color: '#53D9FF'
              }], false),
              borderWidth: 3,
              shadowColor: 'rgba(10,76,139,1)',
              shadowOffsetY: 0,
              shadowBlur: 60
            }
          }
        },
        series: [
          {
            type: 'map',
            map: 'china',
            aspectScale: 0.75,
            label: {
              normal: {//调整数值
                position: 'right',
                show: true,
                color: '#53D9FF',
                fontSize: 20
              },
              emphasis: {
                show: true,
              },
            },
            itemStyle: {
              normal: {
                //地图块颜色
                areaColor: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#073684' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#061E3D' // 100% 处的颜色
                  }],
                },
                borderColor: '#215495',
                borderWidth: 1,
              },
              //鼠标放置颜色加深
              emphasis: {
                areaColor: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#073684' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#2B91B7' // 100% 处的颜色
                  }],
                },
              }
            },
            data: data,
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            rippleEffect: {
              brushType: 'stroke'
            },
            showEffectOn: 'render',
            itemStyle: {
              normal: {
                //气泡颜色
                color: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [{
                    offset: 0,
                    color: 'rgba(5,80,151,0.2)'
                  }, {
                    offset: 0.8,
                    color: 'rgba(5,80,151,0.8)'
                  }, {
                    offset: 1,
                    color: 'rgba(0,108,255,0.7)'
                  }],
                  global: false
                },
              }
            },
            label: {
              normal: {
                show: true,
                color: '#fff',
                fontWeight: 'bold',
                position: 'inside',
                formatter: function (para) {
                  return '{cnNum|' + para.data.value[2] + '}'
                },
                rich: {
                  cnNum: {
                    fontSize: 13,
                    color: '#D4EEFF',
                  }
                }
              },
            },
            symbol: 'circle',
            symbolSize: function (val) {
              if (val[2] == 0) {
                return 0;
              }
              ;
              return ((maxSize4Pin - minSize4Pin) / (max - min)) * val[2] + (maxSize4Pin - ((maxSize4Pin - minSize4Pin) / (max - min)) * max) * 1.2;
            },
            data: convertData(data),
            zlevel: 6,
          }]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsCollapse: {}, // 图标属性
      optionsSetup: {}
    }
  },
  computed: {
    styleObj() {
      if(this.optionsStyle.w){
        return {
          width: "100%",
          height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
        };
      }else{
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
          background: this.optionsSetup.background
        };
      }

    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsCollapse = val.setup;
        this.optionsSetup = val.setup;
        this.editorOptions();
      },
      deep: true
    }
  },
  mounted() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.setup;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    // 修改图标options属性
    editorOptions() {
      this.setOptionsTitle();
      this.setOptionTextValue();
      //this.setOptionDataValue();
      this.setOptionsData();
      this.setOptionAirSize();
      this.setOptionMapBlocak();
      // 修改所选省份
      this.setSelectProvince();
    },
    // 设置选择省份
    setSelectProvince(){
      let spell = "china";
      const optionsSetup = this.optionsSetup;
      const aaa = this.optionsData;
      switch (optionsSetup.selectProvince){
        case "china":spell="china";break;
        case "北京":spell="beijing";break;
        case "天津":spell="tianjin";break;
        case "上海":spell="shanghai";break;
        case "重庆":spell="chongqing";break;
        case "河北":spell="hebei";break;
        case "内蒙古":spell="neimenggu";break;
        case "四川":spell="sichuan";break;
        case "河南":spell="henan";break;
        case "湖北":spell="hubei";break;
        case "湖南":spell="hunan";break;
        case "江苏":spell="jiangsu";break;
        case "江西":spell="jiangxi";break;
        case "辽宁":spell="liaoning";break;
        case "吉林":spell="jilin";break;
        case "黑龙江":spell="heilongjiang";break;
        case "陕西":spell="shanxi";break;
        case "山西":spell="shanxi1";break;
        case "山东":spell="shandong";break;
        case "青海":spell="qinghai";break;
        case "安徽":spell="anhui";break;
        case "海南":spell="hainan";break;
        case "广东":spell="guangdong";break;
        case "贵州":spell="guizhou";break;
        case "浙江":spell="zhejiang";break;
        case "福建":spell="fujian";break;
        case "甘肃":spell="gansu";break;
        case "云南":spell="yunnan";break;
        case "西藏":spell="xizang";break;
        case "新疆":spell="xinjiang";break;
        case "宁夏":spell="ningxia";break;
        case "广西":spell="guangxi";break;
        case "香港":spell="xianggang";break;
        case "澳门":spell="aomen";break;
        case "台湾":spell="台湾";break;
      }
      this.requireProvince(spell);
      this.options.series[0]['map'] = optionsSetup.selectProvince;
      this.options.geo.map = optionsSetup.selectProvince;
    },
    requireProvince(spell){
      const sheng = spell;
      if (sheng != "china"){
        require("../../../../../../../node_modules/echarts/map/js/province/" + sheng +".js")
      }
    },
    // 标题设置
    setOptionsTitle() {
      const optionsCollapse = this.optionsSetup;
      const title = {};
      title.show = optionsCollapse.isNoTitle;
      title.text = optionsCollapse.titleText;
      title.left = optionsCollapse.textAlign;
      title.textStyle = {
        color: optionsCollapse.textColor,
        fontSize: optionsCollapse.textFontSize,
        fontWeight: optionsCollapse.textFontWeight
      };
      title.subtext = optionsCollapse.subText;
      title.subtextStyle = {
        color: optionsCollapse.subTextColor,
        fontWeight: optionsCollapse.subTextFontWeight,
        fontSize: optionsCollapse.subTextFontSize
      };
      this.options.title = title;
    },
    setOptionTextValue() {
      const optionsSetup = this.optionsSetup;
      const label = this.options.series[0]['label'];
      const normal = {
        position: 'right',
        show: true,
        color: optionsSetup.fontTextColor,
        fontSize: optionsSetup.fontTextSize,
        fontWeight: optionsSetup.fontTextWeight,
      };
      label['normal'] = normal;
    },
    setOptionMapBlocak() {
      const optionsSetup = this.optionsSetup;
      const itemStyle = this.options.series[0]['itemStyle'];
      const normal = {
          //地图块颜色
          areaColor: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: optionsSetup.font0PreColor // 0% 处的颜色
            }, {
              offset: 1,
              color: optionsSetup.font100PreColor // 100% 处的颜色
            }],
          },
          borderColor: '#215495',
          borderWidth: 1,
        };
      //鼠标放置颜色加深
      const emphasis = {
        areaColor: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: '#073684' // 0% 处的颜色
          }, {
            offset: 1,
            color: optionsSetup.fontHighlightColor // 100% 处的颜色
          }],
        },
      };
      itemStyle['normal'] = normal;
      itemStyle['emphasis'] = emphasis;
    },
    setOptionAirSize() {
      maxSize4Pin = this.optionsSetup.fontmaxSize4Pin
      minSize4Pin = this.optionsSetup.fontminSize4Pin
    },
    //数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(
        optionsData.dynamicData,
        optionsData.refreshTime
        );
    },
    staticDataFn(val) {
      this.options.series[0]['data'] = val;
      const optionsSetup = this.optionsSetup;
      const label = this.options.series[1]['label'];
      const normal = {
        show: true,
        color: '#fff',
        fontWeight: 'bold',
        position: 'inside',
        formatter: function (para) {
          return '{cnNum|' + para.data.value[2] + '}'
        },
        rich: {
          cnNum: {
            fontSize: optionsSetup.fontDataSize,
            color: optionsSetup.fontDataColor,
            fontWeight: optionsSetup.fontDataWeight,
          }
        }
      };
      const data = convertData(val);
      this.options.series[1]['data']=data
      label['normal'] = normal
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.renderingFn(res);
      });
    },
    renderingFn(val) {
      this.options.series[0]['data'] = val;
      const optionsSetup = this.optionsSetup;
      const label = this.options.series[1]['label'];
      const normal = {
        show: true,
        color: '#fff',
        fontWeight: 'bold',
        position: 'inside',
        formatter: function (para) {
          return '{cnNum|' + para.data.value[2] + '}'
        },
        rich: {
          cnNum: {
            fontSize: optionsSetup.fontDataSize,
            color: optionsSetup.fontDataColor,
            fontWeight: optionsSetup.fontDataWeight,
          }
        }
      };
      const data = convertData(val);
      this.options.series[1]['data']=data
      label['normal'] = normal
    }
  }
};
</script>
<style lang="scss" scoped>
.echartMap {
  width: 100%;
  height: 100%;
}

.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
