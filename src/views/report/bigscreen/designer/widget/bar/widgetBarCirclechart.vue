<template>
  <div :style="styleObj">
    <v-chart v-if="dataShow" :options="options" autoresize  @click="chartEvents"/>
    <div v-if="nodataShow" :style="{
      position: 'relative',
       width: `100%`,
      height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
      background: this.optionsSetup.background,
      'border-radius': this.optionsSetup.borderRadius + 'px',
      textAlign: 'center',
      }"> <img :style="{
        position: 'absolute',
        left: 'calc(50% - 64px)',
        top: 'calc(50% - 64px)',
      }" src="../../../../../../../public/img/code/ksj.png"> </div>
  </div>
</template>

<script>
import {deepClone} from '@/util/util'

export default {
  name: "widgetBarCirclechart",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      dataShow: true,
      nodataShow: false,
      options: {
        angleAxis: {
          show:false,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#00567D'
            }
          },
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            lineStyle: {
              color: '#00567D'
            }
          },
        },
        polar: {
        },
        grid: {},
        legend: {
          textStyle: {
            color: "#fff"
          }
        },
        radiusAxis: {
          type: 'category',
          data: [],
          z: 100,
          axisLabel: {
            color: '#fff'
          }
        },
        series: [

        ]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsSetup: {},
      flagInter: null
    };
  },
  computed: {
    styleObj() {
      if( this.optionsStyle.w ) {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          background: this.optionsSetup.background,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
          background: this.optionsSetup.background
        };
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsCollapse = val.setup;
        this.optionsSetup = val.setup;
        this.editorOptions();
      },
      deep: true
    },
  },
  mounted() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.setup;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    // 修改图标options属性
    editorOptions() {
      this.setOptionsTitle();
      this.setAngleAxis();
      this.setOptionsTooltip();
      this.setOptionsLegend();
      this.setOptionsData();
      this.setOptionsMargin();
      this.setOptionsTop();
    },
    // 标题修改
    setOptionsTitle() {
      const optionsCollapse = this.optionsSetup;
      const title = {};
      title.text = optionsCollapse.titleText;
      title.show = optionsCollapse.isNoTitle;
      title.left = optionsCollapse.textAlign;
      title.textStyle = {
        color: optionsCollapse.textColor,
        fontSize: optionsCollapse.textFontSize,
        fontWeight: optionsCollapse.textFontWeight
      };
      title.subtext = optionsCollapse.subText;
      title.subtextStyle = {
        color: optionsCollapse.subTextColor,
        fontWeight: optionsCollapse.subTextFontWeight,
        fontSize: optionsCollapse.subTextFontSize
      };

      this.options.title = title;
    },
    setAngleAxis(){
      const optionsCollapse = this.optionsSetup;
      const angleAxis = {
        show:false,
        max: "",
        axisLine: {
          show: true,
          lineStyle: {
            color: optionsCollapse.cutlineColor
          }
        },
        axisLabel: {
          color: optionsCollapse.numberColor
        },
        splitLine: {
          lineStyle: {
            color: optionsCollapse.cutlineColor
          }
        },
      };
      angleAxis.show = optionsCollapse.isShowCircleNumber;
      angleAxis.max = optionsCollapse.barMaxNumber;
      this.options.angleAxis = angleAxis;
    },
    // 数值设定 or 柱体设置
    setOptionsTop() {
      const optionsCollapse = this.optionsSetup;
      const series = this.options.series;
      for (const key in series) {
        if (series[key].type == "bar") {
          series[key].label = {
            show: optionsCollapse.isShow,
            position: "top",
            distance: 10,
            fontSize: optionsCollapse.fontSize,
            color: optionsCollapse.subTextColor,
            fontWeight: optionsCollapse.fontWeight
          };
          series[key].barWidth = optionsCollapse.maxWidth;
        }
      }
      this.options.series = series;
    },
    // tooltip 设置
    setOptionsTooltip() {
      const optionsCollapse = this.optionsSetup;
      const tooltip = {
        trigger: "item",
        show: true,
        textStyle: {
          color: optionsCollapse.lineColor,
          fontSize: optionsCollapse.fontSize
        }
      };
      this.options.tooltip = tooltip;
    },
    // 边距设置
    setOptionsMargin() {
      const optionsCollapse = this.optionsSetup;
      const grid = {
        left: optionsCollapse.marginLeft,
        right: optionsCollapse.marginRight,
        bottom: optionsCollapse.marginBottom,
        top: optionsCollapse.marginTop,
        containLabel: true
      };
      this.options.grid = grid;
    },
    // 图例操作 legend
    setOptionsLegend() {
      const optionsCollapse = this.optionsSetup;
      const legend = this.options.legend;
      legend.show = optionsCollapse.isShowLegend;
      if(optionsCollapse.lateralPosition == "left"){
        legend.left = "left"
      }else if(optionsCollapse.lateralPosition == "right"){
        legend.left = "right"
      }else{
        legend.left = "center";
      }
      if(optionsCollapse.longitudinalPosition == "top"){
        legend.top = "top"
      }else if(optionsCollapse.longitudinalPosition == "bottom"){
        legend.top = "bottom"
      }else{
        legend.top = "middle";
      }
      legend.orient = optionsCollapse.layoutFront;
      legend.textStyle = {
        color: optionsCollapse.lengedColor,
        fontSize: optionsCollapse.fontSize
      };
      legend.itemWidth = optionsCollapse.lengedWidth;
	  legend.itemHeight = optionsCollapse.lengedHeight;
    },
    // 数据解析
    setOptionsData() {
      const optionsSetup = this.optionsSetup;
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData, optionsSetup)
        : this.dynamicDataFn(
        optionsData.dynamicData,
        optionsData.refreshTime,
        optionsSetup
        );
    },
    // 静态数据
    staticDataFn(val, optionsSetup) {
      const staticData = JSON.parse(val);
      this.dataShow = true;
      this.nodataShow = false;
      this.options.radiusAxis.data = [];
      const optionsCollapse = optionsSetup;
      this.options.radiusAxis.type = "category";
      // series
      let staticSeries = [];
      for(var j=0; j<staticData.categories.length; j++){
        let item = {
          data: [],
          name:"",
          color:"",
          type: "bar",
          coordinateSystem: 'polar',
          barGap: "0.5px",
        }
        item.data[0] = staticData.series[0].data[j];
        item.name = staticData.categories[j];
        item.color = optionsCollapse.customColor[j].color;
        staticSeries.push(item);
      }
      this.$delete(this.options,'series')
      this.$set(this.options,'series',staticSeries)
    },
    // 动态数据
    dynamicDataFn(val, refreshTime, optionsSetup) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val, optionsSetup);
        this.flagInter = setInterval(() => {
          this.getEchartData(val, optionsSetup);
        }, refreshTime);
      } else {
        this.getEchartData(val, optionsSetup);
      }
    },
    getEchartData(val, optionsSetup) {
      const data = this.queryEchartsData(val);

      data.then(res => {
        this.renderingFn(optionsSetup, res);
      });
    },
    renderingFn(optionsSetup, val) {
      console.log(`val`,val)
      let list = val.series[0].data;
      let namelist = val.xAxis;
      this.titleParam = this.value.setup.titleParam;
      //最大值
      if( this.options.title.text.includes("max") ) {
        let max = Math.max(...list)
        let name = namelist[list.indexOf(max)]
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("Maxname",name);
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("max",max);
        }
      }
      //最小值
      if( this.options.title.text.includes("min") ) {
        let min = Math.min(...list)
        let name = namelist[list.indexOf(min)]
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("Minname",name);
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("min",min);
        }
      }
      //总和
      if( this.options.title.text.includes("sum") ) {
        let sum = 0
        for(let i =0;i<list.length;i++) {
          sum += list[i]
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("sum",sum);
        }
      }
      //平均值
      if( this.options.title.text.includes("svg") ) {
        let sum = 0;
        let svg = 0;
        for(let i =0;i<list.length;i++) {
          sum += list[i]
        }
        svg = Math.round(sum / list.length)
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("svg",svg);
        }
      }
      if( val.series  && val.xAxis && typeof val.series[0].data[0] == 'number') {
        this.dataShow = true;
        this.nodataShow = false;
      }else {
        this.dataShow = false;
        this.nodataShow = true;
      }
      const flexData = val;
      // const optionsCollapse = optionsSetup
      this.options.radiusAxis.data = [];
      // this.options.radiusAxis.data = val.xAxis;
      this.options.radiusAxis.type = "category";
      let flexSeries = [];
      for(var i=0; i<flexData.xAxis.length; i++){
        let item = {
          data: [],
          name:"",
          color:"",
          type: "bar",
          coordinateSystem: 'polar',
          barGap: "0.5px",
        }
        item.data[0] = flexData.series[0].data[i];
        item.name = flexData.xAxis[i];
        // item.color = optionsCollapse.customColor[i].color;
        flexSeries.push(item);
      }
      this.$delete(this.options,'series')
      this.$set(this.options,'series',flexSeries)
    }
  }
};
</script>

<style scoped lang="scss">
.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
