<template>
  <div :style="styleObj">
    <v-chart v-if="dataShow" :options="options" :key="borderNumber" autoresize @click="chartEvents"/>
    <div v-if="nodataShow" :style="{
      position: 'relative',
       width: `100%`,
      height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
      background: this.optionsSetup.background,
      'border-radius': this.optionsSetup.borderRadius + 'px',
      textAlign: 'center',
      }"> <img :style="{
        position: 'absolute',
        left: 'calc(50% - 64px)',
        top: 'calc(50% - 64px)',
      }" src="../../../../../../../public/img/code/ksj.png"> </div>
  </div>
</template>

<script>
import {deepClone} from '@/util/util'

export default {
  name: "WidgetBarStackchart",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      dataShow: true,
      nodataShow: false,
      borderNumber:1,
      options: {
        grid: {},
        legend: {
          textStyle: {
            color: "#fff"
          }
        },
        dataZoom: [{
          show: true,
          realtime: true,
          start: 20,
          end: 80,
          height: 15,
        }],
        xAxis: {
          type: "category",
          data: [],
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff"
            }
          }
        },
        yAxis: {
          type: "value",
          data: [],
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff"
            }
          }
        },
        series: [
          {
            data: [],
            name: '',
            type: "bar",
            barGap: "0%",
            itemStyle: {
              barBorderRadius: null
            }
          }
        ]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsSetup: {},
      flagInter: null
    };
  },
  computed: {
    styleObj() {
      if ( this.optionsStyle.w  ) {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          background: this.optionsSetup.background,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
          background: this.optionsSetup.background
        };
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsCollapse = val.setup;
        this.optionsSetup = val.setup;
        this.editorOptions();
      },
      deep: true
    }
  },
  mounted() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.setup;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    // 修改图标options属性
    editorOptions() {
      this.setOptionsTitle();
      this.setOptionsX();
      this.setOptionsY();
      this.setOptionsTooltip();
      this.setOptionsMargin();
      this.setOptionsLegend();
      this.setOptionsData();
      this.setOptionsDataZoom();
    },
    // 标题修改
    setOptionsTitle() {
      const optionsSetup = this.optionsSetup;
      const title = {};
      title.text = optionsSetup.titleText;
      title.show = optionsSetup.isNoTitle;
      title.left = optionsSetup.textAlign;
      title.textStyle = {
        color: optionsSetup.textColor,
        fontSize: optionsSetup.textFontSize,
        fontWeight: optionsSetup.textFontWeight
      };
      title.subtext = optionsSetup.subText;
      title.subtextStyle = {
        color: optionsSetup.subTextColor,
        fontWeight: optionsSetup.subTextFontWeight,
        fontSize: optionsSetup.subTextFontSize
      };
      this.options.title = title;
    },
    // X轴设置
    setOptionsX() {
      const optionsSetup = this.optionsSetup;
      const xAxis = {
        type: "category",
        show: optionsSetup.hideX, // 坐标轴是否显示
        name: optionsSetup.xName, // 坐标轴名称
        nameTextStyle: {
          color: optionsSetup.xNameColor,
          fontSize: optionsSetup.xNameFontSize
        },
        nameRotate: optionsSetup.textAngleX, // 文字角度
        inverse: optionsSetup.reversalX, // 轴反转
        axisLabel: {
          show: true,
          interval: optionsSetup.textInterval, // 文字角度
          rotate: optionsSetup.textAngleX, // 文字角度
          textStyle: {
            color: optionsSetup.Xcolor, // x轴 坐标文字颜色
            fontSize: optionsSetup.fontSizeX
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: optionsSetup.lineColorX
          }
        },
        splitLine: {
          show: optionsSetup.isShowSplitLineX,
          lineStyle: {
            color: optionsSetup.splitLineColorX
          }
        }
      };
      this.options.xAxis = xAxis;
    },
    // Y轴设置
    setOptionsY() {
      const optionsSetup = this.optionsSetup;
      const yAxis = {
        type: "value",
        show: optionsSetup.isShowY, // 坐标轴是否显示
        name: optionsSetup.textNameY, // 坐标轴名称
        splitNumber: optionsSetup.ySplitNumber, //y轴数据显示个数
        nameTextStyle: {
          color: optionsSetup.NameColorY,
          fontSize: optionsSetup.NameFontSizeY
        },
        inverse: optionsSetup.reversalY, // y轴反转
        axisLabel: {
          show: true,
          rotate: optionsSetup.textAngleY,// 文字角度
          textStyle: {
            color: optionsSetup.colorY, // y轴 坐标文字颜色
            fontSize: optionsSetup.fontSizeY
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: optionsSetup.lineColorY
          }
        },
        splitLine: {
          show: optionsSetup.isShowSplitLineY,
          lineStyle: {
            color: optionsSetup.splitLineColorY
          }
        }
      };
      this.options.yAxis = yAxis;
    },
    // tooltip 提示语设置，鼠标放置显示
    setOptionsTooltip() {
      const optionsSetup = this.optionsSetup;
      const tooltip = {
        trigger: "item",
        show: true,
        textStyle: {
          color: optionsSetup.lineColor,
          fontSize: optionsSetup.tipsFontSize
        }
      };
      this.options.tooltip = tooltip;
    },
    // 边距设置
    setOptionsMargin() {
      const optionsSetup = this.optionsSetup;
      const grid = {
        left: optionsSetup.marginLeft,
        right: optionsSetup.marginRight,
        bottom: optionsSetup.marginBottom,
        top: optionsSetup.marginTop,
        containLabel: true
      };
      this.options.grid = grid;
    },
    // 图例操作 legend
    setOptionsLegend() {
      const optionsSetup = this.optionsSetup;
      const legend = this.options.legend;
      legend.show = optionsSetup.isShowLegend;
      legend.left = optionsSetup.lateralPosition;
      legend.top = optionsSetup.longitudinalPosition == "top" ? 0 : "auto";
      legend.bottom =
        optionsSetup.longitudinalPosition == "bottom" ? 0 : "auto";
      legend.orient = optionsSetup.layoutFront;
      legend.textStyle = {
        color: optionsSetup.lengedColor,
        fontSize: optionsSetup.lengedFontSize
      };
      legend.itemWidth = optionsSetup.lengedWidth;
    },
    setOptionsDataZoom(){
      const optionsCollapse = this.optionsSetup;
      if(optionsCollapse.isRoll){
        if(!optionsCollapse.verticalShow){
          let zoomArr = {}
          zoomArr.show = optionsCollapse.isRoll;
          zoomArr.start = optionsCollapse.minRoll;
          zoomArr.end = optionsCollapse.maxRoll;
          zoomArr.xAxisIndex = [0];
          zoomArr.height = "15px";
          this.$set(this.options.dataZoom,0,zoomArr)
        }else{
          let zoomArr1 = {};
          zoomArr1.show = optionsCollapse.isRoll;
          zoomArr1.start = optionsCollapse.minRoll;
          zoomArr1.end = optionsCollapse.maxRoll;
          zoomArr1.yAxisIndex = [0];
          zoomArr1.width = "15px";
          this.$set(this.options.dataZoom,0,zoomArr1)
        }
      }else{
        this.options.dataZoom[0].show = false;
        this.options.dataZoom[0].start = 0;
        this.options.dataZoom[0].end = 100;
      }
      this.borderNumber++;
    },
    // 数据解析
    setOptionsData() {
      const optionsSetup = this.optionsSetup;
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData, optionsSetup)
        : this.dynamicDataFn(
        optionsData.dynamicData,
        optionsData.refreshTime,
        optionsSetup
        );
    },
    //去重
    setUnique(arr) {
      let newArr = [];
      arr.forEach(item => {
        return newArr.includes(item) ? '' : newArr.push(item);
      });
      return newArr;
    },
    //获取堆叠样式
    getStackStyle() {
      const optionsSetup = this.optionsSetup;
      let style = ""
      if (optionsSetup.stackStyle == "upDown") {
        style = "total"
      }
      return style
    },
    //静态数据
    staticDataFn(val) {
      const optionsSetup = this.optionsSetup;
      this.dataShow = true;
      this.nodataShow = false;
      //颜色
      const customColor = optionsSetup.customColor;
      const arrColor = [];
      for (let i = 0; i < customColor.length; i++) {
        arrColor.push(customColor[i].color);
      }
      //数据
      const series = [];
      let xAxisList = []
      let yAxisList = []
      for (const i in val) {
        xAxisList[i] = val[i].axis
        yAxisList[i] = val[i].name
      }
      xAxisList = this.setUnique(xAxisList) // x轴 0725 0726 0727
      yAxisList = this.setUnique(yAxisList) // y轴 A B C
      for (const i in yAxisList) {
        const data = new Array(yAxisList.length).fill(0)
        for (const j in xAxisList) {
          for (const k in val) {
            if (val[k].name == yAxisList[i]) { // a = a
              if (val[k].axis == xAxisList[j]) { // 0725
                data[j] = val[k].data
              }
            }
          }
        }
        series.push({
          name: yAxisList[i],
          type: "bar",
          data: data,
          barGap: "0%",
          stack: this.getStackStyle(),
          barWidth: optionsSetup.maxWidth,
          label: {
            show: optionsSetup.isShow,
            position: "top",
            distance: 10,
            fontSize: optionsSetup.fontSize,
            color: optionsSetup.subTextColor,
            fontWeight: optionsSetup.fontWeight
          },
          //颜色，圆角属性
          itemStyle: {
            normal: {
              color: arrColor[i],
              barBorderRadius: optionsSetup.radius,
            }
          }
        })
      }
      this.options.series = series
      if (optionsSetup.verticalShow) {
        this.options.xAxis.data = [];
        this.options.yAxis.data = xAxisList;
        this.options.xAxis.type = "value";
        this.options.yAxis.type = "category";
      } else {
        this.options.xAxis.data = xAxisList;
        this.options.yAxis.data = [];
        this.options.xAxis.type = "category";
        this.options.yAxis.type = "value";
      }
    },
    // 动态数据
    dynamicDataFn(val, refreshTime, optionsSetup) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val, optionsSetup);
        this.flagInter = setInterval(() => {
          this.getEchartData(val, optionsSetup);
        }, refreshTime);
      } else {
        this.getEchartData(val, optionsSetup);
      }
    },
    getEchartData(val, optionsSetup) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.renderingFn(optionsSetup, res);
      });
    },
    renderingFn(optionsSetup, val) {
      console.log(`val`,val)
      let listbar = [];
      let typename = [];
      let maxlist = [];
      let maxname = [];
      let minlist = [];
      let minname = [];
      let sumlist = [];
      let svglist = [];
      let list = val
      for(let i in list.series){
        listbar[i] = list.series[i].data
        typename[i] = list.series[i].name
      }
      let xAxislist = val.xAxis
      // this.titleParam = this.value.setup.titleParam;
      //最大值
      for(let i = 0; i< listbar.length;i++){
        //最大值
        maxlist[i] = Math.max(...listbar[i]);
        maxname[i] = xAxislist[listbar[i].indexOf(maxlist[i])]
        this.options.title.text = this.options.title.text.replace(`xAxismaxname${i}`,maxname[i]);
        this.options.title.text = this.options.title.text.replace(`barmaxname${i}`,typename[i]);
        this.options.title.text = this.options.title.text.replace(`max${i}`,maxlist[i]);
        minlist[i] = Math.min(...listbar[i]);
        //最小值
        minname[i] = xAxislist[listbar[i].indexOf(minlist[i])]
        this.options.title.text = this.options.title.text.replace(`xAxisminname${i}`,minname[i]);
        this.options.title.text = this.options.title.text.replace(`barminname${i}`,typename[i]);
        this.options.title.text = this.options.title.text.replace(`min${i}`,minlist[i]);
        //总和
        sumlist[i] = 0;
        for(let j =0;j<listbar[i].length;j++) {
          sumlist[i] += listbar[i][j]
        }
        this.options.title.text = this.options.title.text.replace(`barsumname${i}`,typename[i]);
        this.options.title.text = this.options.title.text.replace(`sum${i}`,sumlist[i]);
        //平均值
        svglist[i] = Math.round(sumlist[i] / listbar[i].length)
        this.options.title.text = this.options.title.text.replace(`barsvgname${i}`,typename[i]);
        this.options.title.text = this.options.title.text.replace(`svg${i}`,svglist[i]);
      }
      if( val.series  && val.xAxis ){
        for(let i =0;i< val.series.length;i++) {
          if(typeof val.series[i].data[0] == 'number' && val.series[i].type == 'bar') {
            this.dataShow = true;
            this.nodataShow = false;
          }else {
            this.dataShow = false;
            this.nodataShow = true;
            return
          }
        }
      }
      //颜色
      const customColor = optionsSetup.customColor;
      const arrColor = [];
      for (let i = 0; i < customColor.length; i++) {
        arrColor.push(customColor[i].color);
      }
      // x轴
      if (optionsSetup.verticalShow) {
        this.options.xAxis.data = [];
        this.options.yAxis.data = val.xAxis;
        this.options.xAxis.type = "value";
        this.options.yAxis.type = "category";
      } else {
        this.options.xAxis.data = val.xAxis;
        this.options.yAxis.data = [];
        this.options.xAxis.type = "category";
        this.options.yAxis.type = "value";
      }
      const series = [];
      for (const i in val.series) {
        if (val.series[i].type == "bar") {
          series.push({
            name: val.series[i].name,
            type: "bar",
            data: val.series[i].data,
            barGap: "0%",
            stack: this.getStackStyle(),
            barWidth: optionsSetup.maxWidth,
            label: {
              show: optionsSetup.isShow,
              position: "top",
              distance: 10,
              fontSize: optionsSetup.fontSize,
              color: optionsSetup.subTextColor,
              fontWeight: optionsSetup.fontWeight
            },
            //颜色，圆角属性
            itemStyle: {
              normal: {
                color: arrColor[i],
                barBorderRadius: optionsSetup.radius,
              }
            }
          })
        }
      }
      this.options.series = series
    }
  }
};
</script>

<style scoped lang="scss">
.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

</style>
