<template>
    <div :style="styleObj">
      <v-chart :options="options" :key="percentNum" autoresize />
    </div>
</template>

<script>
import { deepClone } from "@/util/util";
export default {
  name: "WidgetPictographchart",
  props: {
    value: Object,
    ispreview: Boolean,
  },
  data() {
    return {
      percentNum: 1,
      options: {
        tooltip: {},
        legend: {
          data: [],
          selectedMode: "single",
        },
        xAxis: {
          data: ["a"],
          axisTick: { show: false },
          axisLine: { show: false },
          axisLabel: { show: false },
        },
        yAxis: {
          max: 100,
          min: 0,
          interval: 25,
          offset: -10,
          show: true,
          color: "#fff",
          splitLine: {
            show: true,
            lineStyle: {
                    color: '#ccc',
                    type: 'solid',
                }
          },
          splitNumber: 4,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#d81e06",
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
              fontSize: 33,
            }
          }
        },
        grid: {
          top: "center",
        },
        markLine: {
          z: -100,
        },
        series: [
          {
            // name: 'typeA',
            type: "pictorialBar",
            symbolClip: true,
            symbolBoundingData: 100,
            color: `#1296db`,
            // barWidth: 200,
            // barHeight: 400,
            symbol: "path://path://M10 600 Q 95 0 180 600",
            label: {
              show: true,
              position: "top",
              offset: [0, -20],
              // formatter: function (val) {
              //     return ((value.setup.Number / 100) * 100).toFixed(0) + "%";
              // },
              formatter: ``,
              fontSize: 30,
              fontFamily: "Arial",
            },
            data: [
              {
                value: ``,
                symbol:
                  "path://M359.7,410.8c-1.7-0.8-3.5-0.8-5.2-3.2c-3-5.4-6.1-13.4-7.6-17.9c-1.6-4.5-2.1-10.1-4-13.8c-1.9-3.7-4.3-7.3-5.7-10.9c-1.4-3.6-3-9.1-5-16.6c-0.8-7.6-1.2-15.6-3.2-20.3c-2-4.7-10.9-5.3-14.9-5.5c-4-0.2-8.4-6.9-8.4-6.9c-0.3-3.3,0.4-10.2,0.6-11.3c0.2-1.1,0.3-3,0.4-4.4c0.2,0,0.3,0,1,0c3-2.1,3.3-6.3,3.2-7c-0.1-0.7-0.6-0.8-2-0.7c0.1-8.2-2.4-12-4.1-14c-1.7-1.9-5.1-2.6-7.5-2.6c-2.4,0-5.8,0.7-7.5,2.6c-1.7,1.9-4.2,5.8-4.1,14c-1.4-0.1-1.8,0-2,0.7c-0.1,0.7,0.2,4.9,3.2,7c0.7,0,0.8,0,1,0c0,1.4,0.2,3.6,0.4,4.4c0.2,1.1,0.9,7.9,0.6,11.3c0,0-4.4,6.6-8.4,6.9c-4,0.2-12.9,0.8-14.9,5.5c-2,4.7-2.4,12.6-3.2,20.3c-2.1,7.5-3.6,13-5,16.6c-1.4,3.6-3.8,7.2-5.7,10.9c-1.9,3.7-2.5,9.3-4,13.8c-1.6,4.5-4.7,12.5-7.6,17.9c-1.7,2.4-3.4,2.4-5.2,3.2c-1.7,0.8-6.3,6.5-6.7,7c0,1.1,0,2,1.5,1.3c1.1-0.5,4.5-3.7,4.9-4.1c0.3,0.2,0.2,1.2-2.1,6c-2,4.2-3.6,6.9-3.5,8.4c0,0.5,0.5,0.7,1.2,0.2c1.1-0.8,5-7.3,5.4-8c0.5-1.2,0.7,0,0.5,0.4c-0.4,0.7-3.7,10.1-3.8,11.1c0,1.5,1.7,0.2,1.7,0.2c1.3-0.8,4.6-9.7,5.1-9.5c0.9,0.1,0.5,1.6,0.1,2.8c-0.5,1.5-1.5,7.9-1.5,7.9s0.2,1.1,1.5,0c0.9-1.5,2.5-7.6,3.2-9c0.5-0.4,1,0.1,0.9,1.4c0.3,1.8,0.2,5.5,0.6,6c0.5,0.5,1.1-0.7,1.2-1.7c0.2-4.2,0.6-7.4,1.1-9.9c0.4-1.4,1.1-3.5,1.2-7.3c0.1-2.5-1-2.5,1.4-6.8c1.3-2.4,13.5-23.6,15.6-27.3c2.1-3.8,0.9-4.7,2.4-8c1.5-3.4,5.6-15.4,6.6-16.7c0,0,1.3-0.2,1.8,0c-0.2,2.3,1,12.3,1.5,14c0.5,1.7,1,16,0.2,18.7c-0.8,2.7-4.2,5.5-5.8,15.2c-1.6,9.7-2.9,32-1.9,47.1c1,15.2,4.4,28.7,2.6,33.8c-1.8,5.1-4.1,11.1-3.8,19.2c0.2,8.1,0.2,34.7,0.5,39.1c-1.2,1.7-1.7,1.8-1.7,3.1c0,1.2,0.6,2.5,0.2,4.4c-0.4,2-3.5,9.1-4.1,10.8c0,2.6-0.5,1.7,0,2.6c0.5,0.9,2.7,0.9,3.1,1.2c0.4,0.4,3.1,1.1,4.1,1.1c1,0,3.7,1.1,4.8-0.9c1.1-2,1.5-9.6,1.8-16.4c0.6-1.1,0.5-1.4,0.5-3.8c0-2.5-0.7-3,0.1-6.3c0.9-3.3,1.4-9.5,3.8-15.4c2.5-5.9,4.6-13.3,4.6-16c0-2.7-0.5-12.3,0.5-17.3c0.7-3.5,2.7-5.7,3.4-8.1c0.9-3,1.6-27.2,2.5-32.8c0.9-5.5,2.7-11,3-15.5c0.2-4.6,0.3-7.8,0.9-8.4c1,0.2,1.2,0.2,1.2,0.2s0.2,0.1,1.2-0.2c0.5,0.6,0.6,3.8,0.9,8.4c0.2,4.6,2.1,10,3,15.5c0.9,5.5,1.5,29.8,2.5,32.8c0.7,2.4,2.7,4.6,3.4,8.1c1,5,0.5,14.6,0.5,17.3c0,2.7,2.1,10.1,4.6,16c2.5,5.9,3,12.1,3.8,15.4c0.9,3.3,0.1,3.8,0.1,6.3c0,2.5-0.1,2.7,0.5,3.8c0.4,6.8,0.7,14.4,1.8,16.4c1.1,2,3.8,0.9,4.8,0.9c1,0,3.7-0.7,4.1-1.1c0.4-0.4,2.6-0.4,3.1-1.2c0.5-0.9,0,0,0-2.6c-0.6-1.7-3.7-8.9-4.1-10.8c-0.4-2,0.2-3.2,0.2-4.4c0-1.2-0.5-1.4-1.7-3.1c0.2-4.3,0.2-30.9,0.5-39.1c0.2-8.1-2-14.2-3.8-19.2c-1.8-5.1,1.6-18.6,2.6-33.8c1-15.2-0.3-37.5-1.9-47.1c-1.6-9.7-5-12.4-5.8-15.2c-0.8-2.6-0.3-17,0.2-18.7c0.5-1.7,1.6-11.7,1.5-14c0.5-0.2,1.8,0,1.8,0c1,1.3,5.1,13.3,6.6,16.7c1.5,3.4,0.2,4.3,2.4,8c2.1,3.8,14.3,24.9,15.6,27.3c2.4,4.3,1.3,4.3,1.4,6.8c0.1,3.8,0.8,5.9,1.2,7.3c0.6,2.4,1,5.7,1.1,9.9c0.2,1,0.7,2.2,1.2,1.7c0.5-0.5,0.3-4.2,0.6-6c-0.1-1.3,0.4-1.8,0.9-1.4c0.7,1.4,2.3,7.5,3.2,9c1.3,1,1.5,0,1.5,0s-1-6.4-1.5-7.9c-0.4-1.2-0.8-2.7,0.1-2.8c0.5-0.2,3.9,8.7,5.1,9.5c0,0,1.7,1.2,1.7-0.2c-0.1-1-3.4-10.4-3.8-11.1c-0.2-0.4,0-1.6,0.5-0.4c0.4,0.7,4.3,7.2,5.4,8c0.7,0.5,1.2,0.3,1.2-0.2c0.2-1.5-1.5-4.2-3.5-8.4c-2.3-4.8-2.4-5.8-2.1-6c0.4,0.4,3.9,3.6,4.9,4.1c1.5,0.7,1.5-0.2,1.5-1.3C366,417.4,361.4,411.6,359.7,410.8z",
              },
            ],
            markLine: {
              symbol: "none",
              data: [],
            },
            z: 10,
          },
          {
            name: "full",
            type: "pictorialBar",
            symbolBoundingData: 100,
            animationDuration: 0,
            itemStyle: {
              color: "#000",
              borderWidth: 5,
              borderColor: '#3d59cd'
            },
            // barWidth: 200,
            // barHeight: 400,
            data: [
              {
                value: 1,
                symbol:
                  "path://M359.7,410.8c-1.7-0.8-3.5-0.8-5.2-3.2c-3-5.4-6.1-13.4-7.6-17.9c-1.6-4.5-2.1-10.1-4-13.8c-1.9-3.7-4.3-7.3-5.7-10.9c-1.4-3.6-3-9.1-5-16.6c-0.8-7.6-1.2-15.6-3.2-20.3c-2-4.7-10.9-5.3-14.9-5.5c-4-0.2-8.4-6.9-8.4-6.9c-0.3-3.3,0.4-10.2,0.6-11.3c0.2-1.1,0.3-3,0.4-4.4c0.2,0,0.3,0,1,0c3-2.1,3.3-6.3,3.2-7c-0.1-0.7-0.6-0.8-2-0.7c0.1-8.2-2.4-12-4.1-14c-1.7-1.9-5.1-2.6-7.5-2.6c-2.4,0-5.8,0.7-7.5,2.6c-1.7,1.9-4.2,5.8-4.1,14c-1.4-0.1-1.8,0-2,0.7c-0.1,0.7,0.2,4.9,3.2,7c0.7,0,0.8,0,1,0c0,1.4,0.2,3.6,0.4,4.4c0.2,1.1,0.9,7.9,0.6,11.3c0,0-4.4,6.6-8.4,6.9c-4,0.2-12.9,0.8-14.9,5.5c-2,4.7-2.4,12.6-3.2,20.3c-2.1,7.5-3.6,13-5,16.6c-1.4,3.6-3.8,7.2-5.7,10.9c-1.9,3.7-2.5,9.3-4,13.8c-1.6,4.5-4.7,12.5-7.6,17.9c-1.7,2.4-3.4,2.4-5.2,3.2c-1.7,0.8-6.3,6.5-6.7,7c0,1.1,0,2,1.5,1.3c1.1-0.5,4.5-3.7,4.9-4.1c0.3,0.2,0.2,1.2-2.1,6c-2,4.2-3.6,6.9-3.5,8.4c0,0.5,0.5,0.7,1.2,0.2c1.1-0.8,5-7.3,5.4-8c0.5-1.2,0.7,0,0.5,0.4c-0.4,0.7-3.7,10.1-3.8,11.1c0,1.5,1.7,0.2,1.7,0.2c1.3-0.8,4.6-9.7,5.1-9.5c0.9,0.1,0.5,1.6,0.1,2.8c-0.5,1.5-1.5,7.9-1.5,7.9s0.2,1.1,1.5,0c0.9-1.5,2.5-7.6,3.2-9c0.5-0.4,1,0.1,0.9,1.4c0.3,1.8,0.2,5.5,0.6,6c0.5,0.5,1.1-0.7,1.2-1.7c0.2-4.2,0.6-7.4,1.1-9.9c0.4-1.4,1.1-3.5,1.2-7.3c0.1-2.5-1-2.5,1.4-6.8c1.3-2.4,13.5-23.6,15.6-27.3c2.1-3.8,0.9-4.7,2.4-8c1.5-3.4,5.6-15.4,6.6-16.7c0,0,1.3-0.2,1.8,0c-0.2,2.3,1,12.3,1.5,14c0.5,1.7,1,16,0.2,18.7c-0.8,2.7-4.2,5.5-5.8,15.2c-1.6,9.7-2.9,32-1.9,47.1c1,15.2,4.4,28.7,2.6,33.8c-1.8,5.1-4.1,11.1-3.8,19.2c0.2,8.1,0.2,34.7,0.5,39.1c-1.2,1.7-1.7,1.8-1.7,3.1c0,1.2,0.6,2.5,0.2,4.4c-0.4,2-3.5,9.1-4.1,10.8c0,2.6-0.5,1.7,0,2.6c0.5,0.9,2.7,0.9,3.1,1.2c0.4,0.4,3.1,1.1,4.1,1.1c1,0,3.7,1.1,4.8-0.9c1.1-2,1.5-9.6,1.8-16.4c0.6-1.1,0.5-1.4,0.5-3.8c0-2.5-0.7-3,0.1-6.3c0.9-3.3,1.4-9.5,3.8-15.4c2.5-5.9,4.6-13.3,4.6-16c0-2.7-0.5-12.3,0.5-17.3c0.7-3.5,2.7-5.7,3.4-8.1c0.9-3,1.6-27.2,2.5-32.8c0.9-5.5,2.7-11,3-15.5c0.2-4.6,0.3-7.8,0.9-8.4c1,0.2,1.2,0.2,1.2,0.2s0.2,0.1,1.2-0.2c0.5,0.6,0.6,3.8,0.9,8.4c0.2,4.6,2.1,10,3,15.5c0.9,5.5,1.5,29.8,2.5,32.8c0.7,2.4,2.7,4.6,3.4,8.1c1,5,0.5,14.6,0.5,17.3c0,2.7,2.1,10.1,4.6,16c2.5,5.9,3,12.1,3.8,15.4c0.9,3.3,0.1,3.8,0.1,6.3c0,2.5-0.1,2.7,0.5,3.8c0.4,6.8,0.7,14.4,1.8,16.4c1.1,2,3.8,0.9,4.8,0.9c1,0,3.7-0.7,4.1-1.1c0.4-0.4,2.6-0.4,3.1-1.2c0.5-0.9,0,0,0-2.6c-0.6-1.7-3.7-8.9-4.1-10.8c-0.4-2,0.2-3.2,0.2-4.4c0-1.2-0.5-1.4-1.7-3.1c0.2-4.3,0.2-30.9,0.5-39.1c0.2-8.1-2-14.2-3.8-19.2c-1.8-5.1,1.6-18.6,2.6-33.8c1-15.2-0.3-37.5-1.9-47.1c-1.6-9.7-5-12.4-5.8-15.2c-0.8-2.6-0.3-17,0.2-18.7c0.5-1.7,1.6-11.7,1.5-14c0.5-0.2,1.8,0,1.8,0c1,1.3,5.1,13.3,6.6,16.7c1.5,3.4,0.2,4.3,2.4,8c2.1,3.8,14.3,24.9,15.6,27.3c2.4,4.3,1.3,4.3,1.4,6.8c0.1,3.8,0.8,5.9,1.2,7.3c0.6,2.4,1,5.7,1.1,9.9c0.2,1,0.7,2.2,1.2,1.7c0.5-0.5,0.3-4.2,0.6-6c-0.1-1.3,0.4-1.8,0.9-1.4c0.7,1.4,2.3,7.5,3.2,9c1.3,1,1.5,0,1.5,0s-1-6.4-1.5-7.9c-0.4-1.2-0.8-2.7,0.1-2.8c0.5-0.2,3.9,8.7,5.1,9.5c0,0,1.7,1.2,1.7-0.2c-0.1-1-3.4-10.4-3.8-11.1c-0.2-0.4,0-1.6,0.5-0.4c0.4,0.7,4.3,7.2,5.4,8c0.7,0.5,1.2,0.3,1.2-0.2c0.2-1.5-1.5-4.2-3.5-8.4c-2.3-4.8-2.4-5.8-2.1-6c0.4,0.4,3.9,3.6,4.9,4.1c1.5,0.7,1.5-0.2,1.5-1.3C366,417.4,361.4,411.6,359.7,410.8z",
              },
            ],
          },
        ],
      },
      optionsStyle: {},
      optionsSetup: {},
      optionsCollapse: {},
      optionsData: {},
    };
  },
  computed: {
    styleObj() {
      if( this.optionsStyle.w ) {
        console.log(`optionsStyle`,this.optionsStyle)
        return {
          position: this.ispreview ? "absolute" : "static",
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
        };
      }
      
    },
  },
  watch: {
    value: {
      handler(val) {
        console.log(`val`,val)
        this.optionsStyle = val.position;
        this.optionsSetup = val.setup;
        this.optionsCollapse = val.collapse;
        this.optionsData = val.data;
        this.editorOptions();
        this.percentNum++;
      },
      deep: true,
    },
  },
  created() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.collapse;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
    this.percentNum++;
  },
  methods: {
    editorOptions() {
      this.setOptionsSerise();
      this.setOptionsyAxis();
      this.setOptionsData();
    },
    setOptionsSerise() {
      this.options.series[0].data[0].value = this.optionsSetup.Number;
      this.options.series[0].label.formatter = this.optionsSetup.Number + "%";
      this.options.series[0].label.fontSize = this.optionsSetup.numSize;
      this.options.series[0].color = this.optionsSetup.picColor;
      this.options.series[1].itemStyle.color = this.optionsSetup.bacColor;
      this.options.series[0].data[0].symbol = `path://` + this.optionsSetup.pathD;
      this.options.series[1].data[0].symbol = `path://` + this.optionsSetup.pathD;
      this.options.series[1].itemStyle.borderWidth = this.optionsSetup.borderWidth;
      this.options.series[1].itemStyle.borderColor = this.optionsSetup.borderColor;
      this.options.series[0].barWidth = this.optionsSetup.barWidth;
      this.options.series[1].barWidth = this.optionsSetup.barWidth;

    },
    setOptionsyAxis() {
      this.options.yAxis.show = this.optionsSetup.isShow;
      this.options.yAxis.offset = this.optionsSetup.offSet;
      this.options.yAxis.axisLine.lineStyle.color = this.optionsSetup.lineColor;
      this.options.yAxis.axisLabel.textStyle.color = this.optionsSetup.textColor;
      this.options.yAxis.axisLabel.textStyle.fontSize = this.optionsSetup.fontSize;
      this.options.yAxis.splitLine.show = this.optionsSetup.isShows;
      this.options.yAxis.splitLine.lineStyle.color = this.optionsSetup.splitColor;
      this.options.yAxis.splitLine.lineStyle.type = this.optionsSetup.splitAlign;
    },
    // 数据类型 静态 or 动态
    setOptionsData() {
      const optionsData = this.optionsData;
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    },
    staticDataFn(val) {
      const staticData = JSON.parse(val);
      this.options.series[0].data[0].value = staticData[0].value
      this.options.series[0].label.formatter = staticData[0].value + "%";
      this.options.xAxis.data[0] = staticData[0].name
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then((res) => {
        this.renderingFn(res);
      });
    },
    renderingFn(val) {
      const dynamicData = JSON.parse(val);
      this.options.series[0].data[0].value = dynamicData[0].value
      this.options.series[0].label.formatter = dynamicData[0].value + "%";
    },
  },
};
</script>

<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>