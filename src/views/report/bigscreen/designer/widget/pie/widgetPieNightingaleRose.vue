<template>
  <div :style="styleObj">
    <v-chart v-if="dataShow" :options="options"
             autoresize @click="chartEvents"/>
    <div v-if="nodataShow" :style="{
      position: 'relative',
       width: `100%`,
      height:  `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
      background: this.optionsSetup.background,
      'border-radius': this.optionsSetup.borderRadius + 'px',
      textAlign: 'center',

    }"> <img :style="{
        position: 'absolute',
        left: 'calc(50% - 64px)',
        top: 'calc(50% - 64px)',
      }" src="../../../../../../../public/img/code/ksj.png"> </div>
  </div>
</template>

<script>
import {deepClone} from '@/util/util'

export default {
  name: "WidgetPieNightingaleRoseArea", //南丁格尔玫瑰图面积模式 参考：https://echarts.apache.org/examples/zh/editor.html?c=pie-roseType-simple
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data () {
    return {
      dataShow: true,
      nodataShow: false,
      options: {
        legend: {
          top: "bottom"
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: true }
          }
        },
        series: [
          {
            //name: "面积模式",
            type: "pie",
            radius: ["10%", "50%"],
            center: ["50%", "50%"],
            roseType: "area",
            itemStyle: {
              borderRadius: 8
            },
            data: []
          }
        ]
      },
      optionsStyle: {}, // 样式
      optionsData: {}, // 数据
      optionsCollapse: {}, // 图标属性
      optionsSetup: {}
    };
  },
  computed: {
    styleObj () {
      if( this.optionsStyle.w ) {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          background: this.optionsSetup.background,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
          background: this.optionsSetup.background
        };
      }
    }
  },
  watch: {
    value: {
      handler (val) {
        this.optionsStyle = val.position;
        this.optionsData = val.data;
        this.optionsCollapse = val.setup;
        this.optionsSetup = val.setup;
        this.editorOptions();
      },
      deep: true
    }
  },
  mounted () {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.setup;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    // 修改图标options属性
    editorOptions () {
      this.setOptionsTitle();
      this.setOptionsValue();
      this.setOptionsTooltip();
      this.setOptionsLegend();
      this.setOptionsColor();
      this.setOptionsData();
      this.setOptionsRosetype();
    },
    // 饼图模式 面积模式"area" 半径模式"radius"
    setOptionsRosetype () {
      this.options.series[0]['roseType'] = this.optionsSetup.nightingleRosetype;
      this.options.series[0]['radius'] = [this.optionsSetup.insideSize + "%", this.optionsSetup.outsideSize + "%"]
    },
    // 标题修改
    setOptionsTitle () {
      const optionsCollapse = this.optionsSetup;
      const title = {};
      title.text = optionsCollapse.titleText;
      title.show = optionsCollapse.isNoTitle;
      title.left = optionsCollapse.textAlign;
      title.textStyle = {
        color: optionsCollapse.textColor,
        fontSize: optionsCollapse.textFontSize,
        fontWeight: optionsCollapse.textFontWeight
      };
      title.subtext = optionsCollapse.subText;
      title.subtextStyle = {
        color: optionsCollapse.subTextColor,
        fontWeight: optionsCollapse.subTextFontWeight,
        fontSize: optionsCollapse.subTextFontSize
      };
      this.options.title = title;
    },
    // 数值设定
    setOptionsValue() {
      const optionsCollapse = this.optionsSetup;
      const series = this.options.series;
      const textValue = optionsCollapse.textValue ? "{b}：" : "" ;
      const numberValue = optionsCollapse.numberValue ? "{c}" : "";
      const percentage = optionsCollapse.percentage ? "({d})%" : "";
      const label = {
        show: optionsCollapse.isShow,
        formatter: `{a|${textValue}${numberValue} \n ${percentage}}`,
        rich: {
          a: {
            padding: [-30, 15, -20, 15],
            color: optionsCollapse.subTextColor,
            fontSize: optionsCollapse.fontSize,
            fontWeight: optionsCollapse.fontWeight
          }
        },
        fontSize: optionsCollapse.fontSize,

        fontWeight: optionsCollapse.optionsCollapse
      };
      const labelLine= {
        show: optionsCollapse.isShowLine,//数据标签引导线
        length: optionsCollapse.lineLength,
        lineStyle: {
          width: 1,
          type: 'solid'
        }
      }
      for (const key in series) {
        if (series[key].type == "pie") {
          series[key].label = label;
          series[key].labelLine = labelLine;
        }
      }
    },
    // tooltip 设置
    setOptionsTooltip () {
      const optionsCollapse = this.optionsSetup;
      const tooltip = {
        trigger: "item",
        show: true,
        textStyle: {
          color: optionsCollapse.lineColor,
          fontSize: optionsCollapse.fontSize
        }
      };
      this.options.tooltip = tooltip;
    },
    // 边距设置
    setOptionsMargin () {
      const optionsCollapse = this.optionsSetup;
      const grid = {
        left: optionsCollapse.marginLeft,
        right: optionsCollapse.marginRight,
        bottom: optionsCollapse.marginBottom,
        top: optionsCollapse.marginTop,
        containLabel: true
      };
      this.options.grid = grid;
    },
    // 图例操作 legend
    setOptionsLegend () {
      const optionsCollapse = this.optionsSetup;
      const legend = this.options.legend;
      legend.show = optionsCollapse.isShowLegend;
      if(optionsCollapse.lateralPosition == "left"){
        legend.left = "left"
      }else if(optionsCollapse.lateralPosition == "right"){
        legend.left = "right"
      }else{
        legend.left = "center";
      }
      if(optionsCollapse.longitudinalPosition == "top"){
        legend.top = "top"
      }else if(optionsCollapse.longitudinalPosition == "bottom"){
        legend.top = "bottom"
      }else{
        legend.top = "middle";
      }
      legend.orient = optionsCollapse.layoutFront;
      legend.textStyle = {
        color: optionsCollapse.lengedColor,
        fontSize: optionsCollapse.lengedFontSize
      };
      legend.itemWidth = optionsCollapse.lengedWidth;
	  legend.itemHeight = optionsCollapse.lengedHeight;
    },
    // 图例颜色修改
    setOptionsColor() {
      const optionsCollapse = this.optionsSetup;
      const customColor = optionsCollapse.customColor;
      if (!customColor) return;
      const arrColor = [];
      for (let i = 0; i < customColor.length; i++) {
        arrColor.push(customColor[i].color);
      }
      this.options.color = arrColor;
      this.options = Object.assign({}, this.options);
    },
    // 数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData)
        : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    },
    staticDataFn(val) {
      const staticData = JSON.parse(val);
      this.dataShow = true;
      this.nodataShow = false;
      for (const key in this.options.series) {
        if (this.options.series[key].type == "pie") {
          this.options.series[key].data = staticData;
        }
      }
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        this.renderingFn(res);
      });
    },
    renderingFn(val) {
      let list = [];
      let namelist = [];
      for ( let i =0;i<val.length;i++) {
        list.push(val[i].value)
        namelist.push(val[i].name)
      }
      console.log(this.value)
      this.titleParam = this.value.setup.titleParam;
      //最大值
      if( this.options.title.text.includes("max") ) {
        this.max = Math.max(...list)
        let name = namelist[list.indexOf(this.max)]
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("Maxname",name);
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("max",this.max);
        }
      }
      //最小值
      if( this.options.title.text.includes("min") ) {
        this.min = Math.min(...list)
        let name = namelist[list.indexOf(this.min)]
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("Minname",name);
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("min",this.min);
        }
      }
      //总和
      if( this.options.title.text.includes("sum") ) {
        for(let i =0;i<list.length;i++) {
          this.sum += list[i]
        }
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("sum",this.sum);
        }
      }
      //平均值
      if( this.options.title.text.includes("svg") ) {
        for(let i =0;i<list.length;i++) {
          this.sum += list[i]
        }
        this.svg = Math.round(this.sum / list.length)
        for(let i=0; i<this.options.title.text.split("").length; i++){
          this.options.title.text = this.options.title.text.replace("svg",this.svg);
        }
      }
      if(val[0].name  && val[0].value && typeof val[0].value == 'number') {
        this.dataShow = true;
        this.nodataShow = false;
      }else {
        this.dataShow = false;
        this.nodataShow = true;
      }
      for (const key in this.options.series) {
        if (this.options.series[key].type == "pie") {
          this.options.series[key].data = val;
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.echarts {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
