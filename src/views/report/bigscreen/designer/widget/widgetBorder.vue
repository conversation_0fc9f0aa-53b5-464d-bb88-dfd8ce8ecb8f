
<template>
  <div class="text" :style="styleColor">
    <dv-border-box-1 :reverse="reserve" v-if="borderShow == 'dv-border-box-1'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-1>
    <dv-border-box-2 :reverse="reserve" v-if="borderShow == 'dv-border-box-2'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-2>
    <dv-border-box-3 :reverse="reserve" v-if="borderShow == 'dv-border-box-3'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-3>
    <dv-border-box-4 :reverse="reserve" v-if="borderShow == 'dv-border-box-4'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-4>
    <dv-border-box-5 :reverse="reserve" v-if="borderShow == 'dv-border-box-5'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-5>
    <dv-border-box-6 :reverse="reserve" v-if="borderShow == 'dv-border-box-6'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-6>
    <dv-border-box-7 :reverse="reserve" v-if="borderShow == 'dv-border-box-7'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-7>
    <dv-border-box-8 :reverse="reserve" v-if="borderShow == 'dv-border-box-8'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-8>
    <dv-border-box-9 :reverse="reserve" v-if="borderShow == 'dv-border-box-9'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-9>
    <dv-border-box-10 :reverse="reserve" v-if="borderShow == 'dv-border-box-10'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-10>
    <dv-border-box-11 :reverse="reserve" v-if="borderShow == 'dv-border-box-11'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-11>
    <dv-border-box-12 :reverse="reserve" v-if="borderShow == 'dv-border-box-12'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-12>
    <dv-border-box-13 :reverse="reserve" v-if="borderShow == 'dv-border-box-13'" :key="borderNumber" :color="[mainColor, lessColor]" :backgroundColor=borderBackground></dv-border-box-13>
  </div>
</template>

<script>
export default {
  name: "widgetBorder",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {},
      optionsData: {},
      borderShow:'dv-border-box-1',
      reserve:false,
      borderNumber:1,
      borderBackground:'',
      mainColor:'',
      lessColor:'',
    };
  },
  computed: {
    transStyle() {
      return this.objToOne(this.options);
    },
    styleColor() {
      return {
        position: this.ispreview ? "absolute" : "static",
        background: this.transStyle.background,
        "background-image": this.transStyle.backgroundImage,
        width: this.transStyle.width + "px",
        height: this.transStyle.height + "px",
        left: this.transStyle.left + "px",
        top: this.transStyle.top + "px",
        right: this.transStyle.right + "px",
      };
    }
  },
  watch: {
    value: {
      handler(val) {
        this.options = val;
        this.borderShow = val.setup.borderStyle;
        this.reserve = val.setup.isReverse;
        this.borderNumber++;
        this.borderBackground = val.setup.borderBackground;
        this.mainColor = val.setup.mainColor;
        this.lessColor = val.setup.lessColor;

        // this.optionsData = val.data;
        // this.setOptionsData();
      },
      deep: true
    }
  },
  mounted() {
    this.options = this.value;
    this.borderShow = this.value.setup.borderStyle;
    this.reserve = this.value.setup.isReverse;
    this.optionsData = this.value.data;
    this.borderBackground = this.value.setup.borderBackground;
    this.mainColor = this.value.setup.mainColor;
    this.lessColor = this.value.setup.lessColor;
    // this.setOptionsData();
  },
  methods: {
    // 数据解析
    // setOptionsData() {
    //   const optionsData = this.optionsData; // 数据类型 静态 or 动态
    //   if (optionsData.dataType == "dynamicData") {
    //     this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
    //   } else {};
    // },
    // dynamicDataFn(val, refreshTime) {
    //   if (!val) return;
    //   if (this.ispreview) {
    //     this.getEchartData(val);
    //     this.flagInter = setInterval(() => {
    //       this.getEchartData(val);
    //     }, refreshTime);
    //   } else {
    //     this.getEchartData(val);
    //   }
    // },
    // getEchartData(val) {
    //   const data = this.queryEchartsData(val);
    //   data.then(res => {
    //     this.styleColor.text = res[0].value;
    //     this.$forceUpdate();
    //   });
    // }
  }
};
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
