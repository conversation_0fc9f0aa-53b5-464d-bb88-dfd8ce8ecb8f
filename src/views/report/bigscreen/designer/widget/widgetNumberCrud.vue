
<template>
  <div class="text" :style="styleColor">
    <div class="flexs" :style="flexColor">
      <div v-for="(item,index) in datalist" :key="index" :style="{
        width: optionsSetup.crudwidth + '%',
        height: optionsSetup.crudheight + '%',
        'margin-bottom': optionsSetup.crudbottom + 'px',
        'background-color': optionsSetup.crudbackground,
        'border-radius': optionsSetup.crudborder + 'px',
        'text-align': optionsSetup.crudtextAlign
      }" >
        <p :style="{
          color: optionsSetup.allcustomColor[index] ? optionsSetup.allcustomColor[index].color : '#1ED2F7',
          'font-size': optionsSetup.topsize + 'px',
          'font-weight': optionsSetup.topfontWeight,
          'line-height': optionsSetup.toplinehight + 'px',
          'margin-top': optionsSetup.toptop + 'px',
          }">{{item.value}}</p>
        <p :style="{
          color: optionsSetup.allcustomColor2[index] ? optionsSetup.allcustomColor2[index].color : 'rgba(138,144,156,0.63)',
          'font-size': optionsSetup.bottomsize + 'px',
          'font-weight': optionsSetup.bottomfontWeight,
          'line-height': optionsSetup.bottomlinehight + 'px',
          'margin-top': optionsSetup.bottomtop + 'px',
        }">{{item.label}}</p>
      </div>
    </div>
  </div>
</template>

<script>
import {deepClone} from "@/util/util";

export default {
  name: "WidgetNumberCrud",
  components: {},
  props: {
    value: Object,
    ispreview: Boolean
  },
  data() {
    return {
      options: {},
      optionsData: {},
      optionsSetup: {},
      datalist: [
      ]
    };
  },
  computed: {
    transStyle() {
      return this.objToOne(this.options);
    },
    styleColor() {
      if(this.transStyle.w){
        return{
          position: this.ispreview ? "absolute" : "static",
          // color: this.options.setup.color,
          background: this.transStyle.background,
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          // border: this.transStyle.textBorder + "px solid #fff",
          // "border-color": this.transStyle.textBorderColor,
          "border-radius": this.transStyle.borderRadius + 'px',
          padding:this.transStyle.padding + "px",
          // cursor: this.transStyle.pointer
        }
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          // color: this.transStyle.color,
          "font-weight": this.transStyle.fontWeight,
          text: this.transStyle.text,
          "font-family":this.transStyle.fontFamily,
          "font-size": this.transStyle.fontSize + "px",
          "letter-spacing": this.transStyle.letterSpacing + "em",
          background: this.transStyle.background,
          "text-align": this.transStyle.textAlign,
          width: this.transStyle.width + "px",
          height: this.transStyle.height + "px",
          left: this.transStyle.left + "px",
          top: this.transStyle.top + "px",
          right: this.transStyle.right + "px",
          border: this.transStyle.textBorder + "px solid #fff",
          "border-color": this.transStyle.textBorderColor,
          "border-radius": this.transStyle.textBorderRadius + "px",
          padding:this.transStyle.padding + "px",
          // cursor: this.transStyle.pointer
        };
      }
    },
    flexColor(){
      return{
        position: this.ispreview ? "absolute" : "static",
        background: this.transStyle.background,
        width: `100%`,
        height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
        left: this.transStyle.left + "px",
        top: this.transStyle.top + "px",
        right: this.transStyle.right + "px",
        padding:this.transStyle.padding + "px",
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        this.options = val;
        this.optionsSetup = val.setup;
        console.log(`this.optionsSetup`,this.optionsSetup)
        this.optionsData = val.data;
        this.setOptionsData();

      },
      deep: true
    }
  },
  mounted() {
    this.options = this.value;
    this.optionsSetup = this.value.setup;
    this.optionsData = this.value.data;
    this.setOptionsData();
  },
  methods: {
    chartEvents(params){
      if(this.$store.state.isMobile){
        return
      }
      const contextData = this.optionsData.dynamicData.contextData ? this.optionsData.dynamicData.contextData : {};
      let dynamicData = deepClone(this.optionsData.dynamicData);
      if (dynamicData && dynamicData.drillSetList.length > 0) {
        let drillSet = dynamicData.drillSetList[0];
        if (drillSet.drillProperties) {
          for (const key in drillSet.drillProperties) {
            if (params[drillSet.drillProperties[key]]) {
              contextData[key] = params[drillSet.drillProperties[key]];
            }
          }
          dynamicData.contextData = contextData;
          // dynamicData.setCode = dynamicData.drillSetCode;
          this.$EventBus.$emit('child-event', dynamicData);
        }
      }
    },
    // 数据解析
    setOptionsData() {
      const optionsData = this.optionsData; // 数据类型 静态 or 动态
      if (optionsData.dataType == "dynamicData") {
        this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime);
      } else {
        this.staticDataFn(optionsData.staticData)
      }
    },
    staticDataFn(val) {
      console.log(`staticDataFn`,val)
      this.datalist = val
    },
    dynamicDataFn(val, refreshTime) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val);
        this.flagInter = setInterval(() => {
          this.getEchartData(val);
        }, refreshTime);
      } else {
        this.getEchartData(val);
      }
    },
    getEchartData(val) {
      const data = this.queryEchartsData(val);
      data.then(res => {
        console.log(`res`,res)
        this.datalist = res;
        this.$forceUpdate();
      });
    }
  }
};
</script>

<style scoped lang="scss">
.text {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.text_p:hover{
  cursor: pointer ;
}
.flexs{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
