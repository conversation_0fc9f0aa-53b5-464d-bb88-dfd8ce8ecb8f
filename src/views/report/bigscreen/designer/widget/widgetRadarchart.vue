<template>
    <div :style="styleObj">
      <v-chart :options="options" autoresize />
    </div>
</template>

<script>
import { deepClone } from "@/util/util";

export default {
  name: "WidgetRadarchart",
  props: {
    value: Object,
    ispreview: Boolean,
  },
  data() {
    return {
      tit: [],
      options: {
        tooltip: {},
        //   线条颜色
        color: ['#d81e06', '#1296db', '#56A3F1', '#FF917C'],
        textStyle: {
          fontSize: 30,
          color: `#d81e06`,
        },
        title: {
          // text: "雷达图",
        },
        legend: {
          data: [],

        },
        radar: {
			center: ['50%', '50%'],
			// 圆中心坐标，数组的第一项是横坐标，第二项是纵坐标。[ default: ['50%', '50%'] ]
			radius: '',
			// 圆的半径，数组的第一项是内半径，第二项是外半径。
            indicator: [
              {}
            ],
            // 背景圈数
            splitNumber: 4,
            // 外圈形状
            // shape: 'circle',
			name: {
				textStyle: {
					color: '#fff',
					backgroundColor: 'rgba(0, 0, 0, .3)',
					borderRadius: 3,
					padding: [3, 5]
				}
			},
            splitArea: {
                areaStyle: {
                    // 背景圆环颜色
                    color: ['#77EADF', '#26C3BE', '#64AFE9', '#428BD4'],
                    shadowColor: 'rgba(0, 0, 0, 0.2)',
                    shadowBlur: 0
                }
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(211, 253, 250, 0.8)'
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(211, 253, 250, 0.8)'
                }
            }
        },
        series: [
          {
            name: "Budget vs spending",
            type: "radar",
            data: [
              {},
              {},
              {},
              {},
              // {
              //   // value: [4200, 3000, 20000, 35000, 50000, 18000],
              //   // name: "标题一",
              // },
              // {
              //   // value: [5000, 14000, 28000, 26000, 42000, 21000],
              //  // name: "标题二", 
              // },
            ],
          },
        ],
      },
      optionsStyle: {},
      optionsSetup: {},
      optionsCollapse: {},
      optionsData: {},
    };
  },
  computed: {
    styleObj() {
      if( this.optionsStyle.w ) {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: `100%`,
          height: `calc(100% - ${this.optionsSetup.titlelineHeight}px)`,
          'border-radius': this.optionsSetup.borderRadius + 'px',
        };
      } else {
        return {
          position: this.ispreview ? "absolute" : "static",
          width: this.optionsStyle.width + "px",
          height: this.optionsStyle.height + "px",
          left: this.optionsStyle.left + "px",
          top: this.optionsStyle.top + "px",
        };
      }
      
    },
  },
  watch: {
    value: {
      handler(val) {
        this.optionsStyle = val.position;
        this.optionsSetup = val.setup;
        this.optionsCollapse = val.collapse;
        this.optionsData = val.data;
        this.editorOptions();
      },
      deep: true,
    },
  },
  mounted() {
    this.optionsStyle = this.value.position;
    this.optionsData = this.value.data;
    this.optionsCollapse = this.value.collapse;
    this.optionsSetup = this.value.setup;
    this.editorOptions();
  },

  methods: {
    editorOptions() {
        this.setOptionsColor();
        this.setOptionsValue();
        this.setOptionsData();
        this.setOptionsLegend()
    },
    setOptionsColor() {
        let customColor = this.optionsSetup.customColor;
        let colorArr = [];
        for (let i = 0; i < customColor.length; i++) {
            colorArr.push(
              customColor[i].color
            )
        }
        this.options.color = colorArr;
        let backgroundColor = this.optionsSetup.backgroundColor;
        let colorArr2 = [];
        for (let i = 0; i < backgroundColor.length; i++) {
            colorArr2.push(
              backgroundColor[i].color
            )
        }
        this.options.radar.splitArea.areaStyle.color = colorArr2;
        this.options.textStyle.color = this.optionsSetup.color;
		this.options.radar.name.textStyle.backgroundColor = this.optionsSetup.fontBackgroundColor;
        this.options.radar.axisLine.lineStyle.color = this.optionsSetup.lineColor;
        this.options.radar.splitLine.lineStyle.color = this.optionsSetup.lineColor;
    },
    setOptionsValue() {
        this.options.radar.splitNumber = this.optionsSetup.Number;
        this.options.textStyle.fontSize = this.optionsSetup.fontSize;
		this.options.radar.radius = this.optionsSetup.radiusValue;
        // let dynamicAddTable = this.optionsSetup.dynamicAddTable;
        // let indicator = [];
        // for( let i = 0; i <dynamicAddTable.length; i++ ){
        //   indicator.push({
        //     name: dynamicAddTable[i].name,
        //     max: dynamicAddTable[i].key,
        //   })
        // }
        // this.options.radar.indicator = indicator;
    },
    setOptionsLegend() {
      this.options.legend.show = this.optionsSetup.isShowLegend;
      this.options.legend.left = this.optionsSetup.lateralPosition;
      this.options.legend.top = this.optionsSetup.longitudinalPosition;
      this.options.legend.orient = this.optionsSetup.layoutFront;
      this.options.legend.textStyle = {
        color: this.optionsSetup.lengedColor,
        fontSize: this.optionsSetup.lengedFontSize
      };
      this.options.legend.itemWidth = this.optionsSetup.lengedWidth;
    },
    // 数据类型 静态 or 动态
    setOptionsData() {
      const optionsData = this.optionsData;
      const optionsSetup = this.optionsSetup;
      optionsData.dataType == "staticData"
        ? this.staticDataFn(optionsData.staticData, optionsSetup)
        : this.dynamicDataFn(optionsData.dynamicData, optionsData.refreshTime, optionsSetup);
    },
    staticDataFn(val,optionsSetup) {
      const staticData = JSON.parse(val);
      for( var i =0; i<staticData.length; i++){
        if(staticData[i].name === "最大") {
          delete staticData[i].name
          let name = Object.keys(staticData[i]);
          let max = Object.values(staticData[i]);
          let indicator = []
          for( var j=0; j<name.length; j++){
            indicator.push({
              name: name[j],
              max: max[j],
            })
          }
          this.options.radar.indicator = indicator

          staticData.splice(i,1)   
        }
        this.tit.push(staticData[i].name)
        delete staticData[i].name
      } 
      for( var a = 0; a<staticData.length; a++ ){
        this.options.series[0].data[a].value = Object.values(staticData[a]);
        this.options.series[0].data[a].name = this.tit[a]
      }
      this.options.legend.data = this.tit;
    },
    dynamicDataFn(val, refreshTime, optionsSetup) {
      if (!val) return;
      if (this.ispreview) {
        this.getEchartData(val, optionsSetup);
        this.flagInter = setInterval(() => {
          this.getEchartData(val, optionsSetup);
        }, refreshTime);
      } else {
        this.getEchartData(val, optionsSetup);
      }
    },
    getEchartData(val, optionsSetup) {
      const data = this.queryEchartsData(val);
      data.then((res) => {
        this.renderingFn(optionsSetup, res);
      });
    },
    renderingFn(optionsSetup, val) {
      const dynamicData = JSON.parse(val);
      for( var i =0; i<dynamicData.length; i++){
        if(dynamicData[i].name === "最大") {
          delete dynamicData[i].name
          let name = Object.keys(dynamicData[i]);
          let max = Object.values(dynamicData[i]);
          let indicator = []
          for( var j=0; j<name.length; j++){
            indicator.push({
              name: name[j],
              max: max[j],
            })
          }
          this.options.radar.indicator = indicator

          staticData.splice(i,1)   
        }
        this.tit.push(dynamicData[i].name)
        delete dynamicData[i].name
      } 
      for( var a = 0; a<dynamicData.length; a++ ){
        this.options.series[0].data[a].value = Object.values(dynamicData[a]);
        this.options.series[0].data[a].name = this.tit[a]
      }
      this.options.legend.data = this.tit;
    },
  },
};
</script>

<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  // overflow: hidden;
}
</style>