
<template>
  <div>
    <grid-layout
      class="gridlayout"
      :style="{
        'background-color': dashboard.components.backgroundColor,
        'background-image': 'url(' + dashboard.components.backgroundImage + ')',
        'background-position': '0% 0%',
        'background-size': '100% 100%',
        'background-repeat': 'initial',
        'background-attachment': 'initial',
        'background-origin': 'initial',
        'background-clip': 'initial',
        'min-height': `calc(100vh - 80px)`,
      }"
      :layout.sync="widgets"
      :preventCollision="dashboard.components.preventCollision"
      :autoSize="true"
      :col-num="dashboard.components.colNum"
      :row-height="dashboard.components.rowHeight"
      :is-draggable="true"
      :is-resizable="true"
      :is-mirrored="false"
      :vertical-compact="dashboard.components.verticalCompact"
      :margin="[dashboard.components.marginLeft, dashboard.components.marginTop]"
      :use-css-transforms="true"
      @click.self.native="setOptionsOnClickScreen"
    >
      <grid-item
        :style="{
          'border-radius': item.value.setup.borderRadius !== 0 ? item.value.setup.borderRadius + `px` : dashboard.components.borderRadius + `px`,
          'background-color': item.value.setup.backgroundColor2 ? item.value.setup.backgroundColor2 : dashboard.components.backgroundColor2,
          'border': `${ item.value.setup.borderColor  ? item.value.setup.borderColor : dashboard.components.borderColor} ${ item.value.setup.borderWeight  ? item.value.setup.borderWeight : dashboard.components.borderWeight}px ${ item.value.setup.borderType  ? item.value.setup.borderType : dashboard.components.borderType}`,
          '-webkit-box-shadow': `${dashboard.components.setX}px ${dashboard.components.setY}px ${dashboard.components.shadowBlur}px ${dashboard.components.shadowSpread}px ${dashboard.components.shadowColor}`,
          overflow: 'hidden'
        }"

        :ref="item.value.setup.cancelLeft ? 'cancelLeft' : '' "
        v-for="(item, index) in widgets"
        :key="item.i"
        class="griditem"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        @moved="movedEvent"
        @resized="resizedEvent"
        @click.native="layindex(index)"
        @contextmenu.prevent.native="rightClick($event, index)"
        @mouseover.prevent.native="setGrade(index)"
        @mouseout.prevent.native="outGrade(index)"
      >
        <div
          v-if="item.value.setup.titleShow && item.type !== 'widget-title' ? item.value.setup.titleShow : false"
          :style="{
            'text-align': item.value.setup.titleAlign,
            'line-height': dashboard.components.titlelineHeight + 'px',
            'background-color': item.value.setup.backgroundtitle,
            'border-bottom': `${item.value.setup.Bottomsize}px solid ${item.value.setup.titleBottom}`,
            paddingLeft: '15px',
            paddingRight: '15px',
          }"
        >
          <span
            v-if="item.value.setup.titleShow"
            :style="{
              fontSize: dashboard.components.titleSize+ 'px',
              color: dashboard.components.titleColor,
              'font-family': dashboard.components.titleFamily,
              'font-weight': dashboard.components.titleFontWeight,
            }"
          >{{ item.value.setup.titleinner }}</span>
          <span
            v-if="item.value.setup.subtitleShow"
            :style="{
              fontSize: item.value.setup.subtitleSize + 'px',
              color: item.value.setup.subtitleColor,
              marginLeft: item.value.setup.subtitleLeft +'px',
            }"
          >{{ item.value.setup.subtitleinner }}</span>
          <div style="display: inline-block;" v-if="item.value.data.dataType == 'dynamicData' && item.value.data.dynamicData && item.value.data.dynamicData.seldynamicData">
            <el-select v-for="(item1,index) in item.value.data.dynamicData.seldynamicData" :key="index" :style="selectColor" :placeholder="item1.placeholder">
              <el-option
                  v-for="(items,indexx) in item1.setParamList"
                  :key="indexx"
                  :label="items.label"
                  :value="items.value">
                <!--                <span>{{ items.label }}11111111</span>-->
              </el-option>
            </el-select>
          </div>


          <a
            v-if="item.value.setup.totitleShow"
            :style="{
              fontSize: item.value.setup.totitleSize + 'px',
              color: item.value.setup.totitleColor,
              position: `absolute`,
              right: '15px',
            }"
            :href="item.value.setup.linkAdress"
            target="_blank"
          >{{ item.value.setup.totitleinner }}</a>
        </div>
        <div v-if="movegrade == index ? true : false" class="bg-grid"></div>
        <div v-if="grade == index ? true : false" class="bg-grid"></div>
        <component :is="item.type" :value="item.value" :style="{ padding: item.value.setup.padding +'px' ? item.value.setup.padding +'px': dashboard.components.padding + `px`}"/>
        <span :style="{
          position: 'absolute',
          bottom: '5px',
          right: '5px',
          'z-index': '999'
          }">{{item.i}}</span>
      </grid-item>
      <content-menu
        :visible.sync="visibleContentMenu"
        :style-obj="styleObj"
        @deletelayer="deletelayer"
        @copylayer="copylayer"
      />
    </grid-layout>
  </div>
</template>

<script>
import { widgetTools, getToolByCode } from "../laytools";
import draggable from "vuedraggable";
import contentMenu from "../form/layoutcontentMenu";
import VueGridLayout from "vue-grid-layout";
import widgetProgress from "./widgetProgress.vue";
import widgetHref from "./widgetHref.vue";
import widgetText from "./widgetText.vue";
import widgetBlock from "./widgetBlock.vue";
import WidgetMarquee from "./widgetMarquee.vue";
import widgetTime from "./widgetTime.vue";
import widgetImage from "./widgetImage.vue";
import widgetSlider from "./widgetSlider.vue";
import widgetVideo from "./widgetVideo.vue";
import WidgetIframe from "./widgetIframe.vue";
import widgetBarchart from "./widgetBarchart.vue";
import widgetGradientColorBarchart from "./bar/widgetGradientColorBarchart.vue";
import widgetLinechart from "./widgetLinechart.vue";
import widgetBarlinechart from "./widgetBarlinechart";
import WidgetPiechart from "./widgetPiechart.vue";
import WidgetFunnel from "./widgetFunnel.vue";
import WidgetGauge from "./widgetGauge.vue";
import WidgetPieNightingaleRoseArea from "./pie/widgetPieNightingaleRose";
import widgetTable from "./widgetTable.vue";
import widgetMap from "./widgetMap.vue";
import widgetPiePercentageChart from "./pie/widgetPiePercentageChart";
import widgetAirBubbleMap from "./map/widgetAirBubbleMap";
import widgetBarCirclechart from "./bar/widgetBarCirclechart";
// import widgetBarManychart from "./bar/widgetBarManychart";
import widgetHebeiMap from "./map/widgetHebeiMap";
import widgetBarStackChart from "./bar/widgetBarStackChart";
import widgetLineStackChart from "./line/widgetLineStackChart";
import widgetBarCompareChart from "./bar/widgetBarCompareChart";
import widgetBorder from "./widgetBorder.vue";
import widgetDecorate from "./widgetDecorate.vue";
import widgetTurncard from "./widgetTurncard.vue";
import widgetPercent from "./widgetPercent.vue";
// import widgetWater from "./widgetWater.vue";
import WidgetPictographchart from "./widgetPictographchart.vue";
import WidgetRadarchart from "./widgetRadarchart";
import WidgetDetails from "./widgetDetails.vue";
import widgetTitle from "./widgetTitle.vue";
import widgetPersonal from "./widgetPersonal.vue";
import widgetEpidemic from "./widgetEpidemic.vue";
import widgetNumberCrud from "@/views/report/bigscreen/designer/widget/widgetNumberCrud";

export default {
  name: "Widget",
  components: {
    draggable,
    contentMenu,
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem,
    widgetProgress,
    widgetHref,
    widgetText,
    widgetBlock,
    WidgetMarquee,
    widgetTime,
    widgetImage,
    widgetSlider,
    widgetVideo,
    WidgetIframe,
    widgetBarchart,
    widgetGradientColorBarchart,
    widgetLinechart,
    widgetBarlinechart,
    WidgetPiechart,
    WidgetFunnel,
    WidgetGauge,
    WidgetPieNightingaleRoseArea,
    widgetTable,
    widgetMap,
    widgetPiePercentageChart,
    widgetAirBubbleMap,
    widgetBarCirclechart,
    // widgetBarManychart,
    widgetHebeiMap,
    widgetBarStackChart,
    widgetLineStackChart,
    widgetBarCompareChart,
    widgetBorder,
    widgetDecorate,
    widgetTurncard,
    widgetPercent,
    // widgetWater,
    WidgetPictographchart,
    WidgetRadarchart,
    WidgetDetails,
    widgetTitle,
    widgetPersonal,
    widgetEpidemic,
    widgetNumberCrud
  },
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    /*
    widget-text widget-marquee widget-href widget-time widget-image widget-slider widget-video widget-table widget-iframe widget-universal
    widget-linechart widget-barlinechart widget-piechart widget-hollow-piechart widget-funnel widget-gauge widget-china-map
    */
    index: Number, // 当前组件，在工作区变量widgetInWorkbench中的索引
    type: String,
    bigscreen: Object,
    value: {
      type: [Object],
      default: () => {},
    },
    step: Number,
    widgets: Array,
    dashboard: Object,
    dashboardoptions: Object,
  },
  data() {
    return {
      data: {
        setup: {},
        data: {},
        position: {},
      },
      grade: -1,
      movegrade: -1,
      styleObj: {
        left: 0,
        top: 0,
      },
      visibleContentMenu: false,
      rightClickIndex: -1,
      widgetOptions: {
        setup: [], // 配置
        data: [], // 数据
        position: [], // 坐标
      },
      widgetIndex: 0,
      screenCode: ``,
      activeName: `first`,
      linkaeList: [],
      selectList: [],
      key: [],
    };
  },
  watch: {
    widgets:{
      handler(val) {
        console.log(`widgets`,val)
        for(let i =0; i<val.length; i++) {
          this.widgets[i].value.setup.customColor = this.dashboard.components.allcustomColor;
          // if( val[i].value.setup.cancelLeft == true ) {
          //   this.$nextTick(() => {
          //     console.log(`cancelLeft`,this.$refs.cancelLeft)
          //     this.$refs.cancelLeft.style
          //   });
          // }
        }
        // this.selectData();
      }
    }
  },
  computed: {
    selectColor() {
      return {
        'margin-left':  '10px'
      }
    }
  },
  updated() {
    // this.selectData();
  },
  created() {
    for( let j = 0; j <this.widgets.length; j++ ) {
      this.widgets[j].value.setup.customColor = this.dashboard.components.allcustomColor;
    }
  },
  methods: {
    setOptionsOnClickScreen() {
      this.grade = -1;
      this.screenCode = "screen";
      this.activeName = "first";
      this.widgetOptions = this.dashboardoptions;
      let options = this.widgetOptions;
      this.$emit("options", options);
    },
    resizedEvent(i, newH, newW, newHPx, newWPx) {
      let index = {};
      index.index = i;
      index.w = newW;
      index.h = newH;
      index.x = this.widgets[i].x;
      index.y = this.widgets[i].y;
      index.i = i;
      this.layindex(index);
    },
    movedEvent(i, newX, newY) {
      let index = {};
      index.index = i;
      index.x = this.widgets[i].x;
      index.y = this.widgets[i].y;
      index.w = this.widgets[i].w;
      index.h = this.widgets[i].h;
      index.i = i;
      this.layindex(index);
    },
    widgetsClick(index) {
      this.layindex(index);
    },
    //鼠标移入添加蒙板
    setGrade(index) {
      this.movegrade = index;
    },
    outGrade(index) {
      this.movegrade = -1;
    },
    rightClick(event, index) {
      this.rightClickIndex = index;
      const left = event.clientX;
      const top = event.clientY;
      if (left || top) {
        this.styleObj = {
          left: left + "px",
          top: top + "px",
          display: "block",
        };
      }
      this.visibleContentMenu = true;
      return false;
    },
    layindex(index) {
      this.grade = index;
      if (typeof index == "number") {
        this.widgetOptions = ``;
        this.widgetOptions = this.deepClone(this.widgets[index]["options"]);
        let positions = {};
        positions.i = index;
        positions.x= this.widgets[index].x;
        positions.y= this.widgets[index].y;
        positions.w= this.widgets[index].w;
        positions.h= this.widgets[index].h;
        for( let i =0; i< this.widgetOptions.position.length; i++ ){
          const key = this.widgetOptions.position[i].name;
          this.widgetOptions.position[i].value = positions[key]
        }
        // this.widgets[index].value.position.x = this.widgets[index].x;
        // this.widgets[index].value.position.y = this.widgets[index].y;
        // this.widgets[index].value.position.w = this.widgets[index].w;
        // this.widgets[index].value.position.h = this.widgets[index].h;
        const linkaeList = [...this.widgets];
        linkaeList.splice(index,1);
        let data = {};
        data.index = positions;
        data.options = this.widgetOptions;
        data.linkaeList = linkaeList
        data.valuelist = this.widgets[index].value.setup.linkagelist;
        data.type = this.widgets[index].type
        this.$emit("index", data);
        return;
      }
      if (index.index < 0 || index.index >= this.widgets.length) {
        return;
      }
      this.widgetIndex = index.index;
      this.widgets[index.index].value.position = index;
      this.widgets[index.index].options.position.forEach((el) => {
        for (const key in index) {
          if (el.name == key) {
            el.value = index[key];
          }
        }
      });
      this.widgetOptions = this.deepClone(this.widgets[index.index]["options"]);
      const linkaeList = [...this.widgets];
      linkaeList.splice(index.index,1);
      let data = {};
      data.index = index;
      data.options = this.widgetOptions;
      data.linkaeList = linkaeList
      data.valuelist = this.widgets[index.index].value.setup.linkagelist;
      data.type = this.widgets[index].type
      this.$emit("index", data);
    },
    // 删除
    deletelayer() {
      this.widgets.splice(this.rightClickIndex, 1);
      for(let i = 0; i< this.widgets.length; i++){
        this.widgets[i].i = `${i}`
      }
    },
    // 复制
    copylayer() {
      const obj = this.deepClone(this.widgets[this.rightClickIndex]);
      obj.i = `${this.widgets.length}`;
      obj.value.position.i = `${this.widgets.length}`;
      this.widgets.splice(this.widgets.length, 0, obj);
    },
  },
};
</script>

<style scoped lang="scss">
.vue-draggalbe {
  position: absolute;
}
.widget-active {
  cursor: move;
  border: 1px dashed #09f;
  background-color: rgba(115, 170, 229, 0.5);
}
.avue-draggable {
  padding: 0 !important;
}
.griditem {
  // border: cyan 1px solid;
  // box-shadow:  0px 0px 3px 3px #333;
  position: relative;
  z-index: 99;
}
.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100%);
  height: calc(100%);
  background-color: rgba(115, 170, 229, 0.35);
  cursor: move;
  z-index: 2;
}
::v-deep .vue-resizable-handle {
  z-index: 99;
}
.gridlayouts {
  width: 100%;
  height: 100%;
}
</style>
