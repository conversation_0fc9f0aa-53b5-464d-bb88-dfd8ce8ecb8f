<template>
    <div :style="styleColor">
        <img v-if="optionsSetup.imgShow" :src="imgColor.imageAdress" :style="imgColor" alt="">
        <div :style="textColor">
            <h3 v-if="optionsSetup.firsttitleShow" :style="hColor" >{{ optionsSetup.firstTitle }}</h3>
            <p v-if="optionsSetup.secondtitleShow" :style="pColor" >{{ optionsSetup.secondTitle }}</p>
        </div>
        <div v-if="clientWidth > 1000 && selectShow" :style="selectsColor">
            <span :style="selectspanColor">{{optionsSetup.selectTitle}}</span>
            <div v-for="(item,index) in selectList" :key="index" :style="selectColor">
                <el-select v-if="item.type == 'select' || item.type == '' || !item.type" v-model="value1[index]" @change="selectData(value1[index],item,'select')" clearable  :placeholder="item.placeholder">
                    <el-option
                        v-for="items in item.setParamList"
                        :key="items"
                        :label="items.label"
                        :value="items.value">
                    </el-option>
                </el-select>
                <el-date-picker
                    v-if="item.type == 'data'"
                    v-model="value1[index]"
                    type="date"
                    clearable
                    format="yyyy 年 MM 月 dd 日"
                    value-format="yyyy-MM-dd"
                    @change="selectData(value1[index],item,'data')"
                    :placeholder="item.placeholder">
                </el-date-picker>
                <el-date-picker
                    v-if="item.type == 'datarange'"
                    v-model="value1[index]"
                    type="daterange"
                    size="mini"
                    value-format="yyyy-MM-dd"
                    @change="selectData(value1[index],item,'data')"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :placeholder="item.placeholder">
                </el-date-picker>
            </div>
            <el-button type="primary" @click="setData" :style="buttonColor" >{{optionsSetup.btnTitle}}</el-button>
        </div>
    </div>
</template>

<script>
import { Position } from 'monaco-editor';
export default {
    name: 'widgetSelect',
    props: {
        value: Object,
        ispreview: Boolean
    },
    data() {
        return {
            selectList: [
            ],
            selectShow: false,
            List: [],
            value1: [],
            param: {},
            key: [],
            contextDataParam:{},
            optionsData: {},
            options: {},
            clientWidth: document.documentElement.clientWidth,
            optionsSetup: ``,
            imageAdress: 'http://www.sanyth.com/images/index/logo1.png',
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    picker.$emit('pick', [start, end]);
                    }
                }]
            },
            value2: '',
        }
    },
    computed: {
        styleColor(){
            return {
                height: `100%`,
                widthd: `100%`,
                position: 'relative',
              'border-radius': this.optionsSetup.borderRadius + 'px',
              // display: 'flex',
            }
        },
        selectsColor(){
            return {
                float: 'right',
                'margin-top': this.optionsSetup.selectTop + 'px',

                // height: `100%`,
            }
        },
        selectColor(){
            return {
                float: 'left',
                // 'margin-top': this.optionsSetup.selectTop + 'px',
                'margin-right': '30px',
            }
        },
        selectspanColor(){
            return{
                'margin-right': this.optionsSetup.selectHeight  + 'px',
                'font-size': this.optionsSetup.selectWeight + 'px',
                color: this.optionsSetup.selectColor,
            }
        },
        imgColor(){
            return {
                imageAdress: this.optionsSetup.imageAdress,
                float: 'left',
                'margin-top': this.optionsSetup.imgTop + 'px',
                'margin-left': this.optionsSetup.imgLeft + 'px',
                width: this.optionsSetup.imgWidth + 'px',
                height: this.optionsSetup.imgHeight + 'px',
            }
        },
        textColor(){
            return {
                float: 'left',
                // height: `100%`,
            }
        },
        hColor(){
            return {
                'line-height': this.optionsSetup.firstHeight  + 'px',
                'font-size': this.optionsSetup.firstWeight + 'px',
                'font-family': this.optionsSetup.titleFamily,
                color: this.optionsSetup.firstColor,
                'font-weight': this.optionsSetup.titleFontWeight,
                'margin-top': this.optionsSetup.firstmarginTop + 'px',
                'margin-left': this.optionsSetup.firstmarginLeft + 'px',
              'margin-bottom': '0px',

            }
        },
        pColor(){
            return {
                'line-height': this.optionsSetup.secondHeight  + 'px',
                'font-size': this.optionsSetup.secondWeight + 'px',
                'font-family': this.optionsSetup.titleFamily2,
                color: this.optionsSetup.secondColor,
                'font-weight': this.optionsSetup.titleFontWeight2,
                'margin-top': this.optionsSetup.secondmarginTop + 'px',
                'margin-left': this.optionsSetup.secondmarginLeft + 'px',
            }
        },
        buttonColor(){
            return {
                height: `30px`,
                'background-color': this.optionsSetup.buttonColor,
            }
        }
    },
    watch: {
        value: {
            handler(val) {
                this.options = val;
                this.optionsSetup = val.setup;
                this.optionsData = val.data;
                this.editorOptions();
            },
            deep: true
        }
    },
    mounted() {
      console.log(`clientWidth`,this.clientWidth)
        this.options = this.value;
        this.optionsSetup = this.value.setup;
        this.optionsData = this.value.data;
        this.editorOptions();
    },
    methods: {
        selectData(value1,item,type) {
            console.log(`value1`,value1)
            console.log(`item`,item)
            if(type == 'select'){
              this.$forceUpdate()
              this.contextDataParam[item.chartProperties.key] = value1;
            }
            if(type == 'data'){
              this.$forceUpdate()

              this.contextDataParam[item.chartProperties.key] = value1;
            }
            console.log(`contextDataParam`,this.contextDataParam)

        },
        pickerData(value2) {
        },
        setData() {
            let param = {};
            param.data = this.contextDataParam;
            param.list = this.optionsSetup.linkagelist;
            console.log(`setData`,param)
            this.$emit( "changeTitle", param )
        },
        editorOptions() {

            this.setOptionsData();
        },
        setOptionsData() {
            const optionsData = this.optionsData; // 数据类型 静态 or 动态
            optionsData.dataType == "staticData"
                ? this.staticDataFn(optionsData.staticData)
                : this.dynamicDataFn(optionsData.selectdynamicData, optionsData.refreshTime);
        },
        staticDataFn(val) {
            const staticData = JSON.parse(val);
        },
        dynamicDataFn(val, refreshTime) {
            if (!val) return;
            if (this.ispreview) {
                this.getEchartData(val);
                this.flagInter = setInterval(() => {
                this.getEchartData(val);
                }, refreshTime);
            } else {
                this.getEchartData(val);
            }
        },
        getEchartData(val) {
          console.log(`getEchartData`,val)
          this.selectList = val.seldynamicData
          console.log(`this.selectList`,this.selectList)
          this.selectShow = true
          this.$forceUpdate()
            // for( let h = 0; h < val.length; h++ ){
            //     this.queryEchartsData(val[h]).then(res => {
            //         let name = Object.keys(res[0])[0];
            //         this.key.push(name);
            //         let list = [];
            //         for( let j =0; j<res.length; j++ ) {
            //             list.push({
            //                 label: res[j][name],
            //                 value: res[j][name]
            //             })
            //         }
            //         this.selectList.push(list);
            //     })
            // }
            // const data = this.queryEchartsData(val);
            // data.then(res => {
            //     this.renderingFn(res);
            // });
            // val.drillSetList[0].setCode = val.drillSetList[0].drillSetCode
            // const data2 = this.queryEchartsData(val.drillSetList[0]);
            // data2.then(res => {
            // })
            // for( let h =0; h<val.drillSetList.length; h++ ){
            //     val.drillSetList[h].setCode = val.drillSetList[h].drillSetCode
            //     this.queryEchartsData(val.drillSetList[h]).then(res => {
            //         let name = Object.keys(res[0])[0];
            //         this.key.push(name);
            //         let list = [];
            //         for( let j =0; j<res.length; j++ ) {
            //             list.push({
            //                 label: res[j][name],
            //                 value: res[j][name]
            //             })
            //         }
            //         this.selectList.push(list);
            //     })
            // }
        },
    },
};
</script>

<style lang="scss" scoped>

</style>
