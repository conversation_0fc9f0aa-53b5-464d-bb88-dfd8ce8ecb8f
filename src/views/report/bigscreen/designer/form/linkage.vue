<template>
    <div>
        <el-select v-model="value" @change="selectData(value1)" multiple placeholder="请选择">
                <el-option
                v-for="item in options"
                :key="item.i"
                :label="item.i +`:`+ item.value.setup.titleinner"
                :value="item.i">
                </el-option>
            </el-select>
    </div>
</template>

<script>
export default {
    name: 'GitSanythReportUiLinkage',
    props: {
        options: Array,
        index: String,
        valuelist: Array
    },
    data() {
        return {
            value:[]
        };
    },
    watch: {
        valuelist:{
            handler(val){
                this.value = val;
                console.log(val)
            }
        }
    },
    mounted() {
        this.value = this.valuelist;
    },
    updated() {
    },
    methods: {
        selectData() {
            let data = {};
            data.list = this.value;
            data.index = this.index;
            this.$emit( "onChanged", data )
        }
    },
};
</script>

<style lang="scss" scoped>

</style>