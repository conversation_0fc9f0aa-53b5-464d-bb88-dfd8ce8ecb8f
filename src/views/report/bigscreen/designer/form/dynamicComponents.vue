<template>
  <div>
    <el-form ref="form" :model="form" label-width="100px" label-position="left">
      <el-form-item label="数据集">
        <el-select
            size="mini"
            v-model="dataSetValue"
            clearable
            placeholder="请选择"
            @change="selectDataSet"
            filterable
        >
          <el-option
              v-for="item in tbDataSet"
              :key="item.id"
              :label="item.setName"
              :value="item.id"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item
          v-for="(item, index) in userNameList"
          :key="index"
          :label="item.paramName"
      >
        <el-input v-model.trim="item.sampleItem" size="mini"/>
      </el-form-item>-->
      <el-form-item v-for="item in setParamList" :key="item" :label="item">
        <Dictionary
            v-model="params"
            :updata-dict="updataDict[item]"
            :dict-key="'CHART_PROPERTIES'"
            :drill="{drill:false}"
            @input="selectParams($event, item)"
        />
      </el-form-item>
<!--      <el-form-item label="图表下钻数据集">
        <el-select
            size="mini"
            v-model="drillDataSetValue"
            clearable
            placeholder="请选择"
            @change="selectDrillDataSet"
        >
          <el-option
              v-for="item in xzDataSet"
              :key="item.id"
              :label="item.setName"
              :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-for="item in drillParamList" :key="item" :label="item">
        <Dictionary
            v-model="mappingParam"
            :dict-key="'DRILL_PROPERTIES'"
            @input="selectDrillParams($event, item)"
        />
      </el-form-item>-->
<!--    <el-form v-if="dynamicData.seldrillShow" ref="form" :model="form" label-width="100px" label-position="left">-->
      <el-form-item v-if="dynamicData.seldrillShow || dynamicData.seldynamicData" label="筛选数据集个数">
        <el-input-number v-model="seldrillLevel" controls-position="right" @change="selhandleChange" :min="0" :max="10" size="mini" class="cengshu"></el-input-number>
      </el-form-item>
      <div v-if="dynamicData.seldrillShow || dynamicData.seldynamicData" v-for="(item,index) in seldrillSetList" :key="index">
        <el-button @click="selcheckShow(item)" class="el-button-h"> <span class="el-collapse-item__tit">数据集{{index+1}}</span>  <i v-if="!item.show" class="el-collapse-item__arrow el-icon-arrow-right"></i> <i v-if="item.show" class="el-collapse-item__arrow el-icon-arrow-down"></i> </el-button>
          <el-collapse-transition>
            <div v-show="item.show">
              <el-form-item class="transition-box" label="筛选器">
                <el-select v-model="item.type" placeholder="请选择">
                  <el-option
                      v-for="item in typeoptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
                <el-form-item class="transition-box" label="数据集">
                    <el-select
                        size="mini"
                        v-model="item.dataSetValue"
                        clearable
                        placeholder="请选择"
                        @change="selselectDataSet(item)"
                        filterable
                    >
                    <el-option
                        v-for="item in sxDataSet"
                        :key="item.id"
                        :label="item.setName"
                        :value="item.id"
                    />
                    </el-select>
                </el-form-item>
              <el-form-item class="transition-box" label="Key">
                <el-input v-model="item.chartProperties.key" placeholder="请输入内容"></el-input>
              </el-form-item>
              <el-form-item class="transition-box" label="提示语">
                <el-input v-model="item.placeholder" placeholder="请输入内容"></el-input>
              </el-form-item>
<!--                <el-form-item v-for="items in item.setParamList" :key="items" :label="items">-->
<!--                    <Dictionary-->
<!--                        v-model="selparams"-->
<!--                        :updata-dict="selupdataDict[items]"-->
<!--                        :dict-key="'CHART_PROPERTIES'"-->
<!--                        :drill="{drill:false}"-->
<!--                        @input="selselectParams($event, items, index)"-->
<!--                    />-->
<!--                </el-form-item>-->
            </div>
         </el-collapse-transition>
      </div>
<!--    </el-form>-->
      <el-form-item label="钻取层数">
        <el-input-number v-model="drillLevel" controls-position="right" @change="handleChange" :min="0" :max="10" size="mini" class="cengshu"></el-input-number>
      </el-form-item>
      <div v-for="(item,index) in drillSetList" :key="index">
        <el-button @click="checkShow(item)" class="el-button-h"> <span class="el-collapse-item__tit">下钻数据{{index+1}}</span>  <i v-if="!item.show" class="el-collapse-item__arrow el-icon-arrow-right"></i> <i v-if="item.show" class="el-collapse-item__arrow el-icon-arrow-down"></i> </el-button>
          <el-collapse-transition>
            <div v-show="item.show">
              <el-form-item class="transition-box" label="下钻标题">
                <el-input placeholder="请输入标题" v-model="item.drillSetName" clearable></el-input>
              </el-form-item>
              <!-- <el-form-item label="对齐列数">
                <el-input-number placeholder="请输入" v-model="item.drillSetNum" :min="0" clearable controls-position="right" size="mini" class="cengshu"></el-input-number>
              </el-form-item>
              <el-form-item label="对齐方式">
                <el-select
                    size="mini"
                    v-model="item.drillSetTyp"
                    clearable
                    placeholder="请选择"
                    @change="selectDataSet"
                >
                  <el-option
                      value="居中"
                  />
                  <el-option
                      value="右对齐"
                  />
                </el-select>
              </el-form-item> -->
              <el-form-item class="transition-box" label="下钻数据集">
                <el-select
                    size="mini"
                    v-model="item.drillSetCode"
                    clearable
                    placeholder="请选择"
                    @change="selectDrillDataSet(item,index)"
                    filterable
                >
                  <el-option
                      v-for="zxData in xzDataSet"
                      :key="zxData.id"
                      :label="zxData.setName"
                      :value="zxData.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item class="transition-box" v-for="drillParam in item.drillParamList" :key="drillParam" :label="drillParam">
                <Dictionary
                    v-model="selupdataDict[index][drillParam]"
                    :updata-dict="selupdataDict[index][drillParam]"
                    :dict-key="'DRILL_PROPERTIES'"
                    :dict-data="item.drillDictData"
                    :drill="{drill:true}"
                    @input="selectDrillParams($event, drillParam, item)"
                />
<!--                <el-select
                    size="mini"
                    v-model="item.params"
                    clearable
                    placeholder="请选择"
                    @input="selectDrillParams($event, drillParam,item)"
                    filterable
                >
                  <el-option
                      v-for="item in item.setParamList"
                      :key="item.label"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>-->
              </el-form-item>
              <el-form-item label="下钻参数类型">
                <el-select
                    size="mini"
                    v-model="item.drillParamType"
                    clearable
                    placeholder="请选择"
                    filterable
                >
                  <el-option value="表头"/>
                  <el-option value="单元格"/>
                </el-select>
              </el-form-item>
              <el-form-item label="下钻类型">
                <el-select
                    size="mini"
                    v-model="item.drillSetTyp"
                    clearable
                    placeholder="请选择"
                    @change="selectDataSetType"
                    filterable
                >
                  <el-option value="数据表格"/>
                  <el-option value="详情页面"/>
                  <el-option value="宿舍页面"/>
                  <el-option value="大屏/报表/画像"/>
                </el-select>
              </el-form-item>
              <el-form-item v-if="item.drillSetTyp == '宿舍页面'" label="宿舍页面">
                <el-select
                    size="mini"
                    v-model="item.drillReportSSId"
                    clearable
                    placeholder="请选择"
                    @change="selectDataSetType"
                    filterable
                >
                  <el-option
                      v-for="zxData in xzDataSet"
                      :key="zxData.id"
                      :label="zxData.setName"
                      :value="zxData.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="item.showReport" label="大屏/报表/画像">
                <el-select
                    size="mini"
                    v-model="item.drillReportId"
                    clearable
                    placeholder="请选择"
                    @change="selectDataSetType"
                    filterable
                >
                  <el-option
                      v-for="item in reportArr"
                      :key="item"
                      :value="item"
                  />
                </el-select>
              </el-form-item>
            </div>
         </el-collapse-transition>
      </div>
      <el-button style="width: 100%" type="primary" size="mini" @click="saveDataBtn">装载数据到图表</el-button>
    </el-form>
  </div>
</template>
<script>
import {detailBysetId, getData, queryAllDataSet} from "@/api/bigscreen";
import Dictionary from "@/components/Dictionary/index";
import {ListReportNoLogin} from "@/api/report";

export default {
  name: "DynamicComponents",
  components: {
    Dictionary
  },
  model: {
    prop: "formData",
    event: "input"
  },
  props: {
    chartType: String,
    dynamicData: Object,
    props: ["formData"],
  },
  data() {
    return {
      seldrillLevel: 0,
      seldrillSetList: [],
      seltbDataSet: [],
      selparams:{},
      selupdataDict: [],
      drillDictData:[],
      form:{},
      typeoptions:[
        {
          value: 'select',
          label: '下拉框'
        },{
          value: 'data',
          label: '日期选择器'
        },
      ],
      // dataSetValue: this.dynamicData.setCode,
      dataSetValue: "",

      drillDataSetValue: "",
      dataSet: [], // 数据集
      tbDataSet: [], // 图标数据集
      xzDataSet: [], // 下钻数据集
      sxDataSet: [],// 筛选数据集
      // userNameList: [], //
      setParamList: [], // 左侧的参数(即数据集结果的列)
      tempSetParamList: [], //  临时存储上一个数据集的参数
      // drillParamList: [], // 下钻数据集sql对应的动态参数
      /*mappingParamList:[ //下钻数据集sql对应的映射参数
          {label:'name',value:'name'},
          {label:'value',value:'value'}
      ],*/
      mappingParamArray:[
          {id:'name',name:'name', value: 'name'}
      ],
      mappingParam:'',//下钻数据集sql对应的映射参数
      params: {},
      chartProperties: {},
      drillProperties: {},
      drillLevel: 0,
      dictValueList:[],
      drillSetList: [], // 下钻数据集配置
      show3: true,
      reportArr: [],
      drillReportid: ``,
      IdArr: [],
      updataDict: ``,
      olddrill: ``,
    };
  },
  watch: {
    dynamicData:{
      handler( newValue, oldValue ){
        console.log(`this.chartType1`,this.chartType)
        console.log(`this.formData1`,this.formData)
        console.log(`this.dynamicData1`,this.dynamicData)
        this.loadDataSet();
		    this.queryByPage();
      },
      deep: true
    }
  },
  computed: {
    setCode() {
      let code = "";
      this.tbDataSet.forEach(el => {
        if (el.id == this.dataSetValue) {
          code = el.id;
        }
      });
      return code;
    },
    drillSetCode() {
      let code = "";
      this.xzDataSet.forEach(el => {
        if (el.id == this.drillDataSetValue) {
          code = el.id;
        }
      });
      return code;
    },
  },
  mounted() {
    console.log(`this.chartType`,this.chartType)
    console.log(`this.formData`,this.formData)
    console.log(`this.dynamicData`,this.dynamicData)
    this.loadDataSet();
		this.queryByPage();

  },
  updated() {
  },
  methods: {
    async queryByPage() {
      const reqParam = {
        page: 1,
        pageSize: 1000,
        queryParam: {
          reportName: "",
          reportRroup: ""
        }
      }
      const res = await ListReportNoLogin(reqParam);
      let Arr = res.data.info.records
      this.reportArr = [];
      for(var i =0; i < Arr.length; i++ ){
        this.reportArr.push(Arr[i].reportName)
      }
      this.IdArr = Arr;
    },
    loadDataSet() {
      console.log(`dynamicData===`,this.dynamicData)
      if( this.dynamicData.setCode || this.dynamicData.drillSetList ) {
        this.dataSetValue = this.dynamicData.setCode;
        this.updataDict = this.dynamicData.chartProperties;
        this.dynamicData.drillSetList.forEach((dr,index) => {
          // this.selupdataDict = Object.assign(this.seldrillSetList, dr.drillProperties);
          this.selupdataDict[index] = dr.drillProperties

        });
        this.chartProperties = this.dynamicData.chartProperties;
        this.drillLevel = this.dynamicData.drillSetList.length;
        if(this.dynamicData.seldynamicData){
          this.seldrillLevel = this.dynamicData.seldynamicData.length;
        }

        this.olddrill = this.dynamicData.drillSetList.length;
        // this.selectDataSet(this.dataSetValue);
        if (this.dataSetValue) {
          detailBysetId({id: this.dataSetValue}).then(res => {
            if (res.data.code === '00000') {
              // this.userNameList = res.data.info.dataSetParamDtoList;
              this.setParamList = res.data.info.setParamList;
            }
          }).catch(res => {
            this.$message.error(res.info);
          });
        }
        this.handleChange(this.drillLevel);
        this.selhandleChange(this.seldrillLevel)
        // this.drillSetList = this.dynamicData.drillSetList;
      }else {
        this.dataSetValue = ''
        this.updataDict = {};
        this.selupdataDict = []
        this.chartProperties = {};
        this.drillLevel = 0
        if(this.dynamicData.seldynamicData){
          this.seldrillLevel = this.dynamicData.seldynamicData.length;
        }
        this.olddrill = 0
        this.seldrillLevel = 0
        this.setParamList = []
        // this.selectDataSet(this.dataSetValue);
        if (this.dataSetValue) {
          detailBysetId({id: this.dataSetValue}).then(res => {
            if (res.data.code === '00000') {
              // this.userNameList = res.data.info.dataSetParamDtoList;
              this.setParamList = res.data.info.setParamList;
            }
          }).catch(res => {
            this.$message.error(res.info);
          });
        }
        this.handleChange(this.drillLevel);
        this.selhandleChange(this.seldrillLevel)
      }
      queryAllDataSet().then(res => {
        if (res.data.code === '00000') {
          this.dataSet = res.data.info;
          this.tbDataSet = [];
          this.xzDataSet = [];
          this.seltbDataSet = [];
          this.sxDataSet = [];
          this.dataSet.forEach(o=>{
            if (o.type === '图表') {
              this.tbDataSet.push(o);
              this.seltbDataSet.push(o);
            }else if(o.type === '下钻'){
              this.xzDataSet.push(o);
            }else if(o.type === '筛选'){
              this.sxDataSet.push(o);
                          }
          })
          /*if (this.dataSetValue) {
            this.selectDataSet();
          }*/
        }
      }).catch(res => {
        this.$message.error(res.info);
      });

    },
    selectDataSetType(item){
      if ( item && item.indexOf(`大屏`)!==-1) {
        for (let a = 0; a < this.drillSetList.length; a++) {
          this.drillSetList[a].showReport = true;
        }
      }
    },
    selectDataSet(item) {
      if (this.dataSetValue) {
        this.chartProperties = {};
        detailBysetId({id: this.dataSetValue}).then(res => {
          if (res.data.code === '00000') {
            // this.userNameList = res.data.info.dataSetParamDtoList;
            this.setParamList = res.data.info.setParamList;
          }
        }).catch(res => {
          this.$message.error(res.info);
        });
      }
      // if ( item && item.indexOf(`大屏`)!==-1) {
      //   for (let a = 0; a < this.drillSetList.length; a++) {
      //     this.drillSetList[a].showReport = true;
      //   }
      // }
    },
    selectDrillDataSet(item,index) {
      let self = this;
      if (item.drillSetCode) {
        detailBysetId({id: item.drillSetCode}).then(res => {
          if (res.data.code === '00000') {
            item.drillDictData = [];
            item.drillDesc = res.data.info.setDesc;
            item.drillFieldConfig = res.data.info.fieldConfig;
            item.drillParamList = res.data.info.drillParamList;
            if (item.index === 0) {
              item.setParamList = self.mappingParamArray;
            } else if (self.tempSetParamList !== null && self.tempSetParamList.length > 0) {
              // item.setParamList = res.data.info.setParamList;
              let drillDictArr = [];
              self.tempSetParamList.forEach(tmp => {
                drillDictArr.push({
                  id:tmp,
                  name:tmp,
                  value:tmp
                });
              });
              /*self.tempSetParamList.forEach(tmp => {
                  item.setParamList.push({
                    id:tmp,
                    name:tmp,
                    value:tmp
                  });
              });*/
              item.drillDictData = drillDictArr;
              // item.setParamList = self.tempSetParamList == null ? [] : self.tempSetParamList;
            }
            self.tempSetParamList = res.data.info.setParamList;
          }
        }).catch(res => {
          this.$message.error(res.info);
        });
      }
    },
    selectParams(val, key) {
      this.chartProperties[key] = val;
    },
    async saveDataBtn() {
      const contextData = {};
      for( var j = 0 ; j < this.drillSetList.length; j++){
        if(this.drillSetList[j].drillReportId !== ""){
          for(var a=0; a < this.IdArr.length; a++){
            if( this.IdArr[a].reportName == this.drillSetList[j].drillReportId){
              this.drillReportid = this.IdArr[a].id;
            }
          }
        }
      }
      const selparam = [];
      for( let i =0; i < this.seldrillSetList.length; i++ ) {
        var obj2 = {};
        obj2.index = this.seldrillSetList[i].index;
        obj2.chartType = ``;
        obj2.type = this.seldrillSetList[i].type;
        obj2.setCode = this.seldrillSetList[i].dataSetValue;
        obj2.chartProperties = this.seldrillSetList[i].chartProperties;
        obj2.show = false;
        obj2.dataSetValue = this.seldrillSetList[i].dataSetValue;
        obj2.setParamList = this.seldrillSetList[i].setParamList;
        obj2.contextData = {};
        obj2.placeholder = this.seldrillSetList[i].placeholder;
        await selparam.push(obj2)
      }
      const params = {
        chartType: this.chartType,
        setCode: this.setCode,
        chartProperties: this.chartProperties,
        // drillSetCode: this.drillSetCode,
        // drillProperties: this.drillProperties,
        drillSetList: this.drillSetList,
        drillReportid: this.drillReportid,
        contextData,
        seldynamicData: selparam.length > 0 ? selparam : null,
        seldrillShow: this.dynamicData.seldrillShow
      };
      console.log(`params`,params)

      this.$emit("input", params);
      this.$emit("change", params);
    },
    selectDrillParams(val, key,item) {
      item.drillProperties[key] = val;
    },
    checkShow(item) {
      this.dynamicData.drillSetList = this.drillSetList;
      this.drillSetList[item.index].show = !this.drillSetList[item.index].show;
      // item.show = !item.show
    },
    handleChange(value) {
      var arr = [];
      for (var i = 0; i < value; i++) {
        var obj = {};
        obj.index = i;
        obj.drillSetName = "";
        obj.drillSetCode = "";
        obj.drillReportId = "";
        obj.drillReportSSId = "";
        obj.drillParamList = [];
        obj.drillProperties = {};
        obj.show = false
        obj.showReport = false
        obj.drillDesc = ""
        arr.push(obj)
      }
      if( this.dynamicData.drillSetList ) {
          if( value >= this.dynamicData.drillSetList.length ) {
            this.drillSetList = arr;
            this.drillSetList.splice(0,this.dynamicData.drillSetList.length, ...this.dynamicData.drillSetList);
          } else {
            this.drillSetList.splice(value,this.dynamicData.drillSetList.length - value);
            this.dynamicData.drillSetList.splice(value,this.dynamicData.drillSetList.length - value);
          }
          // this.dynamicData.drillSetList = this.drillSetList;
      } else{
        this.drillSetList = arr;
      }

    },
    selhandleChange(value) {
      var arr2 = [];
      for (var i = 0; i < value; i++) {
        var obj2 = {};
        obj2.index = i;
        obj2.chartType = "";
        obj2.type = 'select';
        obj2.setCode = "";
        obj2.chartProperties = {
          key: '',
        };
        obj2.drillReportid = [];
        obj2.contextData = {};
        obj2.show = false;
        obj2.dataSetValue = ``;
        obj2.setParamList = [];
        arr2.push(obj2)
      }
      if( this.dynamicData.seldynamicData ) {
          if( value >= this.dynamicData.seldynamicData.length ) {
            this.seldrillSetList = arr2;
            this.seldrillSetList.splice(0,this.dynamicData.seldynamicData.length, ...this.dynamicData.seldynamicData);
          } else {
            this.seldrillSetList.splice(value,this.dynamicData.seldynamicData.length - value);
            this.dynamicData.seldynamicData.splice(value,this.dynamicData.seldynamicData.length - value);
          }
          // this.dynamicData.drillSetList = this.drillSetList;
      } else{
        this.seldrillSetList = arr2;
      }
      // this.seldrillSetList = arr;
    },
    selcheckShow( item ) {
      this.dynamicData.seldrillSetList = this.seldrillSetList;
      this.seldrillSetList[item.index].show = !this.seldrillSetList[item.index].show;
    },
    selselectDataSet( item ) {
      if (item.dataSetValue) {
        item.chartProperties = {};
        getData({setCode: item.dataSetValue,contextData:{}}).then(res => {
          if (res.data.code === '00000') {
            let value = res.data.info
            item.setParamList = value
          }
        }).catch(res => {
          this.$message.error(res.info);
        });
      }
    },
    selselectParams( val, key, index ) {
      this.seldrillSetList[index].chartProperties[key] = val;
    },


  }
};
</script>
<style  scoped lang="scss">
.cengshu{
  width: 100%;
}
.el-button-h{
  height: 40px;
  line-height: 40px;
  background: transparent;
  color: #bcc9d4;
  font-weight: 300;
  font-size: 12px;
  border-color: #282e3a;
  width: 100%;
  padding: 0;
  margin: 0;
  border-left: 0;
  border-right: 0;
  border-top: 0;
}
.el-collapse-item__tit{
  float: left;
  color: #bfcbd9 !important;
  font-weight: normal !important;
}
.el-collapse-item__arrow{
  float: right;
  line-height: 40px;

}

</style>
