<template>
  <div>
    <el-button
      type="primary"
      size="small"
      icon="el-icon-plus"
      plain
      @click="handleAddClick"
      >新增</el-button
    >
    <el-table :data="formData" style="width: 100%">
      <el-table-column prop="name" label="名称" width="80" />
      <el-table-column prop="key" label="key值" width="80" />
      <el-table-column prop="is" label="是否开启条件判断添加字体颜色" width="80" >
        <template slot-scope="scope">
          <span>
            {{formData[scope.$index].is ? '是' : '否'}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            @click="handleEditorClick(scope.$index, scope.row)"
            type="text"
            size="small"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleDeleteClick(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="新增"
      :visible.sync="dialogVisible"
      width="50%"
      destroy-on-close
      :before-close="handleClose"
    >
      <el-form :model="rowFormData" :key="formindex" label-width="50px">
        <el-form-item label-width="60px" label="名称:">
          <el-input
            v-model.trim="rowFormData['name']"
            placeholder="请输入名称"
            size="mini"
          >
          </el-input>
        </el-form-item>
        <el-form-item label-width="60px" label="key值:">
          <el-input
            v-model.trim="rowFormData['key']"
            placeholder="请输入key值"
            size="mini"
          >
          </el-input>
        </el-form-item>
        <el-form-item label-width="200px" label="是否开启条件判断添加字体颜色:">
          <el-switch
              v-model.trim="rowFormData['is']"
              >
          </el-switch>
        </el-form-item>
        <avue-form v-if="rowFormData['is']" :option="option" v-model="rowFormData['rowform']">
        </avue-form>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleSaveClick"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import ColorPicker from "./colorPicker.vue";
// import vueJsonEditor from "vue-json-editor";
// import dynamicComponents from "@/views/report/bigscreen/designer/form/dynamicComponents";
// import customColorComponents from "@/views/report/bigscreen/designer/form/customColorComponents";
// import customUpload from "@/views/report/bigscreen/designer/form/customUpload";
// import selectdycustComponents from "@/views/report/bigscreen/designer/form/selectdycustComponents";

export default {
  model: {
    prop: "formData",
    event: "input"
  },
  components: {
    // ColorPicker,
    // vueJsonEditor,
    // dynamicComponents,
    // customColorComponents,
    // customUpload,
    // selectdycustComponents
  },
  props: {
    formData: Array
  },
  data() {
    return {
      dialogVisible: false,
      rowFormData: {
        name: "",
        key: "",
        width: ""
      },
      formindex: 0,
      option: {
        labelWidth: 0,
        searchBtn: false,
        emptyBtn: false,
        saveBtn: false,
        submitBtn: false,
        column: [
          {
            prop: 'dynamic',
            type: 'dynamic',
            span:24,
            children: {
              searchBtn: false,
              emptyBtn: false,
              saveBtn: false,
              submitBtn: false,

              align: 'center',
              labelWidth: 0,
              headerAlign: 'center',
              rowAdd:(done)=>{
                this.$message.success('新增回调');
                done({
                  input:'默认值'
                });
              },
              rowDel:(row,done)=>{
                this.$message.success('删除回调'+JSON.stringify(row));
                done();
              },
              column: [{
                width: 100,
                label: '最小值',
                prop: "min"
              },{
                width: 100,
                label: '最大值',
                prop: "max"
              },{
                label: '字体颜色',
                prop: 'color',
                type: 'color'
              },{
                label: '背景颜色',
                prop: 'backcolor',
                type: 'color'
              }]
            }
          },

        ]
      },
      flag: true, // true 新增， false 编辑
      indexEditor: -1, // 编辑第几个数据
      tableData: []
    };
  },
  methods: {
    // 新增
    handleAddClick() {
      this.rowFormData = {};
      this.flag = true;
      this.dialogVisible = true;
    },
    // 编辑
    handleEditorClick(index, row) {
      this.flag = false;
      this.rowFormData = this.deepClone(row);
      this.formindex++
      this.indexEditor = index;
      this.dialogVisible = true;
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false;
    },
    // 保存
    handleSaveClick() {
      if (this.flag) {
        // 新增
        this.formData.push(this.rowFormData);
        this.dialogVisible = false;
      } else {
        // 编辑
        this.formData[this.indexEditor] = this.rowFormData;
        this.$set(this.formData, this.indexEditor, this.rowFormData);
        // this.dialogVisible = false;
      }
      this.$emit("input", this.formData);
      this.$emit("change", this.formData);
      this.dialogVisible = false;
    },
    // 删除
    handleDeleteClick(index) {
      this.formData.splice(index, 1);
      this.$emit("input", this.formData);
      this.$emit("change", this.formData);
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep::-webkit-scrollbar-track-piece {
  background-color: transparent;
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 0; // 横向滚动条
  height: 8px; // 纵向滚动条 必写
}
// 滚动条的滑块
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 5px !important;
  background-color: rgba(144, 146, 152, 0.3);
}
::v-deep  ::-webkit-scrollbar-thumb:horizontal {
  background-color: #dcdfe6;
  -webkit-border-radius: 0px !important;
}
::v-deep.el-table,
::v-deep.el-table__expanded-cell,
::v-deep.el-table th,
::v-deep.el-table tr {
  background-color: transparent !important;
  color: #859094 !important;
  font-size: 12px !important;
}

::v-deep.el-table td,
::v-deep.el-table th.is-leaf {
  border-bottom: none;
  line-height: 26px;
}
::v-deep.el-table tbody tr:hover {
  background-color: #263445 !important;
}
::v-deep.el-table tbody tr:hover > td {
  background-color: #263445 !important;
}
::v-deep.el-table::before {
  height: 0;
}
</style>
