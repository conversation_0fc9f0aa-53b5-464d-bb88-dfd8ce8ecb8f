<template>
  <div>
    <el-form ref="form" :model="form" label-width="100px" label-position="left">
      <el-form-item label="查询数据集个数">
        <el-input-number v-model="drillLevel" controls-position="right" @change="handleChange" :min="0" :max="10" size="mini" class="cengshu"></el-input-number>
      </el-form-item>
      <div v-for="(item,index) in drillSetList" :key="index">
        <el-button @click="checkShow(item)" class="el-button-h"> <span class="el-collapse-item__tit">数据集{{index+1}}</span>  <i v-if="!item.show" class="el-collapse-item__arrow el-icon-arrow-right"></i> <i v-if="item.show" class="el-collapse-item__arrow el-icon-arrow-down"></i> </el-button>
          <el-collapse-transition>
            <div v-show="item.show">
                <el-form-item class="transition-box" label="数据集">
                    <el-select
                        size="mini"
                        v-model="item.dataSetValue"
                        clearable
                        placeholder="请选择"
                        @change="selectDataSet(item)"
                        filterable
                    >
                    <el-option
                        v-for="item in tbDataSet"
                        :key="item.id"
                        :label="item.setName"
                        :value="item.id"
                    />
                    </el-select>
                </el-form-item>
                <el-form-item v-for="items in item.setParamList" :key="items" :label="items">
                    <Dictionary
                        v-model="params"
                        :updata-dict="updataDict[items]"
                        :dict-key="'CHART_PROPERTIES'"
                        @input="selectParams($event, items, item)"
                    />
                </el-form-item>    
            </div>
         </el-collapse-transition>
      </div>
      <el-button style="width: 100%" type="primary" size="mini" @click="saveDataBtn">装载数据到图表</el-button>
    </el-form>
  </div>
</template>
<script>
import {detailBysetId, queryAllDataSet} from "@/api/bigscreen";
import Dictionary from "@/components/Dictionary/index";
import {ListReportNoLogin} from "@/api/report";

export default {
  name: "DynamicComponents",
  components: {
    Dictionary
  },
  model: {
    prop: "formData",
    event: "input"
  },
  props: {
    chartType: String,
    dynamicData: Object,
    props: ["formData"],
  },
  data() {
    return {
      form:{},
      dataSetValue: "",
      drillDataSetValue: "",
      dataSet: [], // 数据集
      tbDataSet: [], // 图标数据集
      xzDataSet: [], // 下钻数据集
      setParamList: [], // 左侧的参数(即数据集结果的列)
      tempSetParamList: [], //  临时存储上一个数据集的参数
      mappingParamArray:['name'],
      mappingParam:'',//下钻数据集sql对应的映射参数
      params: {},
      chartProperties: {},
      drillProperties: {},
      drillLevel: 0,
      dictValueList:[],
      drillSetList: [], // 下钻数据集配置
      show3: true,
      reportArr: [],
      drillReportid: ``,
      IdArr: [],
      updataDict: ``,
      olddrill: ``,
    };
  },
  watch: {
    dynamicData:{
      handler( newValue, oldValue ){
        this.loadDataSet();
		    // this.queryByPage();
      },
      deep: true
    }
  },
  computed: {
  },
  mounted() {
    this.loadDataSet();
		// this.queryByPage();
    
  },
  updated() {
  },
  methods: {
    // async queryByPage() {
    //   const reqParam = {
    //     page: 1,
    //     pageSize: 1000,
    //     queryParam: {
    //       reportName: "",
    //       reportRroup: ""
    //     }
    //   }
    //   const res = await ListReportNoLogin(reqParam);
    //   let Arr = res.data.info.records
    //   this.reportArr = [];
    //   for(var i =0; i < Arr.length; i++ ){
    //     this.reportArr.push(Arr[i].reportName)
    //   }
    //   this.IdArr = Arr;
    // },
    loadDataSet() {
      queryAllDataSet().then(res => {
        if (res.data.code === '00000') {
          this.dataSet = res.data.info;
          this.tbDataSet = [];
          this.xzDataSet = [];
          this.dataSet.forEach(o=>{
            if (o.type === '图表') {
              this.tbDataSet.push(o);
            }else if(o.type === '下钻'){
              this.xzDataSet.push(o);
            }
          })
        }
      }).catch(res => {
        this.$message.error(res.info);
      });

    },
    selectDataSet(item) {
      if (item.dataSetValue) {
        item.chartProperties = {};
        detailBysetId({id: item.dataSetValue}).then(res => {
          if (res.data.code === '00000') {
            item.setParamList = res.data.info.setParamList;
          }
        }).catch(res => {
          this.$message.error(res.info);
        });
      }
    },
    // selectDrillDataSet(item) {
    //   let self = this;
    //   if (item.drillSetCode) {
    //     detailBysetId({id: item.drillSetCode}).then(res => {
    //       if (res.data.code === '00000') {
    //         item.drillDesc = res.data.info.setDesc;
    //         item.drillFieldConfig = res.data.info.fieldConfig;
    //         item.drillParamList = res.data.info.drillParamList;
    //         if (item.index === 0) {
    //           item.setParamList = self.mappingParamArray;
    //         } else {
    //           // item.setParamList = res.data.info.setParamList;
    //           item.setParamList = self.tempSetParamList==null?[]:self.tempSetParamList;
    //         }
    //         self.tempSetParamList = res.data.info.setParamList;
    //       }
    //     }).catch(res => {
    //       this.$message.error(res.info);
    //     });
    //   }
    // },
    selectParams(val, key, item) {
      item.chartProperties[key] = val;
    },
    async saveDataBtn() {
    const params = [];
      console.log(`drillSetList`,this.drillSetList)
      for( let i =0; i < this.drillSetList.length; i++ ) {
        var obj2 = {};
        obj2.chartType = this.chartType;
        obj2.setCode = this.drillSetList[i].dataSetValue;
        obj2.chartProperties = this.drillSetList[i].chartProperties;
        obj2.contextData = {};
        params.push(obj2)
      }
      this.$emit("input", params);
      this.$emit("change", params);
    },
    checkShow(item) {
      // this.dynamicData.drillSetList = this.drillSetList;
      this.drillSetList[item.index].show = !this.drillSetList[item.index].show;
    },
    handleChange(value) {
      var arr = [];
      for (var i = 0; i < value; i++) {
        var obj = {};
        obj.index = i;
        obj.chartType = "";
        obj.setCode = "";
        obj.chartProperties = "";
        obj.drillReportid = [];
        obj.contextData = {};
        obj.show = false;
        obj.dataSetValue = ``;
        obj.setParamList = [];
        arr.push(obj)
      }
      
    this.drillSetList = arr;
    },
  }
};
</script>
<style  scoped lang="scss">
.cengshu{
  width: 100%;
}
.el-button-h{
  height: 40px;
  line-height: 40px;
  background: transparent;
  color: #bcc9d4;
  font-weight: 300;
  font-size: 12px;
  border-color: #282e3a;
  width: 100%;
  padding: 0;
  margin: 0;
  border-left: 0;
  border-right: 0;
  border-top: 0;
}
.el-collapse-item__tit{
  float: left;
  color: #bfcbd9 !important;
  font-weight: normal !important;
}
.el-collapse-item__arrow{
  float: right;
  line-height: 40px;

}

</style>
