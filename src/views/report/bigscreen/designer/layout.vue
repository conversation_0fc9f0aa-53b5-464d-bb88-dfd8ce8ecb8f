<template>
  <div class="layout">
    <div
      v-if="toolIsShow"
      class="layout-left"
      :style="{ width: widthLeftForTools + 'px' }"
    >
      <el-tabs type="border-card" :stretch="true">
        <!-- 左侧组件栏-->
        <el-tab-pane label="工具栏" class="border_cards">
          <el-tabs tab-position="left" style="height: calc(100vh - 70px);">
            <el-tab-pane v-for="(item,index) in listTools" :key="index" style="width: 100%;">
              <span slot="label"><i class="iconfont" :class="item.icon"></i></span>
              <draggable
                  v-for="widget in item.list"
                  :key="widget.code"
                  @end="(evt) => widgetOnDragged(evt, widget.code)"
                >
                  <div class="tools-item">
                    <span class="tools-item-icon">
                      <i class="iconfont" :class="widget.icon"></i>
                    </span>
                    <span class="tools-item-text" :title="widget.label">{{ widget.label }}</span>
                  </div>
                </draggable>
            </el-tab-pane>
          </el-tabs>
          <!-- <draggable
            v-for="widget in widgetTools"
            :key="widget.code"
            @end="(evt) => widgetOnDragged(evt, widget.code)"
          >
            <div class="tools-item">
              <span class="tools-item-icon">
                <i class="iconfont" :class="widget.icon"></i>
              </span>
              <span class="tools-item-text">{{ widget.label }}</span>
            </div>
          </draggable> -->
          <!-- <div v-for="(item,index) in listTools" :key="index">
            <el-button @click="item.show = !item.show" class="el-button-h">
              <span class="el-collapse-item__tit"><i class="iconfont iconziyuan el-collapse-item__icon"></i>{{item.type}}</span>
              <i
                v-if="!item.show"
                class="el-collapse-item__arrow el-icon-arrow-right"
              ></i>
              <i
                v-if="item.show"
                class="el-collapse-item__arrow el-icon-arrow-down"
              ></i>
            </el-button>
            <el-collapse-transition>
              <div v-show="item.show">
                <draggable
                  v-for="widget in item.list"
                  :key="widget.code"
                  @end="(evt) => widgetOnDragged(evt, widget.code)"
                >
                  <div class="tools-item">
                    <span class="tools-item-icon">
                      <i class="iconfont" :class="widget.icon"></i>
                    </span>
                    <span class="tools-item-text">{{ widget.label }}</span>
                  </div>
                </draggable>
              </div>
            </el-collapse-transition>
          </div> -->
          <!-- <div>
            <el-button @click="navShow = !navShow" class="el-button-h">
              <span class="el-collapse-item__tit"><i class="iconfont iconziyuan el-collapse-item__icon"></i>柱图</span>
              <i
                v-if="!navShow"
                class="el-collapse-item__arrow el-icon-arrow-right"
              ></i>
              <i
                v-if="navShow"
                class="el-collapse-item__arrow el-icon-arrow-down"
              ></i>
            </el-button>
            <el-collapse-transition>
              <div v-show="navShow">
                <draggable
                  v-for="widget in widgetTools"
                  :key="widget.code"
                  @end="(evt) => widgetOnDragged(evt, widget.code)"
                >
                  <div class="tools-item">
                    <span class="tools-item-icon">
                      <i class="iconfont" :class="widget.icon"></i>
                    </span>
                    <span class="tools-item-text">{{ widget.label }}</span>
                  </div>
                </draggable>
              </div>
            </el-collapse-transition>
          </div> -->
        </el-tab-pane>
        <!-- 左侧图层-->
        <el-tab-pane label="图层">
          <draggable
            v-model="layerWidget"
            @update="datadragEnd"
            :options="{ animation: 300 }"
          >
            <transition-group>
              <div
                v-for="(item, index) in layerWidget"
                :key="'item' + index"
                class="tools-item"
                :class="widgetIndex == index ? 'is-active' : ''"
                @click="layerClick(index)"
              >
                <span class="tools-item-icon">
                  <i class="iconfont" :class="item.icon"></i>
                </span>
                <span class="tools-item-text">{{ item.label }}</span>
              </div>
            </transition-group>
          </draggable>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div
      class="layout-left-fold"
      :style="{ width: widthLeftForToolsHideButton + 'px' }"
      @click="toolIsShow = !toolIsShow"
    >
      <i class="el-icon-arrow-right" />
    </div>

    <div
      class="layout-middle"
      :style="{ width: middleWidth + 'px', height: middleHeight + 'px' }"
    >
      <div class="top-button">
        <span class="btn">
          <el-tooltip
            class="item"
            effect="dark"
            content="保存"
            placement="bottom"
          >
            <i class="iconfont icon-save" @click="saveData"></i>
          </el-tooltip>
        </span>
        <span class="btn">
          <el-tooltip
            class="item"
            effect="dark"
            content="预览"
            placement="bottom"
          >
            <i class="iconfont iconyulan" @click="viewScreen"></i>
          </el-tooltip>
        </span>
        <span class="btn">
          <el-tooltip
            class="item"
            effect="dark"
            content="导入"
            placement="bottom"
          >
            <el-upload
              class="el-upload"
              ref="upload"
              :action="uploadUrl"
              accept=".zip"
              :on-success="handleUpload"
              :on-error="handleError"
              :show-file-list="false"
              :limit="1"
            >
              <i class="iconfont icondaoru"></i>
            </el-upload>
          </el-tooltip>
        </span>
        <span class="btn border-left">
          <ul class="nav">
            <li>
              <i class="iconfont icondaochu"></i
              ><i class="el-icon-arrow-down"></i>
              <ul>
                <li>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="适合当前系统"
                    placement="right"
                  >
                    <div @click="exportDashboard(1)">导出(包含数据集)</div>
                  </el-tooltip>
                </li>
                <li>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="适合跨系统"
                    placement="right"
                  >
                    <div @click="exportDashboard(0)">导出(不包含数据集)</div>
                  </el-tooltip>
                </li>
              </ul>
            </li>
          </ul>
        </span>
        <span class="dashboard_title"
          >Sanyth Report - {{ dashboard.components.title }}</span
        >
      </div>
      <div
        class="workbench-container"
        :style="{
          width: bigscreenWidthInWorkbench,
          height: bigscreenHeightInWorkbench,
          margin: `auto`
        }"
      >
          <widget
            ref="widgets"
            :widgets ="widgets"
            :dashboard="dashboard"
            :grade="grade"
            :dashboardoptions="dashboardoptions"
            @index="Layindex"
            @options="Options"
          >
          </widget>
      </div>
    </div>

    <div class="layout-right" :style="{ width: widthLeftForOptions + 'px' }">
      <el-tabs v-model="activeName" type="border-card" :stretch="true">
        <el-tab-pane
          v-if="
            isNotNull(widgetOptions.setup) || isNotNull(widgetOptions.collapse)
          "
          name="first"
          label="配置"
        >
          <dynamicForm
            ref="formData"
            :options="widgetOptions.setup"
            @onChanged="(val) => widgetValueChanged('setup', val)"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="isNotNull(widgetOptions.data)"
          name="second"
          label="数据"
        >
          <dynamicForm
            ref="formData"
            :options="widgetOptions.data"
            @onChanged="(val) => widgetValueChanged('data', val)"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="isNotNull(widgetOptions.position)"
          name="third"
          label="坐标"
        >
          <dynamicForm
            ref="formData"
            :options="widgetOptions.position"
            @onChanged="(val) => widgetValueChanged('position', val)"
          />
        </el-tab-pane>
        <el-tab-pane
           v-if="linkShow"
          name="fourth"
          label="联动"
        >
          <linkage
            :index="index"
            :options="linkaeList"
            :valuelist="valuelist"
            @onChanged="linkaeListChanged"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {
  insertDashboard,
  detailDashboard,
  importDashboard,
  exportDashboard,
} from "@/api/bigscreen";
import { widgetTools, getToolByCode } from "./laytools";
import widget from "./widget/layitem.vue";
import dynamicForm from "./form/dynamicForm.vue";
import draggable from "vuedraggable";
import VueRulerTool from "vue-ruler-tool"; // 大屏设计页面的标尺插件
import contentMenu from "./form/contentMenu";
import { baseUrl } from "@/config/env";
import VueGridLayout from 'vue-grid-layout';
import linkage from "./form/linkage.vue"

export default {
  name: "Login",
  components: {
    draggable,
    VueRulerTool,
    widget,
    dynamicForm,
    contentMenu,
    linkage,
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem
  },
  data() {
    return {
      index: ``,
      valuelist: [],
      linkaeList:[],
      listTools: [],
      linkShow: false,
      uploadUrl:
        baseUrl +
        "/report/dashboard/import?reportCode=" +
        this.$route.query.reportId,
      grade: -1,
      layerWidget: [],
      widgetTools: widgetTools, // 左侧工具栏的组件图标，将js变量加入到当前作用域
      widthLeftForTools: 200, // 左侧工具栏宽度
      widthLeftForToolsHideButton: 15, // 左侧工具栏折叠按钮宽度
      widthLeftForOptions: 300, // 右侧属性配置区
      widthPaddingTools: 18,
      toolIsShow: true, // 左侧工具栏是否显示
      bigscreenWidth: 1920, // 大屏设计的大小
      bigscreenHeight: 1080,
      // 工作台大屏画布，保存到表report_dashboard中
      components:{},
      dashboard: {
        id: null,
        components: {},
        title: "", // 大屏页面标题
        colNum: 24, // 大屏设计宽度
        rowHeight: 20, // 大屏设计高度
        backgroundColor: "rgba(101, 150, 198, 0.51)", // 大屏背景色
        backgroundImage: "", // 大屏背景图片
        refreshSeconds: null, // 大屏刷新时间间隔
        presetLine: [], // 辅助线
        presetLineVisible: false, // 辅助线是否显示
        borderRadius: 0,
        backgroundColor2: "rgba(101, 150, 198, 0.51)",
        isShow: true,
        shadowType: "outset",
        setX: 0,
        setY: 0,
        shadowBlur: 0,
        shadowSpread: 0,
        shadowColor: "rgba(101, 150, 198, 0.51)",
        height:{},
      },
      // 大屏的标记
      screenCode: "",
      dashboardoptions: {},
      // 大屏画布中的组件
      widgets: [
        {
          x: 0,
          y: 0,
          w: 2,
          h: 2,
          // type和value最终存到数据库中去，保存到report_dashboard_widget中
          type: "widget-text",
          value: {
            setup: {},
            data: {},
            position: {
              w: 2,
              h: 2,
              x: 0,
              y: 0,
              i: `0`,
            },
          },
          // options属性是从工具栏中拿到的tools中拿到
          options: [],
        },
      ], // 工作区中拖放的组件

      // 当前激活组件
      widgetIndex: 0,
      // 当前激活组件右侧配置属性
      widgetOptions: {
        setup: [], // 配置
        data: [], // 数据
        position: [], // 坐标
      },
      flagWidgetClickStopPropagation: false, // 点击组件时阻止事件冒泡传递到画布click事件上
      styleObj: {
        left: 0,
        top: 0,
      },
      visibleContentMenu: false,
      rightClickIndex: -1,
      activeName: "first",
    };
  },
  computed: {
    // 左侧折叠切换时，动态计算中间区的宽度
    middleWidth() {
      var widthLeftAndRight = 0;
      if (this.toolIsShow) {
        widthLeftAndRight += this.widthLeftForTools; // 左侧工具栏宽度
      }
      widthLeftAndRight += this.widthLeftForToolsHideButton; // 左侧工具栏折叠按钮宽度
      widthLeftAndRight += this.widthLeftForOptions; // 右侧配置栏宽度

      var middleWidth = this.bodyWidth - widthLeftAndRight;
      return middleWidth;
    },
    middleHeight() {
      return this.bodyHeight;
    },
    // 大屏在设计模式的大小
    bigscreenWidthInWorkbench() {
      // return this.getPXUnderScale(this.bigscreenWidth) + this.widthPaddingTools;
      return `calc(100% - 40px)`;
    },
    bigscreenHeightInWorkbench() {
      return `calc(100vh - 80px)`;
    },
  },
  watch: {
    widgets: {
      handler(val) {
        console.log(val)
        this.handlerLayerWidget(val);
        this.widgets = val;
        for( let i =0; i<val.length; i++ ){
          this.widgets[i].value.position.x = val[i].x;
          this.widgets[i].value.position.y = val[i].y;
          this.widgets[i].value.position.w = val[i].w;
          this.widgets[i].value.position.h = val[i].h;
        }
      },
      deep: true,
    },
  },
  mounted() {
    // 如果是新的设计工作台
    this.dashboardoptions = getToolByCode("screen")["options"];
    this.widgetsToolsData();
    this.initEchartData();
    this.widgets = [];
  },
  updated() {
  },
  methods: {
    handlerLayerWidget(val) {
      const layerWidgetArr = [];
      for (let i = 0; i < val.length; i++) {
        const obj = {};
        obj.icon = getToolByCode(val[i].type).icon;
        const options = val[i].options["setup"];
        options.forEach((el) => {
          if (el.name == "layerName") {
            obj.label = el.value;
          }
        });
        layerWidgetArr.push(obj);
      }
      this.layerWidget = layerWidgetArr;
    },
    widgetsToolsData() {
      let typeTools = [];
      for( let l = 0; l<this.widgetTools.length; l++ ) {
        if( typeTools.indexOf(this.widgetTools[l].type) == -1 ) {
          typeTools.push(this.widgetTools[l].type)
        }
      }
      for( let j = 0; j<typeTools.length; j++ ) {
        this.listTools.push({type:``,list:[],show: false,icon: ``});
        this.listTools[j].type = typeTools[j];
        for ( let h = 0; h<this.widgetTools.length; h++ ){
          if( this.widgetTools[h].type == this.listTools[j].type ){
            this.listTools[j].list.push(this.widgetTools[h]);
          }
        }
        this.listTools[j].icon = this.listTools[j].list[0].icon;
      }
      let first = {
        type: `all`,
        list: this.widgetTools,
        icon: `iconliebiao`,
      }
      this.listTools.unshift(first);
    },
    async initEchartData() {
      const reportCode = this.$route.query.reportId;
      const data = await detailDashboard({
        reportCode: reportCode,
      });
      if (data.data.code != "00000") return;
      const processData = this.handleInitEchartsData(data.data.info);
      this.widgets = processData;
      if( data.data.info.dashboard ){
        this.dashboard.components = data.data.info.dashboard.components;
        this.screenCode = "screen";
        this.activeName = "first";
        this.dashboardoptions = data.data.info.dashboard.components.dashboardoptions;
        this.widgetOptions = data.data.info.dashboard.components.dashboardoptions;
      } else {
        this.screenCode = "screen";
        this.activeName = "first";
        this.widgetOptions = this.dashboardoptions;
        this.dashboard.components = this.handleBigScreen(this.dashboardoptions);
      }

    },
    //dashboard的数据整理
    handleBigScreen(data) {
      const optionScreen = data;
      const setup = optionScreen.setup;
      const dashboardoptions = {};
      for (const key in data) {
        for (let i = 0; i < setup.length; i++) {
          let item = setup[i];
          if (Object.prototype.toString.call(item) == "[object Object]") {
              dashboardoptions[setup[i].name] = setup[i].value;
          } else if (Object.prototype.toString.call(item) == "[object Array]") {
            for (let j = 0; j < item.length; j++) {
              const list = item[j].list;
              for ( let h = 0; h < list.length; h++ ) {
                dashboardoptions[list[h].name] = list[h].value;
              }
            }
          }
        }
      }
      return dashboardoptions
    },
    //每个组件的数据整理
    handleInitEchartsData(data) {
      const widgets = data.dashboard ? data.dashboard.widgets : [];
      const widgetsData = [];
      for (let i = 0; i < widgets.length; i++) {
        var obj = {};
        obj.type = widgets[i].type;
        obj.value = {
          setup: widgets[i].value.setup,
          data: widgets[i].value.data,
          position: widgets[i].value.position,
        };
        const tool = this.deepClone(getToolByCode(widgets[i].type));
        const option = tool.options;
        const options = this.handleOptionsData(widgets[i].value, option);
        obj.options = options;
        obj.x = widgets[i].value.position.x;
        obj.y = widgets[i].value.position.y;
        obj.w = widgets[i].value.position.w;
        obj.h = widgets[i].value.position.h;
        obj.i = `${i}`;
        widgetsData.push(obj);
      }
      return widgetsData;
    },
    Options(options) {
      console.log(options)
      this.screenCode = "screen";
      this.activeName = "first";
      this.widgetOptions = options;
      this.linkShow = false;
    },
    handleOptionsData(data, option) {
      for (const key in data.setup) {
        for (let i = 0; i < option.setup.length; i++) {
          let item = option.setup[i];
          if (Object.prototype.toString.call(item) == "[object Object]") {
            if (key == option.setup[i].name) {
              option.setup[i].value = data.setup[key];
            }
          } else if (Object.prototype.toString.call(item) == "[object Array]") {
            for (let j = 0; j < item.length; j++) {
              const list = item[j].list;
              list.forEach((el) => {
                if (key == el.name) {
                  el.value = data.setup[key];
                }
              });
            }
          }
        }
      }
      // position
      for (const key in data.position) {
        for (let i = 0; i < option.position.length; i++) {
          if (key == option.position[i].name) {
            option.position[i].value = data.position[key];
          }
        }
      }
      // data
      for (const key in data.data) {
        for (let i = 0; i < option.data.length; i++) {
          if (key == option.data[i].name) {
            option.data[i].value = data.data[key];
          }
        }
      }
      return option;
    },
    // 保存数据
    async saveData() {
      if (!this.widgets || this.widgets.length == 0) {
        this.$message.error("请添加组件");
        return;
      }
      this.dashboard.components.dashboardoptions = this.dashboardoptions;
      const screenData = {
        reportCode: this.$route.query.reportId,
        dashboard: {
          components: this.dashboard.components
        },
        widgets: this.widgets,
      };
      const data = await insertDashboard(screenData);
      if (data.data.code == "00000") {
        this.$message.success("保存成功！");
      }
    },
    // 预览
    viewScreen() {
      var routeUrl = this.$router.resolve({
        path: "/bigscreen/layoutviewer",
        query: {
          reportId: this.$route.query.reportId,
        },
      });
      window.open(routeUrl.href, "_blank");
    },
    //  导出
    async exportDashboard(val) {
      const fileName = this.$route.query.reportId + ".zip";
      const param = {
        reportCode: this.$route.query.reportId,
        showDataSet: val,
      };
      exportDashboard(param).then((res) => {
        const blob = new Blob([res], { type: "application/octet-stream" });
        if (window.navigator.msSaveOrOpenBlob) {
          //msSaveOrOpenBlob方法返回bool值
          navigator.msSaveBlob(blob, fileName); //本地保存
        } else {
          const link = document.createElement("a"); //a标签下载
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          window.URL.revokeObjectURL(link.href);
        }
      });
    },
    getPXUnderScale(px) {
      return this.bigscreenScaleInWorkbench * px;
    },

    // 上传成功的回调
    handleUpload(response, file, fileList) {
      //清除el-upload组件中的文件
      this.$refs.upload.clearFiles();
      //刷新大屏页面
      this.initEchartData();
      this.$message({
        message: "导入成功！",
        type: "success",
      });
    },
    handleError() {
      this.$message({
        message: "上传失败！",
        type: "error",
      });
    },
    Layindex(data) {
      this.screenCode = ""
      // this.activeName = "first";
      if(data.type == 'widget-title'){
        this.linkShow = true
      }else {
        this.linkShow = false
      }
      this.index = data.index.i;
      this.widgetIndex = data.index.i;
      this.widgetOptions = data.options;
      this.linkaeList = data.linkaeList;
      this.valuelist = data.valuelist;
    },
    // 拖动一个组件放到工作区中去，在拖动结束时，放到工作区对应的坐标点上去
    widgetOnDragged(evt, widgetCode) {
      var widgetType = widgetCode;
      // 复制一个组件
      var tool = getToolByCode(widgetType);
      var widgetJson = {
        type: widgetType,
        value: {
          setup: {},
          data: {},
          position: {
            x: 0,
            y: 0,
            w: 2,
            h: 2,
            i: 0,
          },
        },
        options: tool.options,
      };
      // 处理默认值
      const widgetJsonValue = this.handleDefaultValue(widgetJson);
      // 将选中的复制组件，放到工作区中去
      this.widgets.push(this.deepClone(widgetJsonValue));
      this.widgets[this.widgets.length-1].i = `${this.widgets.length-1}`;
      // 激活新组件的配置属性
      // this.Layindex(this.widgets.length - 1);
      this.widgetOptions = this.deepClone(this.widgets[this.widgets.length - 1]["options"]);
      if(this.widgets[this.widgets.length - 1].type == 'widget-title'){
        this.linkShow = true
      }else {
        this.linkShow = false
      }
    },

    // 对组件默认值处理
    handleDefaultValue(widgetJson) {
      for (const key in widgetJson) {
        if (key == "options") {
          // collapse、data、position、setup
          // setup 处理
          for (let i = 0; i < widgetJson.options.setup.length; i++) {
            const item = widgetJson.options.setup[i];
            if (Object.prototype.toString.call(item) == "[object Object]") {
              widgetJson.value.setup[item.name] = item.value;
            } else if (
              Object.prototype.toString.call(item) == "[object Array]"
            ) {
              for (let j = 0; j < item.length; j++) {
                const list = item[j].list;
                list.forEach((el) => {
                  widgetJson.value.setup[el.name] = el.value;
                });
              }
            }
          }
          // position
          for (let i = 0; i < widgetJson.options.position.length; i++) {
            const item = widgetJson.options.position[i];
            if (item.value) {
              widgetJson.value.position[item.name] = item.value;
            }
          }
          // data 处理
          if (widgetJson.options.data && widgetJson.options.data.length > 0) {
            for (let i = 0; i < widgetJson.options.data.length; i++) {
              const item = widgetJson.options.data[i];
              if (item.value) {
                widgetJson.value.data[item.name] = item.value;
              }
            }
          }
        }
      }
      widgetJson.x = widgetJson.value.position.x;
      widgetJson.y = widgetJson.value.position.y;
      widgetJson.w = widgetJson.value.position.w;
      widgetJson.h = widgetJson.value.position.h;
      widgetJson.i = ``;
      return widgetJson;
    },
    layerClick(index) {
      console.log(index)
      this.widgetIndex = index;
      this.widgetOptions = this.deepClone(this.widgets[index]["options"]);
      if(this.widgets[index].type == 'widget-title'){
        this.linkShow = true
      }else {
        this.linkShow = false
      }
    },
    linkaeListChanged(data) {
      this.widgets[data.index].value.setup.linkagelist = data.list;
    },
    // 将当前选中的组件，右侧属性值更新
    widgetValueChanged(key, val) {
      if (this.screenCode == "screen") {
        this.dashboard.components = this.deepClone(val);
        this.setDefaultValue(this.dashboardoptions[key], val);
      } else {
        for (let i = 0; i < this.widgets.length; i++) {
          if (this.widgetIndex == i) {
            this.widgets[i].value[key] = this.deepClone(val);
            this.setDefaultValue(this.widgets[i].options[key], val);
            if(key== `position`) {
              this.widgets[i].x = this.deepClone(val.x);
              this.widgets[i].y = this.deepClone(val.y);
              this.widgets[i].w = this.deepClone(val.w);
              this.widgets[i].h = this.deepClone(val.h);
            }
          }
        }
      }
    },
    rightClick(event, index) {
      this.rightClickIndex = index;
      const left = event.clientX;
      const top = event.clientY;
      if (left || top) {
        this.styleObj = {
          left: left + "px",
          top: top + "px",
          display: "block",
        };
      }
      this.visibleContentMenu = true;
      return false;
    },
    setDefaultValue(options, val) {
      for (let i = 0; i < options.length; i++) {
        if (Object.prototype.toString.call(options[i]) == "[object Object]") {
          for (const k in val) {
            if (options[i].name == k) {
              options[i].value = val[k];
            }
          }
        } else if (
          Object.prototype.toString.call(options[i]) == "[object Array]"
        ) {
          for (let j = 0; j < options[i].length; j++) {
            const list = options[i][j].list;
            for (let z = 0; z < list.length; z++) {
              for (const k in val) {
                if (list[z].name == k) {
                  list[z].value = val[k];
                }
              }
            }
          }
        }
      }
    },
    datadragEnd(evt) {
      evt.preventDefault();
      this.widgets = this.swapArr(this.widgets, evt.oldIndex, evt.newIndex);
    },
  },
};
</script>

<style scoped lang="scss">
.mr10 {
  margin-right: 10px;
}

.ml20 {
  margin-left: 20px;
}

.border-right {
  border-right: 1px solid #273b4d;
}

.border-left {
  border-left: 1px solid #273b4d;
}

.el-icon-arrow-down {
  font-size: 10px;
}

.is-active {
  background: #31455d !important;
  color: #bfcbd9 !important;
}

.layout {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;

  .layout-left {
    display: inline-block;
    height: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border: 0px;
    background-color: #263445;

    //工具栏一个元素
    .tools-item {
      display: flex;
      position: relative;
      width: 100%;
      height: 48px;
      align-items: center;
      -webkit-box-align: center;
      padding: 0 6px;
      cursor: pointer;
      font-size: 12px;
      margin-bottom: 1px;

      .tools-item-icon {
        color: #409eff;
        margin-right: 10px;
        width: 30px !important;
        height: 30px;
        line-height: 30px;
        text-align: center;
        display: block;
        border: 1px solid #3a4659;
        background: #282a30;
      }

    }
  }

  .layout-left-fold {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 100%;

    font-size: 12px;
    overflow: hidden;
    background-color: #242a30;
    cursor: pointer;
    padding-top: 26%;

    i {
      font-size: 18px;
      width: 18px;
      height: 23px;
      margin-left: 0px;
      color: #bfcbd9;
    }
  }

  .layout-middle {
    // display: flex;
    position: relative;
    //width: calc(100% - 445px);
    height: 100%;
    background-color: rgb(36, 42, 48);
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border: 1px solid rgb(36, 42, 48);
    align-items: center;
    vertical-align: middle;
    // text-align: center;
    background: url(../../../../assets/images/bg.png) repeat;

    .top-button {
      display: flex;
      flex-direction: row;
      height: 40px;
      line-height: 40px;
      background-color: #242a30;

      .btn {
        color: #788994;
        width: 55px;
        text-align: center;
        display: block;
        cursor: pointer;

        .el-icon-arrow-down {
          transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          /* IE 9 */
          -moz-transform: rotate(0deg);
          /* Firefox */
          -webkit-transform: rotate(0deg);
          /* Safari 和 Chrome */
          -o-transform: rotate(0deg);
          /* Opera */
          transition: all 0.4s ease-in-out;
        }

        &:hover {
          background: rgb(25, 29, 34);

          .el-icon-arrow-down {
            transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            /* IE 9 */
            -moz-transform: rotate(180deg);
            /* Firefox */
            -webkit-transform: rotate(180deg);
            /* Safari 和 Chrome */
            -o-transform: rotate(180deg);
            /* Opera */
            transition: all 0.4s ease-in-out;
          }
        }
      }
    }

    .workbench-container {
      position: relative;
      -webkit-transform-origin: 0 0;
      transform-origin: 0 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      top: 20px;
      overflow-y: auto;


      .vueRuler {
        width: 100%;
        padding: 18px 0px 0px 18px;
      }

      .workbench {
        background-color: #1e1e1e;
        position: relative;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        margin: 0;
        padding: 0;
        box-shadow: 6px 6px 30px rgba(0, 0, 0, 0.4) !important;
      }

      .bg-grid {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: 30px 30px, 30px 30px;
        background-image: linear-gradient(
            hsla(0, 0%, 100%, 0.1) 1px,
            transparent 0
          ),
          linear-gradient(90deg, hsla(0, 0%, 100%, 0.1) 1px, transparent 0);
        // z-index: 2;
      }
    }

    .bottom-text {
      width: 100%;
      color: #a0a0a0;
      font-size: 16px;
      position: absolute;
      bottom: 20px;
    }
  }

  .layout-right {
    display: inline-block;
    height: 100%;
  }

 ::v-deep .el-tabs--border-card {
    border: 0;

    .el-tabs__header {
      .el-tabs__nav {
        .el-tabs__item {
          background-color: #242f3b;
          border: 0px;
        }

        .el-tabs__item.is-active {
          background-color: #31455d;
        }
      }
    }

    .el-tabs__content {
      background-color: #242a30;
      height: calc(100vh - 39px);
      overflow-x: hidden;
      overflow-y: auto;

      .el-tab-pane {
        color: #bfcbd9;
      }

      &::-webkit-scrollbar {
        width: 5px;
        height: 14px;
      }

      &::-webkit-scrollbar-track,
      &::-webkit-scrollbar-thumb {
        border-radius: 1px;
        border: 0 solid transparent;
      }

      &::-webkit-scrollbar-track-piece {
        /*修改滚动条的背景和圆角*/
        background: #29405c;
        -webkit-border-radius: 7px;
      }

      &::-webkit-scrollbar-track {
        box-shadow: 1px 1px 5px rgba(116, 148, 170, 0.5) inset;
      }

      &::-webkit-scrollbar-thumb {
        min-height: 20px;
        background-clip: content-box;
        box-shadow: 0 0 0 5px rgba(116, 148, 170, 0.5) inset;
      }

      &::-webkit-scrollbar-corner {
        background: transparent;
      }

      /*修改垂直滚动条的样式*/
      &::-webkit-scrollbar-thumb:vertical {
        background-color: #00113a;
        -webkit-border-radius: 7px;
      }

      /*修改水平滚动条的样式*/
      &::-webkit-scrollbar-thumb:horizontal {
        background-color: #00113a;
        -webkit-border-radius: 7px;
      }
    }
  }
}

ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav {
  width: 40px;
  padding: 0;
  list-style: none;
  /* overflow: hidden; */
}

.nav {
  zoom: 1;
}

.nav:before,
.nav:after {
  content: "";
  display: table;
}

.nav:after {
  clear: both;
}

.nav li {
  width: 55px;
  text-align: center;
  position: relative;
}

.nav li a {
  float: left;
  padding: 12px 30px;
  color: #999;
  font: bold 12px;
  text-decoration: none;
}

.nav li:hover {
  color: #788994;
}

.nav li ul {
  visibility: hidden;
  position: absolute;
  z-index: 1000;
  list-style: none;
  top: 38px;
  left: 0;
  padding: 0;
  background-color: rgb(36, 42, 48);
  opacity: 0;
  _margin: 0;
  width: 120px;
  transition: all 0.2s ease-in-out;
}

.nav li:hover > ul {
  opacity: 1;
  visibility: visible;
  margin: 0;

  li:hover {
    background-color: rgb(25, 29, 34);
  }
}

.nav ul li {
  float: left;
  display: block;
  border: 0;
  width: 100%;
  font-size: 12px;
}

.nav ul a {
  padding: 10px;
  width: 100%;
  display: block;
  float: none;
  height: 120px;
  border: 1px solid #30445c;
  background-color: rgb(25, 29, 34);
  transition: all 0.2s ease-in-out;
}

.nav ul a:hover {
  border: 1px solid #3c5e88;
}

.nav ul li:first-child > a:hover:before {
  border-bottom-color: #04acec;
}

.nav ul ul {
  top: 0;
  left: 120px;
  width: 400px;
  height: 300px;
  overflow: auto;
  padding: 10px;
  _margin: 0;
}

.nav ul ul li {
  width: 120px;
  height: 120px;
  margin-right: 3px;
  display: block;
  float: left;
}

::v-deep .vue-ruler-h {
  opacity: 0.2;
}

::v-deep .vue-ruler-v {
  opacity: 0.2;
}

.icon-save {
  font-size: 22px;
}

::v-deep .el-tab-pane {
  padding-bottom: 20px;
}

::v-deep .dashboard_title {
  min-width: 250px;
  height: 28px;
  line-height: 28px;
  padding: 0px 10px;
  background-color: rgba(0, 0, 0, 0.3) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  position: relative;
  top: 5px;
  left: 30%;
  font-size: 14px;
  text-align: center;
}
.gridlayout{
  background-color: cornsilk;
}
.griditem{
  background-color: cyan;
}
.el-button-h {
  height: 40px;
  line-height: 40px;
  background: transparent;
  color: #bcc9d4;
  font-weight: 300;
  font-size: 14px;
  border-color: #282e3a;
  width: 100%;
  padding: 0;
  margin: 0;
  border-left: 0;
  border-right: 0;
  border-top: 0;
}
.el-collapse-item__arrow {
  float: right;
  line-height: 40px;
  font-size: 14px;
  margin: 0;
}
.el-collapse-item__icon {
  color: #409eff;
  margin-right: 10px;
  padding: 6px 18px 6px 18px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 1px solid #3a4659;
  background: #282a30;
}
::v-deep .el-tabs--border-card>.el-tabs__content {
  padding: 10px 10px 0px 10px;
}
.el-collapse-item__tit {
  float: left;
}
::v-deep .el-tabs__nav{
  width: 55px;
}
::v-deep .el-tabs__nav-wrap::after {
  background-color: #242a30;
}
::v-deep .el-tabs__item > span {
  color: #409eff;
  text-align: center !important;
  font-size: 16px;
}
.tools-item-text {
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
  width: 90px;
}
.layout .layout-left .tools-item{
  padding: 0;
}


</style>
