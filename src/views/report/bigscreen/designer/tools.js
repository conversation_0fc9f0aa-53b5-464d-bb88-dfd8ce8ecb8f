const screenConfig = {
  code: 'screen',
  type: 'screen',
  label: '大屏设置',
  icon: '',
  options: {
    setup: [
      {
        type: 'el-input-number',
        label: '大屏宽度',
        name: 'width',
        required: false,
        placeholder: 'px',
        value: '1920',
      },
      {
        type: 'el-input-number',
        label: '大屏高度',
        name: 'height',
        required: false,
        placeholder: 'px',
        value: '1080',
      },
      {
        type: 'el-input-text',
        label: '标题',
        name: 'title',
        require: false,
        placeholder: '',
        value: '大屏',
      },
      {
        type: 'el-input-textarea',
        label: '大屏简介',
        name: 'description',
        required: false,
        placeholder: '',
      },
      {
        type: 'vue-color',
        label: '背景颜色',
        name: 'backgroundColor',
        required: false,
        placeholder: '',
        value: '#000',
      },
      {
        type: 'custom-upload',
        label: '图片地址',
        name: 'backgroundImage',
        required: false,
        placeholder: '',
        value: '',
      },
    ],
    data: [],
    position: [],
  },
}

const widgetTools = [

  // type=html类型的组件
  {
    code: 'widget-text',
    type: 'html',
    label: '文本',
    icon: 'iconziyuan',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '文本框',
        },
        {
          type: 'el-input-number',
          label: '显示时间',
          name: 'startTime',
          required: false,
          placeholder: '',
          value: 1
        },
        {
          type: 'el-select',
          label: '显示效果',
          name: 'transition',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'fade', name: '淡入'},
            {code: 'el-zoom-in-center', name: '上到下'},
            {code: 'el-zoom-in-top', name: '中间展开'},
          ],
          value: 'fade'
        },
        {
          type: 'el-input-number',
          label: '过渡时间',
          name: 'transitionTime',
          required: false,
          placeholder: '',
          value: 1
        },
        [
          {
            name: '动态文本设置',
            list: [
              {
                type: 'el-switch',
                label: '动态文本显示',
                name: 'dynamicShow',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-textarea',
                label: '文本内容',
                name: 'dynamictext',
                required: false,
                placeholder: '',
                value: '文本框',
              },
              {
                type: 'el-select',
                label: '选择字体',
                name: 'dynamicfontFamily',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: '黑体', name: '黑体'},
                  {code: 'Microsoft YaHei', name: '微软雅黑'},
                  {code: 'PingFang SC', name: '苹方字体'},
                  {code: '宋体', name: '宋体'},
                  {code: '朗倩体', name: '朗倩体'},
                  {code: 'DIN', name: 'DIN数字体'},
                  {code: 'DIN-Light', name: 'DIN数字体-细'},
                  {code: 'BEBAS', name: 'BEBAS数字体'}
                ],
                value: '微软雅黑'
              },
              {
                type: 'el-input-number',
                label: '文本字号',
                name: 'dynamicfontSize',
                required: false,
                placeholder: '',
                value: '14',
              },
              {
                type: 'el-input-number',
                label: '文本行高',
                name: 'dynamiclineHeight',
                required: false,
                placeholder: '',
                value: '18',
              },
              {
                type: 'el-input-number',
                label: '文本上边距',
                name: 'dynamiclineTop',
                required: false,
                placeholder: '',
                value: '10',
              },
              {
                type: 'vue-color',
                label: '文本颜色',
                name: 'dynamiccolor',
                required: false,
                placeholder: '',
                value: 'rgba(196, 171, 40, 1)',
              },
              {
                type: 'el-input-number',
                label: '文本间距',
                name: 'dynamicletterSpacing',
                required: false,
                placeholder: '',
                value: '0',
              },
              {
                type: 'el-select',
                label: '文本粗细',
                name: 'dynamicfontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-select',
                label: '对齐方式',
                name: 'dynamictextAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
            ],
          },
          {
            name: '静态文本设置',
            list: [
              {
                type: 'el-switch',
                label: '静态文本显示',
                name: 'staticShow',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-textarea',
                label: '文本内容',
                name: 'statictext',
                required: false,
                placeholder: '',
                value: '文本框',
              },
              {
                type: 'el-select',
                label: '选择字体',
                name: 'staticfontFamily',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: '黑体', name: '黑体'},
                  {code: 'Microsoft YaHei', name: '微软雅黑'},
                  {code: 'PingFang SC', name: '苹方字体'},
                  {code: '宋体', name: '宋体'},
                  {code: '朗倩体', name: '朗倩体'},
                  {code: 'DIN', name: 'DIN数字体'},
                  {code: 'DIN-Light', name: 'DIN数字体-细'},
                  {code: 'BEBAS', name: 'BEBAS数字体'}
                ],
                value: '微软雅黑'
              },
              {
                type: 'el-input-number',
                label: '文本字号',
                name: 'staticfontSize',
                required: false,
                placeholder: '',
                value: '14',
              },
              {
                type: 'el-input-number',
                label: '文本行高',
                name: 'staticlineHeight',
                required: false,
                placeholder: '',
                value: '10',
              },
              {
                type: 'el-input-number',
                label: '文本上边距',
                name: 'staticlineTop',
                required: false,
                placeholder: '',
                value: '18',
              },
              {
                type: 'vue-color',
                label: '文本颜色',
                name: 'staticcolor',
                required: false,
                placeholder: '',
                value: 'rgba(196, 171, 40, 1)',
              },
              {
                type: 'el-input-number',
                label: '文本间距',
                name: 'staticletterSpacing',
                required: false,
                placeholder: '',
                value: '0',
              },
              {
                type: 'el-select',
                label: '文本粗细',
                name: 'staticfontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-select',
                label: '对齐方式',
                name: 'statictextAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '文本框',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-text',
          value: {},
        }
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 100,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 40
        },
      ],
    },
  },
  {
    code: 'widget-details',
    type: 'html',
    label: '详情',
    icon: 'iconziyuan',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '详情页',
        },
        {
          type: 'el-input-number',
          label: '文本行高',
          name: 'lineHeight',
          required: false,
          placeholder: '',
          value: '40',
        },
        {
          type: 'el-input-number',
          label: '文本间距',
          name: 'letterSpacing',
          required: false,
          placeholder: '',
          value: '0',
        },
        {
          type: 'el-input-number',
          label: '边框粗细',
          name: 'textBorder',
          required: false,
          placeholder: '',
          value: '0',
        },
        {
          type: 'vue-color',
          label: '边框颜色',
          name: 'textBorderColor',
          required: false,
          placeholder: '',
          value: 'rgba(255,255,255,.5)',
        },
        {
          type: 'el-input-number',
          label: '边框圆角',
          name: 'textBorderRadius',
          required: false,
          placeholder: '',
          value: '0',
        },
        {
          type: 'el-input-number',
          label: '文本字号',
          name: 'nameSize',
          required: false,
          placeholder: '',
          value: '35',
        },
        {
          type: 'vue-color',
          label: '文本颜色',
          name: 'nameColor',
          required: false,
          placeholder: '',
          value: 'rgba(8, 112, 214, 1)',
        },
        {
          type: 'el-select',
          label: '文本粗细',
          name: 'nameWeight',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'normal', name: '正常'},
            {code: 'bold', name: '粗体'},
            {code: 'bolder', name: '特粗体'},
            {code: 'lighter', name: '细体'}
          ],
          value: 'normal'
        },
        {
          type: 'el-select',
          label: '选择字体',
          name: 'nameFamily',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: '黑体', name: '黑体'},
            {code: 'Microsoft YaHei', name: '微软雅黑'},
            {code: 'PingFang SC', name: '苹方字体'},
            {code: '宋体', name: '宋体'},
            {code: '朗倩体', name: '朗倩体'},
            {code: 'DIN', name: 'DIN数字体'},
            {code: 'DIN-Light', name: 'DIN数字体-细'},
            {code: 'BEBAS', name: 'BEBAS数字体'}
          ],
          value: '黑体'
        },
        {
          type: 'el-select',
          label: '对齐方式',
          name: 'nameAlign',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'center', name: '居中'},
            {code: 'left', name: '左对齐'},
            {code: 'right', name: '右对齐'},
          ],
          value: 'left'
        },
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[{"姓名": "张三", "性别": "女", "学籍状态": "在籍", "院系": "传媒学院", "班级": "20英文","专业": "英文系","年级": "2020级", "学号": "180521521", "政治面貌": "团员", "出生日期": "2003.03.23", "民族": "汉族", "籍贯": "河北石家庄", "身份证号": "130128200303023456"}]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-details',
          value: {},
        }
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 800,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 300,
        },
      ],
    },
  },
  {
    code: 'widget-turncard',
    type: 'html',
    label: '翻牌器',
    icon: 'iconziyuan',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '数字翻牌器',
        },
        {
          type: 'el-input-text',
          label: '单位名称',
          name: 'unitName',
          required: false,
          placeholder: '',
          value: '个',
        },
        {
          type: 'el-select',
          label: '选择字体',
          name: 'fontFamily',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: '黑体', name: '黑体'},
            {code: 'Microsoft YaHei', name: '微软雅黑'},
            {code: 'PingFang SC', name: '苹方字体'},
            {code: '宋体', name: '宋体'},
            {code: '朗倩体', name: '朗倩体'},
            {code: 'DIN', name: 'DIN数字体'},
            {code: 'DIN-Light', name: 'DIN数字体-细'},
            {code: 'BEBAS', name: 'BEBAS数字体'}
          ],
          value: '黑体'
        },
        {
          type: 'el-input-number',
          label: '文本字号',
          name: 'fontSize',
          required: false,
          placeholder: '',
          value: '26',
        },
        {
          type: 'vue-color',
          label: '文本颜色',
          name: 'color',
          required: false,
          placeholder: '',
          value: '#FAD400',
        },
        {
          type: 'el-select',
          label: '文本粗细',
          name: 'fontWeight',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'normal', name: '正常'},
            {code: 'bold', name: '粗体'},
            {code: 'bolder', name: '特粗体'},
            {code: 'lighter', name: '细体'}
          ],
          value: 'normal'
        },
        // {
        //   type: 'el-input-number',
        //   label: '动画时长',
        //   name: 'animationTime',
        //   required: false,
        //   placeholder: '',
        //   value: '500',
        // },
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: 999,
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-text',
          value: {},
        }
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 200,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 100
        },
      ],
    },
  },
  {
    code: 'widget-border',
    type: 'html',
    label: '边框',
    icon: 'iconkuangjia',
    options: {
      // 配置
      setup: [
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: '',
        },
        {
          type: 'el-select',
          label: '边框样式',
          name: 'borderStyle',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'dv-border-box-1', name: '边框1'},
            {code: 'dv-border-box-2', name: '边框2'},
            {code: 'dv-border-box-3', name: '边框3'},
            {code: 'dv-border-box-4', name: '边框4'},
            {code: 'dv-border-box-5', name: '边框5'},
            {code: 'dv-border-box-6', name: '边框6'},
            {code: 'dv-border-box-7', name: '边框7'},
            {code: 'dv-border-box-8', name: '边框8'},
            {code: 'dv-border-box-9', name: '边框9'},
            {code: 'dv-border-box-10', name: '边框10'},
            {code: 'dv-border-box-11', name: '边框11'},
            {code: 'dv-border-box-12', name: '边框12'},
            {code: 'dv-border-box-13', name: '边框13'}
          ],
          value: 'dv-border-box-1'
        },
        {
          type: 'el-switch',
          label: '是否翻转',
          name: 'isReverse',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'vue-color',
          label: '边框背景颜色',
          name: 'borderBackground',
          required: false,
          placeholder: '',
          value: 'rgba(0,0,0,0)',
        },
        {
          type: 'vue-color',
          label: '边框主颜色',
          name: 'mainColor',
          required: false,
          placeholder: '',
          value: 'rgb(71,189,211)',
        },
        {
          type: 'vue-color',
          label: '边框副颜色',
          name: 'lessColor',
          required: false,
          placeholder: '',
          value: 'rgb(40,44,52)',
        },
      ],


      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200
        },
      ],
    },
  },
  {
    code: 'widget-decorate',
    type: 'html',
    label: '装饰',
    icon: 'icongexingzhuangban',
    options: {
      // 配置
      setup: [
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: '',
        },
        {
          type: 'el-select',
          label: '边框样式',
          name: 'borderStyle',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'dv-decoration-1', name: '装饰1'},
            {code: 'dv-decoration-2', name: '装饰2'},
            {code: 'dv-decoration-3', name: '装饰3'},
            {code: 'dv-decoration-4', name: '装饰4'},
            {code: 'dv-decoration-5', name: '装饰5'},
            {code: 'dv-decoration-6', name: '装饰6'},
            {code: 'dv-decoration-7', name: '装饰7'},
            {code: 'dv-decoration-8', name: '装饰8'},
            {code: 'dv-decoration-9', name: '装饰9'},
            {code: 'dv-decoration-10', name: '装饰10'},
            {code: 'dv-decoration-11', name: '装饰11'},
            {code: 'dv-decoration-12', name: '装饰12'},
          ],
          value: 'dv-decoration-1'
        },
        {
          type: 'vue-color',
          label: '装饰主颜色',
          name: 'mainColor',
          required: false,
          placeholder: '',
          value: 'rgb(71,189,211)',
        },
        {
          type: 'vue-color',
          label: '装饰副颜色',
          name: 'lessColor',
          required: false,
          placeholder: '',
          value: 'rgb(40,44,52)',
        },
        {
          type: 'el-switch',
          label: '是否翻转',
          name: 'isReverse',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'el-input-number',
          label: '动画时长',
          name: 'animation',
          required: false,
          placeholder: 'px',
          value: 3,
        },
      ],


      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 200,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 100
        },
      ],
    },
  },
  {
    code: 'widget-water',
    type: 'html',
    label: '水位图',
    icon: 'iconfenxiang2',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-number',
          label: '水位数值',
          name: 'Number',
          required: false,
          placeholder: '',
          value: 66,
        },
        {
          type: 'el-input-number',
          label: '波浪数',
          name: 'waveNum',
          required: false,
          placeholder: '',
          value: 1,
        },
        {
          type: 'el-select',
          label: '水位图形状',
          name: 'shape',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'rect', name: '矩形'},
            {code: 'roundRect', name: '圆角矩形'},
            {code: 'round', name: '圆形'},
          ],
          value: 'round'
        },
        {
          type: 'vue-color',
          label: '水位图配色1',
          name: 'colors1',
          required: false,
          placeholder: '',
          value: '#00BAFF',
        },
        {
          type: 'vue-color',
          label: '水位图配色2',
          name: 'colors2',
          required: false,
          placeholder: '',
          value: '#3DE7C9',
        },
        {
          type: 'el-input-number',
          label: '波浪高度',
          name: 'waveHeight',
          required: false,
          placeholder: '',
          value: 40,
        },
        {
          type: 'el-input-number',
          label: '字体大小',
          name: 'fontSize',
          required: false,
          placeholder: '',
          value: '26',
        },
      ],
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-text',
          value: {},
        }
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 500,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 500
        },
      ],
    },
  },
  {
    code: 'widget-percent',
    type: 'html',
    label: '进度池',
    icon: 'iconfenxiang2',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-number',
          label: '进度数值',
          name: 'Number',
          required: false,
          placeholder: '',
          value: 66,
        },
        // [{
        //   name: '自定义配色',
        //   list: [
        //     {
        //       type: 'customColor',
        //       label: '',
        //       name: 'customColor',
        //       required: false,
        //       value: [{color: '#016DF3'}, {color: '#1ED2F7'}],
        //     },
        //   ],
        // },],
        {
          type: 'vue-color',
          label: '文字配色',
          name: 'fontColors',
          required: false,
          placeholder: '',
          value: 'rgb(255,255,255)',
        },
        {
          type: 'vue-color',
          label: '进度池配色',
          name: 'Colors',
          required: false,
          placeholder: '',
          // value: 'rgb(71,189,211)',
          value: `rgb(71,189,211)`,

        },
        {
          type: 'vue-color',
          label: '进度池配色',
          name: 'Colors2',
          required: false,
          placeholder: '',
          // value: 'rgb(71,189,211)',
          value: `rgb(255,255,255)`,

        },
        [
          {
            name: '边框设置',
            list: [
              {
                type: 'el-input-number',
                label: '边框宽度',
                name: 'borderWidth',
                required: false,
                placeholder: '',
                value: 3
              },
              {
                type: 'el-input-number',
                label: '边框间隙',
                name: 'borderGap',
                required: false,
                placeholder: '',
                value: 3
              },
              {
                type: 'el-input-number',
                label: '边框半径',
                name: 'borderRadius',
                required: false,
                placeholder: '',
                value: 5
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000,
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '文本框',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-text',
          value: {},
        }
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],

    },
  },
  {
    code: 'widget-block',
    type: 'html',
    label: '背景块',
    icon: 'iconkuangjia',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '背景块',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: 'rgba(115,170,229,1)',
        },
        {
          type: 'vue-color',
          label: '背景图片',
          name: 'backgroundImage',
          required: false,
          placeholder: '',
          value: 'rgba(115,170,229,1)',
        },
        {
          type: 'el-input-number',
          label: '边框粗细',
          name: 'textBorder',
          required: false,
          placeholder: '',
          value: '0',
        },
        {
          type: 'vue-color',
          label: '边框颜色',
          name: 'textBorderColor',
          required: false,
          placeholder: '',
          value: 'rgba(255,255,255,.5)',
        },
        {
          type: 'el-input-number',
          label: '边框圆角',
          name: 'textBorderRadius',
          required: false,
          placeholder: '',
          value: '0',
        },
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 100,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 40
        },
      ],
    },
  },
  {
    code: 'widget-marquee',
    type: 'html',
    label: '滚动文本',
    icon: 'iconhengxiangwenzi',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '跑马灯',
        },
        {
          type: 'el-input-text',
          label: '文本内容',
          name: 'text',
          required: false,
          placeholder: '',
          value: '滚动文本',
        },
        {
          type: 'el-input-number',
          label: '字体大小',
          name: 'fontSize',
          required: false,
          placeholder: '',
          value: '26',
        },
        {
          type: 'vue-color',
          label: '字体颜色',
          name: 'color',
          required: false,
          placeholder: '',
          value: '#FAD400',
        },
        {
          type: 'el-input-number',
          label: '字体间距',
          name: 'letterSpacing',
          required: false,
          placeholder: '',
          value: '0',
        },
        {
          type: 'vue-color',
          label: '字体背景',
          name: 'background',
          required: false,
          placeholder: '',
          value: 'rgba(115,170,229,.5)',
        },
        {
          type: 'el-select',
          label: '文字粗细',
          name: 'fontWeight',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'normal', name: '正常'},
            {code: 'bold', name: '粗体'},
            {code: 'bolder', name: '特粗体'},
            {code: 'lighter', name: '细体'}
          ],
          value: 'normal'
        },
/*        {
          type: 'el-input-number',
          label: '滚动速度',
          name: 'jScrollPane',
          //required: false,
          placeholder: '',
          value: '50',
        }*/
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '文本框',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-text',
          value: {},
        }
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 100,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 40,
        },
      ],
    },
  },
  {
    code: 'widget-href',
    type: 'html',
    label: '超链接',
    icon: 'iconchaolianjie',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '超链接',
        },
        {
          type: 'el-input-text',
          label: '文本内容',
          name: 'text',
          required: false,
          placeholder: '',
          value: '超链接',
        },
        {
          type: 'el-input-number',
          label: '字体大小',
          name: 'fontSize',
          required: false,
          placeholder: '',
          value: '26',
        },
        {
          type: 'vue-color',
          label: '字体颜色',
          name: 'color',
          required: false,
          placeholder: '',
          value: '#FAD400',
        },
        {
          type: 'el-input-number',
          label: '字体间距',
          name: 'letterSpacing',
          required: false,
          placeholder: '',
          value: '0',
        },
        {
          type: 'vue-color',
          label: '字体背景',
          name: 'background',
          required: false,
          placeholder: '',
          value: 'rgba(115,170,229,.5)',
        },
        {
          type: 'el-select',
          label: '文字粗细',
          name: 'fontWeight',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'normal', name: '正常'},
            {code: 'bold', name: '粗体'},
            {code: 'bolder', name: '特粗体'},
            {code: 'lighter', name: '细体'}
          ],
          value: 'normal'
        },
        {
          type: 'el-select',
          label: '对齐方式',
          name: 'textAlign',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'center', name: '居中'},
            {code: 'left', name: '左对齐'},
            {code: 'right', name: '右对齐'},
          ],
          value: 'center'
        },
        {
          type: 'el-radio-group',
          label: '跳转方式',
          name: 'jumpMode',
          required: false,
          placeholder: '',
          selectOptions: [
            {
              code: 'self',
              name: '本窗口',
            },
            {
              code: 'other',
              name: '新窗口',
            },
          ],
          value: 'self',
        },
        {
          type: 'el-input-text',
          label: '超链地址',
          name: 'linkAdress',
          required: false,
          placeholder: '',
          value: 'http://www.baidu.com',
        },
      ],
      // 数据
      data: [],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 100,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 40,
        },
      ],
    },
  },
  {
    code: 'widget-time',
    type: 'html',
    label: '当前时间',
    icon: 'iconshijian',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '当前时间',
        },
        {
          type: 'el-select',
          label: '时间格式',
          name: 'timeFormat',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'yyyy-MM-dd', name: '日期'},
            {code: 'yyyy-MM-dd hh:mm', name: '日期+时分'},
            {code: 'yyyy-MM-dd hh:mm:ss', name: '日期+时分秒'},
            {code: 'MM-dd', name: '日期无年'},
            {code: 'hh:mm', name: '时分'},
            {code: 'hh:mm:ss', name: '时分秒'},
            {code: 'year-week', name: '日期+星期'},
            {code: 'year-h-m-week', name: '日期+时分+星期'},
            {code: 'year-h-m-s-week', name: '日期+时分秒+星期'},
            {code: 'week', name: '星期'}
          ],
          value: 'yyyy-MM-dd hh:mm:ss'
        },
        {
          type: 'el-input-number',
          label: '字体间距',
          name: 'letterSpacing',
          required: false,
          placeholder: '',
          value: '0'
        },
        {
          type: 'el-input-number',
          label: '字体大小',
          name: 'fontSize',
          required: false,
          placeholder: '',
          value: '26'
        },
        {
          type: 'vue-color',
          label: '字体颜色',
          name: 'color',
          required: false,
          placeholder: '',
          value: '#FAD400'
        },
        {
          type: 'vue-color',
          label: '字体背景',
          name: 'background',
          required: false,
          placeholder: '',
          value: 'rgba(115,170,229,.5)'
        },
        {
          type: 'el-select',
          label: '文字粗细',
          name: 'fontWeight',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'normal', name: '正常'},
            {code: 'bold', name: '粗体'},
            {code: 'bolder', name: '特粗体'},
            {code: 'lighter', name: '细体'}
          ],
          value: 'normal'
        },
        {
          type: 'el-select',
          label: '对齐方式',
          name: 'textAlign',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'center', name: '居中'},
            {code: 'left', name: '左对齐'},
            {code: 'right', name: '右对齐'},
          ],
          value: 'left'
        },
      ],
      // 数据
      data: [],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 300,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 100,
        },
      ],
    },
  },
  {
    code: 'widget-image',
    type: 'html',
    label: '图片',
    icon: 'icontupian',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '图片',
        },
        {
          type: 'el-switch',
          label: '开启动画',
          name: 'startRotate',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'el-input-number',
          label: '动画时间',
          name: 'animationTime',
          required: false,
          placeholder: '',
          value: 5
        },
        {
          type: 'el-select',
          label: '动画效果',
          name: 'animationType',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'startImg', name: '旋转'},
            {code: 'startImg2', name: '向上拉伸'},
            {code: 'startImg5', name: '左右拉伸'},
            {code: 'startImg3', name: '透明度'},
            {code: 'startImg4', name: '缩放'},
          ],
          value: 'startImg'
        },
        {
          type: 'el-slider',
          label: '透明度',
          name: 'transparency',
          required: false,
          placeholder: '',
          value: 100
        },
        {
          type: 'el-input-number',
          label: '显示时间',
          name: 'startTime',
          required: false,
          placeholder: '',
          value: 1
        },
        {
          type: 'el-select',
          label: '显示效果',
          name: 'transition',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'fade', name: '淡入'},
            {code: 'el-zoom-in-center', name: '上到下'},
            {code: 'el-zoom-in-top', name: '中间展开'},
          ],
          value: 'fade'
        },
        {
          type: 'el-input-number',
          label: '过渡时间',
          name: 'transitionTime',
          required: false,
          placeholder: '',
          value: 1
        },
        {
          type: 'el-input-number',
          label: '圆角',
          name: 'borderRadius',
          required: false,
          placeholder: '',
          value: '0'
        },
        {
          type: 'custom-upload',
          label: '图片地址',
          name: 'imageAdress',
          required: false,
          placeholder: '',
          value: 'http://www.sanyth.com/images/index/logo1.png',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
        },
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-barlinechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 300,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  // {
  //   code: 'widget-slider',
  //   type: 'html',
  //   label: '轮播图片',
  //   icon: 'slider',
  //   options: {
  //     // 配置
  //     setup: [
  //       {
  //         type: 'el-input-text',
  //         label: '图层名称',
  //         name: 'layerName',
  //         required: false,
  //         placeholder: '',
  //       },
  //       {
  //         type: 'el-switch',
  //         label: '隐藏图层',
  //         name: 'hideLayer',
  //         required: false,
  //         placeholder: '',
  //       },
  //       {
  //         type: 'el-select',
  //         label: '轮播方向',
  //         name: 'tabDirection',
  //         required: false,
  //         placeholder: '',
  //       },
  //       {
  //         type: 'el-select',
  //         label: '选择器',
  //         name: 'tabSelector',
  //         required: false,
  //         placeholder: '',
  //       },
  //       {
  //         type: 'el-input-number',
  //         label: '轮播时间',
  //         name: 'tabTime',
  //         required: false,
  //         placeholder: '',
  //       },
  //     ],
  //     // 数据
  //     data: [],
  //     // 坐标
  //     position: [
  //       {
  //         type: 'el-input-number',
  //         label: '左边距',
  //         name: 'left',
  //         required: true,
  //         placeholder: 'px',
  //       },
  //       {
  //         type: 'el-input-number',
  //         label: '上边距',
  //         name: 'top',
  //         required: true,
  //         placeholder: 'px',
  //       },
  //       {
  //         type: 'el-input-number',
  //         label: '宽度',
  //         name: 'width',
  //         required: true,
  //         placeholder: '该容器在1920px大屏中的宽度',
  //       },
  //       {
  //         type: 'el-input-number',
  //         label: '高度',
  //         name: 'height',
  //         required: true,
  //         placeholder: '该容器在1080px大屏中的高度',
  //       },
  //     ],
  //   },
  // },
  {
    code: 'widget-video',
    type: 'html',
    label: '视频',
    icon: 'iconshipin',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: 'video',
        },
        {
          type: 'el-input-text',
          label: '地址',
          name: 'videoAdress',
          required: false,
          placeholder: '',
          value: 'https://www.w3school.com.cn//i/movie.ogg',
        },
      ],
      // 数据
      data: [],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 300,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-table',
    type: 'html',
    label: '表格',
    icon: 'iconbiaoge',
    options: {
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '表格',
        },
        {
          type: 'el-select',
          label: '字体位置',
          name: 'textAlign',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'center', name: '居中'},
            {code: 'left', name: '左对齐'},
            {code: 'right', name: '右对齐'},
          ],
          value: 'center'
        },
        {
          type: 'el-input-number',
          label: '字体大小',
          name: 'fontSize',
          required: false,
          placeholder: '',
          value: '16'
        },
        {
          type: 'el-switch',
          label: '开启滚动',
          name: 'isRoll',
          required: false,
          placeholder: '',
          value: true
        },
        {
          type: 'el-input-number',
          label: '滚动时间(毫秒)',
          name: 'rollTime',
          required: false,
          placeholder: '',
          value: 1000
        },
        {
          type: 'el-input-number',
          label: '滚动个数',
          name: 'rollNumber',
          required: false,
          placeholder: '',
          value: 5
        },
		{
		  type: 'el-input-number',
		  label: '显示条数',
		  name: 'rollVis',
		  required: false,
		  placeholder: '',
		  value: 5
		},
        {
          type: 'el-switch',
          label: '线条',
          name: 'isLine',
          required: false,
          placeholder: '',
          value: false
        },
        {
          type: 'el-input-number',
          label: '边框宽度',
          name: 'borderWidth',
          required: false,
          placeholder: '',
          value: 1
        },
        {
          type: 'vue-color',
          label: '边框颜色',
          name: 'borderColor',
          required: false,
          placeholder: '',
          value: '#fff'
        },
        [
          {
            name: '表头设置',
            list: [
              {
                type: 'el-switch',
                label: '表头显隐',
                name: 'isHeader',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '表头颜色',
                name: 'headColor',
                require: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'vue-color',
                label: '表头背景',
                name: 'headBackColor',
                require: false,
                placeholder: '',
                value: '#0a73ff',
              },
			  {
			    type: 'el-input-number',
			    label: '表头高度',
			    name: 'headHeight',
			    required: false,
			    placeholder: '',
			    value: 40
			  }
            ],
          },
          {
            name: '表体设置',
            list: [
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'bodyColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
			  {
			    type: 'el-select',
			    label: '文本对齐',
			    name: 'textAlign',
			    required: false,
			    placeholder: '',
			    selectOptions: [
			      {code: 'center', name: '居中'},
			      {code: 'left', name: '左对齐'},
			      {code: 'right', name: '右对齐'},
			    ],
			    value: 'center'
			  },
              {
                type: 'vue-color',
                label: '表格背景',
                name: 'tableBgColor',
                require: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '奇行颜色',
                name: 'oldColor',
                require: false,
                placeholder: '',
                value: '#0a2732',
              },
              {
                type: 'vue-color',
                label: '偶行颜色',
                name: 'eventColor',
                required: false,
                placeholder: '',
                value: '#003b51'
              },
			  {
			    type: 'el-input-number',
			    label: '表体行高',
			    name: 'bodyHeight',
			    required: false,
			    placeholder: '',
			    value: 40
			  }
            ],
          },
        ],
        {
          type: 'dynamic-add-table',
          label: '',
          name: 'dynamicAddTable',
          required: false,
          placeholder: '',
          value: [{name: '日期', key: 'date', width: 200}, {name: '姓名', key: 'name', width: 200}, {
            name: '地址',
            key: 'address',
            width: '200'
          }]
        }
      ],
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: [
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
            {date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄'},
          ],
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-table',
          value: {},
        },
      ],
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 600,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 300,
        },
      ]
    }
  },
  {
    code: 'widget-iframe',
    type: 'html',
    label: '内联框架',
    icon: 'iconkuangjia',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: 'iframe',
        },
        {
          type: 'el-input-text',
          label: '地址',
          name: 'iframeAdress',
          required: false,
          placeholder: '',
          value: 'https://report.anji-plus.com/index.html',
        },
      ],
      // 数据
      data: [],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 300,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  // {
  //   code: 'widget-universal',
  //   type: 'html',
  //   label: '全能组件',
  //   icon: 'univresal',
  // },
  // type=chart类型的组件
  {
    code: 'widget-barchart',
    type: 'chart',
    label: '柱形图',
    icon: 'iconzhuzhuangtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '柱状图',
        },
        {
          type: 'el-switch',
          label: '竖展示',
          name: 'verticalShow',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '柱体设置',
            list: [
              {
                type: 'el-slider',
                label: '最大宽度',
                name: 'maxWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-slider',
                label: '圆角',
                name: 'radius',
                require: false,
                placeholder: '',
                value: 0,
              },
              // {
              //   type: 'el-slider',
              //   label: '最小高度',
              //   name: 'minHeight',
              //   require: false,
              //   placeholder: '',
              //   value: 0,
              // },
            ],
          },
          {
            name: '滚动条',
            list: [
              {
                type: 'el-switch',
                label: '是否开启',
                name: 'isRoll',
                required: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '开始位置',
                name: 'minRoll',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-input-number',
                label: '结束位置',
                name: 'maxRoll',
                required: false,
                placeholder: '',
                value: 80
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: 'rgba(30, 144, 255, 1)'
              },
              {
                type: 'el-input-text',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'xNameColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'xNameFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngle',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: '#fff',
              },{
                type: 'el-input-number',
                label: '间隔个数',
                name: 'xSplitNumber',
                required: false,
                placeholder: '',
                value: 2
              },
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'NameColorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'NameFontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              }, {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: '#fff',

              }, {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 2
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-text',
                label: '拼接符号',
                name: 'format',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-select',
                label: '显示位置',
                name: 'barNumberPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'right', name: '右侧'},
                ],
                value: 'top'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              }, {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              }, {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              }, {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          // {
          //   name: '图例操作',
          //   list: [
          //     {
          //       type: 'el-switch',
          //       label: '图例',
          //       name: 'isShowLegend',
          //       required: false,
          //       placeholder: '',
          //       value: true,
          //     },
          //     {
          //       type: 'vue-color',
          //       label: '字体颜色',
          //       name: 'lengedColor',
          //       required: false,
          //       placeholder: '',
          //       value: '#fff',
          //     },
          //     {
          //       type: 'el-input-number',
          //       label: '字体大小',
          //       name: 'lengedFontSize',
          //       required: false,
          //       placeholder: '',
          //       value: 16,
          //     },
          //     {
          //       type: 'el-input-number',
          //       label: '图例宽度',
          //       name: 'lengedWidth',
          //       required: false,
          //       placeholder: '',
          //       value: 15,
          //     },
          //     {
          //       type: 'el-select',
          //       label: '横向位置',
          //       name: 'lateralPosition',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'left', name: '左对齐'},
          //         {code: 'center', name: '居中'},
          //         {code: 'right', name: '右对齐'},
          //       ],
          //       value: ''
          //     },
          //     {
          //       type: 'el-select',
          //       label: '纵向位置',
          //       name: 'longitudinalPosition',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'top', name: '顶部'},
          //         {code: 'middle', name: '中部'},
          //         {code: 'bottom', name: '底部'},
          //       ],
          //       value: ''
          //     },
          //     {
          //       type: 'el-select',
          //       label: '布局前置',
          //       name: 'layoutFront',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'vertical', name: '竖排'},
          //         {code: 'horizontal', name: '横排'},
          //       ],
          //       value: ''
          //     },
          //   ],
          // },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],






      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"categories": ["苹果","三星","小米","oppo","vivo"],"series": [{"name": "手机品牌","data": [1000,2229,3879,2379,4079]}]}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-barchart',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-gradient-color-barchart',
    type: 'chart',
    label: '柱形图-渐变色',
    icon: 'iconzhuzhuangtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '柱形图-渐变色',
        },
        {
          type: 'el-switch',
          label: '竖展示',
          name: 'verticalShow',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '柱体设置',
            list: [
              {
                type: 'el-slider',
                label: '最大宽度',
                name: 'maxWidth',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-slider',
                label: '圆角',
                name: 'radius',
                require: false,
                placeholder: '',
                value: 5,
              },
/*              {
                type: 'el-slider',
                label: '最小高度',
                name: 'minHeight',
                require: false,
                placeholder: '',
                value: 0,
              },*/
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 22
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#90979c'
              },
              {
                type: 'el-input-text',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngle',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: '#fff',
              }
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'ytextAngle',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'ytextInterval',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: '#fff',

              }, {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 2
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-text',
                label: '拼接符号',
                name: 'format',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              }, {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              }, {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              }, {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          // {
          //   name: '图例操作',
          //   list: [
          //     {
          //       type: 'el-switch',
          //       label: '图例',
          //       name: 'isShowLegend',
          //       required: false,
          //       placeholder: '',
          //       value: true,
          //     },
          //     {
          //       type: 'vue-color',
          //       label: '字体颜色',
          //       name: 'lengedColor',
          //       required: false,
          //       placeholder: '',
          //       value: '#fff',
          //     },
          //     {
          //       type: 'el-input-number',
          //       label: '字体大小',
          //       name: 'lengedFontSize',
          //       required: false,
          //       placeholder: '',
          //       value: 16,
          //     },
          //     {
          //       type: 'el-input-number',
          //       label: '图例宽度',
          //       name: 'lengedWidth',
          //       required: false,
          //       placeholder: '',
          //       value: 15,
          //     },
          //     {
          //       type: 'el-select',
          //       label: '横向位置',
          //       name: 'lateralPosition',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'left', name: '左对齐'},
          //         {code: 'center', name: '居中'},
          //         {code: 'right', name: '右对齐'},
          //       ],
          //       value: ''
          //     },
          //     {
          //       type: 'el-select',
          //       label: '纵向位置',
          //       name: 'longitudinalPosition',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'top', name: '顶部'},
          //         {code: 'middle', name: '中部'},
          //         {code: 'bottom', name: '底部'},
          //       ],
          //       value: ''
          //     },
          //     {
          //       type: 'el-select',
          //       label: '布局前置',
          //       name: 'layoutFront',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'vertical', name: '竖排'},
          //         {code: 'horizontal', name: '横排'},
          //       ],
          //       value: ''
          //     },
          //   ],
          // },
          {
            name: '渐变色',
            list: [
              {
                type: 'vue-color',
                label: '0%处',
                name: 'bar0color',
                required: false,
                placeholder: '',
                value: '#00F4FF'
              },
              {
                type: 'vue-color',
                label: '100%处',
                name: 'bar100color',
                required: false,
                placeholder: '',
                value: '#004DA7'
              },
              {
                type: 'vue-color',
                label: '阴影颜色',
                name: 'shadowColor',
                required: false,
                placeholder: '',
                value: '#00A0DD'
              },
              {
                type: 'el-input-number',
                label: '模糊系数',
                name: 'shadowBlur',
                required: false,
                placeholder: '',
                value: 4,
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"categories": ["苹果","三星","小米","oppo","vivo"],"series": [{"name": "手机品牌","data": [1000,2229,3879,2379,4079]}]}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-barchart',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-linechart',
    type: 'chart',
    label: '折线图',
    icon: 'iconjibenshuju',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '折线图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '折线设置',
            list: [
              {
                type: 'el-switch',
                label: '标记点',
                name: 'markPoint',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-slider',
                label: '点大小',
                name: 'pointSize',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-switch',
                label: '平滑曲线',
                name: 'smoothCurve',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '面积堆积',
                name: 'area',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-slider',
                label: '面积厚度',
                name: 'areaThickness',
                required: false,
                placeholder: '',
                value: 5,
              },
              {
                type: 'el-slider',
                label: '线条宽度',
                name: 'lineWidth',
                required: false,
                placeholder: '',
                value: 4,
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '##FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: '滚动条',
            list: [
              {
                type: 'el-switch',
                label: '是否开启',
                name: 'isRoll',
                required: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '开始位置',
                name: 'minRoll',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-input-number',
                label: '结束位置',
                name: 'maxRoll',
                required: false,
                placeholder: '',
                value: 80
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'xNameColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'xNameFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngle',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-text',
                label: '字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: '#fff',

              }
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'NameColorY',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-text',
                label: '字体大小',
                name: 'NameFontSizeY',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-text',
                label: '字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: '#fff',

              }, {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 3
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-text',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              },
              {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              },
              {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          // {
          //   name: '图例操作',
          //   list: [
          //     {
          //       type: 'el-switch',
          //       label: '图例',
          //       name: 'isShowLegend',
          //       required: false,
          //       placeholder: '',
          //       value: true,
          //     },
          //     {
          //       type: 'vue-color',
          //       label: '字体颜色',
          //       name: 'lengedColor',
          //       required: false,
          //       placeholder: '',
          //       value: '#fff',
          //     },
          //     {
          //       type: 'el-input-number',
          //       label: '字体大小',
          //       name: 'lengedFontSize',
          //       required: false,
          //       placeholder: '',
          //       value: 16,
          //     },
          //     {
          //       type: 'el-input-number',
          //       label: '图例宽度',
          //       name: 'lengedWidth',
          //       required: false,
          //       placeholder: '',
          //       value: 15,
          //     },
          //     {
          //       type: 'el-select',
          //       label: '横向位置',
          //       name: 'lateralPosition',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'left', name: '左对齐'},
          //         {code: 'center', name: '居中'},
          //         {code: 'right', name: '右对齐'},
          //       ],
          //       value: 'left'
          //     },
          //     {
          //       type: 'el-select',
          //       label: '纵向位置',
          //       name: 'longitudinalPosition',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'top', name: '顶部'},
          //         {code: 'middle', name: '中部'},
          //         {code: 'bottom', name: '底部'},
          //       ],
          //       value: ''
          //     },
          //     {
          //       type: 'el-select',
          //       label: '布局前置',
          //       name: 'layoutFront',
          //       required: false,
          //       placeholder: '',
          //       selectOptions: [
          //         {code: 'vertical', name: '竖排'},
          //         {code: 'horizontal', name: '横排'},
          //       ],
          //       value: ''
          //     },
          //   ],
          // },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#1E90FF'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"categories": ["苹果","三星","小米","oppo","vivo"],"series": [{"name": "手机品牌","data": [1009,3409,2309,5409,3409]}]}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-linechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-barlinechart',
    type: 'chart',
    label: '柱线图',
    icon: 'iconzhuxiantu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '柱线图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '折线设置',
            list: [
              {
                type: 'el-switch',
                label: '标记点',
                name: 'markPoint',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-slider',
                label: '点大小',
                name: 'pointSize',
                required: false,
                placeholder: '',
                value: 5,
              },
              {
                type: 'el-switch',
                label: '平滑曲线',
                name: 'smoothCurve',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '面积堆积',
                name: 'area',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-slider',
                label: '面积厚度',
                name: 'areaThickness',
                required: false,
                placeholder: '',
                value: 5,
              },
              {
                type: 'el-slider',
                label: '线条宽度',
                name: 'lineWidth',
                required: false,
                placeholder: '',
                value: 3,
              },
            ],
          },
          {
            name: '柱体设置',
            list: [
              {
                type: 'el-slider',
                label: '最大宽度',
                name: 'maxWidth',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-slider',
                label: '圆角',
                name: 'radius',
                require: false,
                placeholder: '',
                value: 5,
              },
              {
                type: 'el-slider',
                label: '最小高度',
                name: 'minHeight',
                require: false,
                placeholder: '',
                value: 0,
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'left'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'xNameColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'xNameFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngle',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: '#fff',

              }
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'NameColorY',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'NameFontSizeY',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: '#fff',

              }, {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 3
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              }, {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              }, {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              }, {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '图例',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-text',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'left', name: '左对齐'},
                  {code: 'center', name: '居中'},
                  {code: 'right', name: '右对齐'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'middle', name: '中部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: ''
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"xAxis": ["1月", "2月", "3月", "4月", "5月","6月", "7月", "8月","9月","10月","11月","12月"],"series": [{"type": "bar","name": "货运量","data": [2.6,5.9,9,26.4,28.7,70.7,175.6,182.2,48.7,18.8,6,2.3]},{"type": "line","name": "货运总量","yAxisIndex": 1,"data": [2,2.2,3.3,4.5,6.3,10.2,20.3,23.4,23,16.5,12,6.2]}]}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-barlinechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-piechart',
    type: 'chart',
    label: '饼图',
    icon: 'iconicon_tubiao_bingtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '饼图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        {
          type: 'el-select',
          label: '饼图样式',
          name: 'piechartStyle',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'shixin', name: '实心饼图'},
            {code: 'kongxin', name: '空心饼图'},
          ],
          value: 'shixin'
        },
        [
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'left'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '名称',
                name: 'textValue',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '数值',
                name: 'numberValue',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '百分比',
                name: 'percentage',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-switch',
                label: '指示线',
                name: 'isShowLine',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '指示线长度',
                name: 'lineLength',
                required: false,
                placeholder: '',
                value: 5,
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'vue-color',
                label: '网格线颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '图例',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-input-number',
                label: '图例高度',
                name: 'lengedHeight',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'left', name: '左对齐'},
                  {code: 'center', name: '居中'},
                  {code: 'right', name: '右对齐'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'middle', name: '中部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: ''
              },
            ],
          },
          {
            name: '大小设置',
            list: [
              {
                type: 'el-input-number',
                label: '内径尺寸(%)',
                name: 'insideSize',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '外径尺寸(%)',
                name: 'outsideSize',
                required: false,
                placeholder: '',
                value: 50
              }
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[{"value": 1048,"name": "搜索引擎"},{"value": 735, "name": "直接访问"},{"value": 580, "name": "邮件营销"},{"value": 484,"name":"联盟广告"},{"value":300,"name":"视频广告"}]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-piechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-funnel',
    type: 'chart',
    label: '漏斗图',
    icon: 'iconloudoutu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '漏斗图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '文字设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                require: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'color',
                require: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                require: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-switch',
                label: '反转',
                name: 'reversal',
                require: false,
                placeholder: '',
                value: 0
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'left'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: ''
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '网格线颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '图例',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'left', name: '左对齐'},
                  {code: 'center', name: '居中'},
                  {code: 'right', name: '右对齐'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'middle', name: '中部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: ''
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#0CD2E6'}, {color: '#00BFA5'}, {color: '#FFC722'}, {color: '#886EFF'}, {color: '#008DEC'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[{"value": 60,"name": "访问"},{"value": 40, "name": "咨询"},{"value": 20, "name": "订单"},{"value": 80,"name":"点击"},{"value":100,"name":"展现"}]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-funnel',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-gauge',
    type: 'chart',
    label: '仪表盘',
    icon: 'iconyibiaopan',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '仪表盘',
        },
        {
          type: 'el-input-number',
          label: '刻度线粗度',
          name: 'tickMarkWeight',
          require: false,
          placeholder: '',
          value: 10,
        },
        {
          type: 'el-switch',
          label: '显示刻度值',
          name: 'showScaleValue',
          require: false,
          placeholder: '',
          value: true,
        },
        {
          type: 'el-switch',
          label: '显示刻度线',
          name: 'showTickMarks',
          require: false,
          placeholder: '',
          value: true,
        },
        {
          type: 'el-input-number',
          label: '刻度字号',
          name: 'scaleFontSize',
          require: false,
          placeholder: '',
          value: 16,
        },
        {
          type: 'el-input-number',
          label: '指标字号',
          name: 'targetFontSize',
          require: false,
          placeholder: '',
          value: 20,
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"value": 50, "name": "名称", "unit": "%"}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-gauge',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widget-map',
    type: 'chart',
    label: '中国地图',
    icon: 'iconzhongguoditu',
    options: {
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '迁徙图',
        },
      ],
      data: [],
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 600,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 400,
        },
      ]
    }
  },
  {
    code: 'WidgetPieNightingaleRoseArea',
    type: 'chart',
    label: '南丁格尔玫瑰图',
    icon: 'iconnandinggeermeiguitu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '南丁格尔玫瑰图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        {
          type: 'el-select',
          label: '饼图模式',
          name: 'nightingleRosetype',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'area', name: '面积模式'},
            {code: 'radius', name: '半径模式'},
          ],
          value: 'area'
        },
        [
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'left'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '名称',
                name: 'textValue',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '数值',
                name: 'numberValue',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '百分比',
                name: 'percentage',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-switch',
                label: '指示线',
                name: 'isShowLine',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '指示线长度',
                name: 'lineLength',
                required: false,
                placeholder: '',
                value: 5,
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'vue-color',
                label: '网格线颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '图例',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 12,
              },
			  {
			    type: 'el-input-number',
			    label: '图例宽度',
			    name: 'lengedHeight',
			    required: false,
			    placeholder: '',
			    value: 12,
			  },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'left', name: '左对齐'},
                  {code: 'center', name: '居中'},
                  {code: 'right', name: '右对齐'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'middle', name: '中部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: ''
              },
            ],
          },
          {
            name: '大小设置',
            list: [
              {
                type: 'el-input-number',
                label: '内径尺寸(%)',
                name: 'insideSize',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '外径尺寸(%)',
                name: 'outsideSize',
                required: false,
                placeholder: '',
                value: 50
              }
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}]
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[{"value": 1048,"name": "搜索引擎"},{"value": 735, "name": "直接访问"},{"value": 580, "name": "邮件营销"},{"value": 484,"name":"联盟广告"},{"value":300,"name":"视频广告"}]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-piechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widgetPiePercentageChart',
    type: 'chart',
    label: '百分比图',
    icon: 'icon020kongxinbingtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '百分比图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '数值设置',
            list: [
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'textNumColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '数值字体',
                name: 'textNumFontSize',
                required: false,
                placeholder: '',
                value: 40
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textNumFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'vue-color',
                label: '%号颜色',
                name: 'textPerColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '%号字体',
                name: 'textPerFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textPerFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              }
            ],
          },
          {
            name: '圆环设置',
            list: [
              {
                type: 'el-input-number',
                label: '刻度数量',
                name: 'lineNumber',
                required: false,
                placeholder: '',
                value: 8
              },
              {
                type: 'el-input-number',
                label: '刻度长度',
                name: 'lineLength',
                required: false,
                placeholder: '',
                value: 15
              },
              {
                type: 'el-input-number',
                label: '刻度宽度',
                name: 'lineWidth',
                required: false,
                placeholder: '',
                value: 5
              },
              {
                type: 'vue-color',
                label: '刻度颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: '#061740'
              },
            ]
          },
          {
            name: '渐变色',
            list: [
              {
                type: 'vue-color',
                label: '0%处颜色',
                name: 'color0Start',
                required: false,
                placeholder: '',
                value: '#4FADFD'
              },
              {
                type: 'vue-color',
                label: '100%颜色',
                name: 'color100End',
                required: false,
                placeholder: '',
                value: '#28E8FA'
              },
              {
                type: 'vue-color',
                label: '余处颜色',
                name: 'colorsurplus',
                required: false,
                placeholder: '',
                value: '#173164'
              },
            ]
          }
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: 60,
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-piechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widgetAirBubbleMap',
    type: 'chart',
    label: '气泡地图',
    icon: 'iconzhongguoditu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '气泡地图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: '#0F1C3C'
        },
        [
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'left'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
            ],
          },
          {
            name: '字体设置',
            list: [
              {
                type: 'el-input-number',
                label: '文字大小',
                name: 'fontTextSize',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'fontTextColor',
                required: false,
                placeholder: '',
                value: '#D4EEFF'
              },
              {
                type: 'el-select',
                label: '文字粗细',
                name: 'fontTextWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '数值大小',
                name: 'fontDataSize',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'fontDataColor',
                required: false,
                placeholder: '',
                value: '#D4EEFF'
              },
              {
                type: 'el-select',
                label: '数值粗细',
                name: 'fontDataWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '气泡设置',
            list: [
              {
                type: 'el-input-number',
                label: '最小半径',
                name: 'fontminSize4Pin',
                required: false,
                placeholder: '',
                value: 20,
              },
              {
                type: 'el-input-number',
                label: '最大半径',
                name: 'fontmaxSize4Pin',
                required: false,
                placeholder: '',
                value: 100,
              },
              /*{
                type: 'vue-color',
                label: '气泡颜色',
                name: 'fontPieColor',
                required: false,
                placeholder: '',
                value: ''
              },*/
            ],
          },
          {
            name: '地图块颜色',
            list: [
              {
                type: 'vue-color',
                label: '0%处颜色',
                name: 'font0PreColor',
                required: false,
                placeholder: '',
                value: '#073684'
              },
              {
                type: 'vue-color',
                label: '100%颜色',
                name: 'font100PreColor',
                required: false,
                placeholder: '',
                value: '#061E3D'
              },
              {
                type: 'vue-color',
                label: '高亮渐变色',
                name: 'fontHighlightColor',
                required: false,
                placeholder: '',
                value: '#2B91B7'
              },
            ],
          },
          {
            name: '省份选择',
            list: [
              {
                type: 'el-select',
                label: '省份选择',
                name: 'selectProvince',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'china', name: '全国'},
                  {code: '北京', name: '北京'},
                  {code: '天津', name: '天津'},
                  {code: '上海', name: '上海'},
                  {code: '重庆', name: '重庆'},
                  {code: '河北', name: '河北'},
                  {code: '内蒙古', name: '内蒙古'},
                  {code: '四川', name: '四川'},
                  {code: '河南', name: '河南'},
                  {code: '湖北', name: '湖北'},
                  {code: '湖南', name: '湖南'},
                  {code: '江苏', name: '江苏'},
                  {code: '江西', name: '江西'},
                  {code: '辽宁', name: '辽宁'},
                  {code: '吉林', name: '吉林'},
                  {code: '黑龙江', name: '黑龙江'},
                  {code: '陕西', name: '陕西'},
                  {code: '山西', name: '山西'},
                  {code: '山东', name: '山东'},
                  {code: '青海', name: '青海'},
                  {code: '安徽', name: '安徽'},
                  {code: '海南', name: '海南'},
                  {code: '广东', name: '广东'},
                  {code: '贵州', name: '贵州'},
                  {code: '浙江', name: '浙江'},
                  {code: '福建', name: '福建'},
                  {code: '甘肃', name: '甘肃'},
                  {code: '云南', name: '云南'},
                  {code: '西藏', name: '西藏'},
                  {code: '宁夏', name: '宁夏'},
                  {code: '广西', name: '广西'},
                  {code: '新疆', name: '新疆'},
                  {code: '香港', name: '香港'},
                  {code: '澳门', name: '澳门'},
                  {code: '台湾', name: '台湾'},
                ],
                value: 'china'
              }
            ],
          }
        ],
      ],
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: [
            {"NAME":"海南省","VALUE":1},
            {"NAME":"北京市","VALUE":524},
            {"NAME":"天津市","VALUE":14},
            {"NAME":"上海市","VALUE":150},
            {"NAME":"重庆市","VALUE":75},
            {"NAME":"河北省","VALUE":13},
            {"NAME":"河南省","VALUE":83},
            {"NAME":"云南省","VALUE":11},
            {"NAME":"辽宁省","VALUE":19},
            {"NAME":"黑龙江省","VALUE":15},
            {"NAME":"湖南省","VALUE":69},
            {"NAME":"安徽省","VALUE":260},
            {"NAME":"山东省","VALUE":39},
            {"NAME":"新疆维吾尔自治区","VALUE":4},
            {"NAME":"江苏省","VALUE":31},
            {"NAME":"浙江省","VALUE":104},
            {"NAME":"江西省","VALUE":36},
            {"NAME":"湖北省","VALUE":1052},
            {"NAME":"广西壮族自治区","VALUE":33},
            {"NAME":"甘肃省","VALUE":347},
            {"NAME":"山西省","VALUE":8},
            {"NAME":"内蒙古自治区","VALUE":157},
            {"NAME":"陕西省","VALUE":22},
            {"NAME":"吉林省","VALUE":4},
            {"NAME":"福建省","VALUE":36},
            {"NAME":"贵州省","VALUE":39},
            {"NAME":"广东省","VALUE":996},
            {"NAME":"青海省","VALUE":27},
            {"NAME":"西藏自治区","VALUE":31},
            {"NAME":"四川省","VALUE":46},
            {"NAME":"宁夏回族自治区","VALUE":16},
            {"NAME":"海南省","VALUE":22},
            {"NAME":"台湾省","VALUE":6},
            {"NAME":"香港特别行政区","VALUE":2},
            {"NAME":"澳门特别行政区","VALUE":9}],
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-airirBubbleMap',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 600,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 400,
        },
      ]
    }
  },
  {
    code: 'widgetBarCirclechart',
    type: 'chart',
    label: '玉珏图',
    icon: 'iconnandinggeermeiguitu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '柱状图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '柱体设置',
            list: [
              {
                type: 'el-slider',
                label: '最大宽度',
                name: 'maxWidth',
                required: false,
                placeholder: '',
                value: 10,
              },
              // {
              //   type: 'el-slider',
              //   label: '圆角',
              //   name: 'radius',
              //   require: false,
              //   placeholder: '',
              //   value: 5,
              // },
              // {
              //   type: 'el-slider',
              //   label: '最小高度',
              //   name: 'minHeight',
              //   require: false,
              //   placeholder: '',
              //   value: 0,
              // },
            ],
          },
          {
            name: '坐标轴设置',
            list: [
              {
                type: 'el-switch',
                label: '数值显示',
                name: 'isShowCircleNumber',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '最大数值',
                name: 'barMaxNumber',
                required: false,
                placeholder: '',
                value: '5000'
              },
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'numberColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'cutlineColor',
                required: false,
                placeholder: '',
                value: '#00567D'
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: 'rgba(30, 144, 255, 1)'
              },
              {
                type: 'el-input-text',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          // {
          //   name: '坐标轴边距设置',
          //   list: [
          //     {
          //       type: 'el-slider',
          //       label: '左边距(像素)',
          //       name: 'marginLeft',
          //       required: false,
          //       placeholder: '',
          //       value: 10,
          //     }, {
          //       type: 'el-slider',
          //       label: '顶边距(像素)',
          //       name: 'marginTop',
          //       required: false,
          //       placeholder: '',
          //       value: 50,
          //     }, {
          //       type: 'el-slider',
          //       label: '右边距(像素)',
          //       name: 'marginRight',
          //       required: false,
          //       placeholder: '',
          //       value: 40,
          //     }, {
          //       type: 'el-slider',
          //       label: '底边距(像素)',
          //       name: 'marginBottom',
          //       required: false,
          //       placeholder: '',
          //       value: 10,
          //     },
          //   ],
          // },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '图例',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-input-number',
                label: '图例高度',
                name: 'lengedHeight',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'left', name: '左对齐'},
                  {code: 'center', name: '居中'},
                  {code: 'right', name: '右对齐'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'middle', name: '中部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: ''
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"categories": ["苹果","三星","小米","oppo","vivo"],"series": [{"name": "手机品牌","data": [1000,1229,2879,3379,4079]}]}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-barchart',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widgetBarManychart',
    type: 'chart',
    label: '分组柱状图',
    icon: 'iconzhuzhuangtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '分组柱状图',
        },
        {
          type: 'el-switch',
          label: '竖展示',
          name: 'verticalShow',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          // {
          //   name: '柱体设置',
          //   list: [
          //     {
          //       type: 'el-slider',
          //       label: '最大宽度',
          //       name: 'maxWidth',
          //       required: false,
          //       placeholder: '',
          //       value: 10,
          //     },
          //     {
          //       type: 'el-slider',
          //       label: '圆角',
          //       name: 'radius',
          //       require: false,
          //       placeholder: '',
          //       value: 5,
          //     },
          //     {
          //       type: 'el-slider',
          //       label: '最小高度',
          //       name: 'minHeight',
          //       require: false,
          //       placeholder: '',
          //       value: 0,
          //     },
          //   ],
          // },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: 'rgba(30, 144, 255, 1)'
              },
              {
                type: 'el-input-text',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: '滚动条',
            list: [
              {
                type: 'el-switch',
                label: '是否开启',
                name: 'isRoll',
                required: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '开始位置',
                name: 'minRoll',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-input-number',
                label: '结束位置',
                name: 'maxRoll',
                required: false,
                placeholder: '',
                value: 80
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'xNameColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'xNameFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngle',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: '#fff',

              }
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-input-text',
                label: '名称',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '坐标名颜色',
                name: 'NameColorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'NameFontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'vue-color',
                label: '颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              }, {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: '#fff',

              }, {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 3
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              }, {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              }, {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              }, {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '图例',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-input-number',
                label: '图例高度',
                name: 'lengedHeight',
                required: false,
                placeholder: '',
                value: 12,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'left', name: '左对齐'},
                  {code: 'center', name: '居中'},
                  {code: 'right', name: '右对齐'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'middle', name: '中部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: ''
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: ''
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '{"categories": ["苹果","三星","小米","oppo","vivo"],"series": [{"name": "手机品牌","type": "bar","data": [1000,2229,3879,2379,4079]},{"name": "手机品牌1","type": "bar","data": [11000,12229,13879,12379,14079]},{"name": "手机品牌2","type": "bar","data": [5000,5229,5879,5379,5079]}]}',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-barchart',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widgetHebeiMap',
    type: 'chart',
    label: '河北地图',
    icon: 'iconB-shengshiqu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '河北地图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: '#0F1C3C'
        },
        [
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'left'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 12
              },
            ],
          },
          {
            name: '字体设置',
            list: [
              {
                type: 'el-input-number',
                label: '文字大小',
                name: 'fontTextSize',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'fontTextColor',
                required: false,
                placeholder: '',
                value: '#D4EEFF'
              },
              {
                type: 'el-select',
                label: '文字粗细',
                name: 'fontTextWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '数值大小',
                name: 'fontDataSize',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'fontDataColor',
                required: false,
                placeholder: '',
                value: '#D4EEFF'
              },
              {
                type: 'el-select',
                label: '数值粗细',
                name: 'fontDataWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '气泡设置',
            list: [
              {
                type: 'el-input-number',
                label: '最小半径',
                name: 'fontminSize4Pin',
                required: false,
                placeholder: '',
                value: 20,
              },
              {
                type: 'el-input-number',
                label: '最大半径',
                name: 'fontmaxSize4Pin',
                required: false,
                placeholder: '',
                value: 100,
              },
              /*{
                type: 'vue-color',
                label: '气泡颜色',
                name: 'fontPieColor',
                required: false,
                placeholder: '',
                value: ''
              },*/
            ],
          },
          {
            name: '地图块颜色',
            list: [
              {
                type: 'vue-color',
                label: '0%处颜色',
                name: 'font0PreColor',
                required: false,
                placeholder: '',
                value: '#073684'
              },
              {
                type: 'vue-color',
                label: '100%颜色',
                name: 'font100PreColor',
                required: false,
                placeholder: '',
                value: '#061E3D'
              },
              {
                type: 'vue-color',
                label: '高亮渐变色',
                name: 'fontHighlightColor',
                required: false,
                placeholder: '',
                value: '#2B91B7'
              },
            ],
          },
        ],
      ],
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: [
            {"name":"石家庄","value":1100},
            {"name":"保定","value":524},
            {"name":"唐山","value":14},
            {"name":"秦皇岛","value":150},
            {"name":"承德","value":75},
            {"name":"张家口","value":13},
            {"name":"邯郸","value":83},
            {"name":"邢台","value":11},
            {"name":"衡水","value":19},
            {"name":"沧州","value":15},
            {"name":"廊坊","value":69}],
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-piechart',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 600,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 400,
        },
      ]
    }
  },
  {
    code: 'widgetLineStackChart',
    type: 'chart',
    label: '折线堆叠图',
    icon: 'iconduidietu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '折线堆叠图',
        },
        {
          type: 'el-switch',
          label: '竖展示',
          name: 'verticalShow',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '折线设置',
            list: [
              {
                type: 'el-switch',
                label: '标记点',
                name: 'markPoint',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-slider',
                label: '点大小',
                name: 'pointSize',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-switch',
                label: '平滑曲线',
                name: 'smoothCurve',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-switch',
                label: '面积堆积',
                name: 'area',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-slider',
                label: '面积厚度',
                name: 'areaThickness',
                required: false,
                placeholder: '',
                value: 5,
              },
              {
                type: 'el-slider',
                label: '线条宽度',
                name: 'lineWidth',
                required: false,
                placeholder: '',
                value: 4,
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: 'rgba(30, 144, 255, 1)'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: '滚动条',
            list: [
              {
                type: 'el-switch',
                label: '是否开启',
                name: 'isRoll',
                required: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '开始位置',
                name: 'minRoll',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-input-number',
                label: '结束位置',
                name: 'maxRoll',
                required: false,
                placeholder: '',
                value: 80
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: 'X轴别名',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '别名颜色',
                name: 'xNameColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '别名字号',
                name: 'xNameFontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngleX',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '文字字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',

              }
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: 'Y轴别名',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '别名颜色',
                name: 'NameColorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '别名字号',
                name: 'NameFontSizeY',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngleY',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '文字字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              }, {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',

              },
              {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 2
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'tipsFontSize',
                required: false,
                placeholder: '',
                value: 16
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              }, {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              }, {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              }, {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: 'top'
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: 'horizontal'
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: [
            {"axis":"2021-07-25","name":"A","data":"12"},
            {"axis":"2021-07-25","name":"B","data":"20"},
            {"axis":"2021-07-26","name":"B","data":"5"},
            {"axis":"2021-07-26","name":"C","data":"20"},
            {"axis":"2021-07-27","name":"A","data":"15"},
            {"axis":"2021-07-27","name":"B","data":"30"},
            {"axis":"2021-07-27","name":"C","data":"5"}
          ],
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-stackchart',
          dictKey: 'STACK_PROPERTIES',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widgetBarStackChart',
    type: 'chart',
    label: '柱状堆叠图',
    icon: 'iconzhuzhuangtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '柱状堆叠图',
        },
        {
          type: 'el-switch',
          label: '竖展示',
          name: 'verticalShow',
          required: false,
          placeholder: '',
          value: false,
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        {
          type: 'el-select',
          label: '堆叠样式',
          name: 'stackStyle',
          required: false,
          placeholder: '',
          selectOptions: [
            {code: 'leftRight', name: '左右堆叠'},
            {code: 'upDown', name: '上下堆叠'},
          ],
          value: 'leftRight'
        },
        [
          {
            name: '柱体设置',
            list: [
              {
                type: 'el-slider',
                label: '最大宽度',
                name: 'maxWidth',
                required: false,
                placeholder: '',
                value: 20,
              },
              {
                type: 'el-slider',
                label: '圆角',
                name: 'radius',
                require: false,
                placeholder: '',
                value: 5,
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-input-text',
                label: '副标题',
                name: 'subText',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: 'rgba(30, 144, 255, 1)'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'subTextFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'subTextFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
            ],
          },
          {
            name: '滚动条',
            list: [
              {
                type: 'el-switch',
                label: '是否开启',
                name: 'isRoll',
                required: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-input-number',
                label: '开始位置',
                name: 'minRoll',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-input-number',
                label: '结束位置',
                name: 'maxRoll',
                required: false,
                placeholder: '',
                value: 80
              },
            ],
          },
          {
            name: 'X轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideX',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: 'X轴别名',
                name: 'xName',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '别名颜色',
                name: 'xNameColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-input-number',
                label: '别名字号',
                name: 'xNameFontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalX',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngleX',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'el-input-number',
                label: '文字间隔',
                name: 'textInterval',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'Xcolor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '文字字号',
                name: 'fontSizeX',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineX',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorX',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',

              }
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowY',
                require: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: 'Y轴别名',
                name: 'textNameY',
                require: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '别名颜色',
                name: 'NameColorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '别名字号',
                name: 'NameFontSizeY',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'el-switch',
                label: '轴反转',
                name: 'reversalY',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-slider',
                label: '文字角度',
                name: 'textAngleY',
                required: false,
                placeholder: '',
                value: 0
              },
              {
                type: 'vue-color',
                label: '文字颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '文字字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              }, {
                type: 'el-switch',
                label: '分割线显示',
                name: 'isShowSplitLineY',
                require: false,
                placeholder: '',
                value: false,
              }, {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitLineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',

              },
              {
                type: 'el-input-number',
                label: '间隔个数',
                name: 'ySplitNumber',
                required: false,
                placeholder: '',
                value: 2
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: false
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'tipsFontSize',
                required: false,
                placeholder: '',
                value: 16
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左边距(像素)',
                name: 'marginLeft',
                required: false,
                placeholder: '',
                value: 10,
              }, {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 50,
              }, {
                type: 'el-slider',
                label: '右边距(像素)',
                name: 'marginRight',
                required: false,
                placeholder: '',
                value: 40,
              }, {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: 'top'
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: 'horizontal'
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: [
            {"axis":"2021-07-25","name":"A","data":"12"},
            {"axis":"2021-07-25","name":"B","data":"20"},
            {"axis":"2021-07-26","name":"B","data":"5"},
            {"axis":"2021-07-26","name":"C","data":"20"},
            {"axis":"2021-07-27","name":"A","data":"15"},
            {"axis":"2021-07-27","name":"B","data":"30"},
            {"axis":"2021-07-27","name":"C","data":"5"}
          ],
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-stackchart',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    },
  },
  {
    code: 'widgetBarCompareChart',
    type: 'chart',
    label: '柱状对比图',
    icon: 'iconduibitupu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '柱状对比图',
        },
        {
          type: 'vue-color',
          label: '背景颜色',
          name: 'background',
          required: false,
          placeholder: '',
          value: ''
        },
        [
          {
            name: '柱体设置',
            list: [
              {
                type: 'el-slider',
                label: '最大宽度',
                name: 'maxWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-slider',
                label: '圆角',
                name: 'radius',
                require: false,
                placeholder: '',
                value: 5,
              },
            ],
          },
          {
            name: '标题设置',
            list: [
              {
                type: 'el-switch',
                label: '标题',
                name: 'isNoTitle',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-text',
                label: '标题',
                name: 'titleText',
                required: false,
                placeholder: '',
                value: '',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: '#FFD700'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'textFontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'textFontSize',
                required: false,
                placeholder: '',
                value: 20
              },
              {
                type: 'el-select',
                label: '字体位置',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
            ],
          },
          {
            name: '左X轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideXLeft',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '数值间隔',
                name: 'splitNumberLeft',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'XcolorLeft',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '数值字号',
                name: 'fontSizeXLeft',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'el-switch',
                label: '刻度线',
                name: 'tickLineLeft',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-switch',
                label: 'X轴线',
                name: 'xLineLeft',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorXLeft',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '竖分割线',
                name: 'SplitLineLeft',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'SplitLineColorLeft',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-input-number',
                label: '分割线宽度',
                name: 'SplitLinefontSizeLeft',
                required: false,
                placeholder: '',
                value: 1,
              },
              {
                type: 'el-switch',
                label: '边框线',
                name: 'frameLineLeft',
                require: false,
                placeholder: '',
                value: false,
              },
            ],
          },
          {
            name: '右X轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideXRight',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '数值间隔',
                name: 'splitNumberRight',
                required: false,
                placeholder: '',
                value: ''
              },
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'XcolorRight',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '数值字号',
                name: 'fontSizeXRight',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'el-switch',
                label: '刻度线',
                name: 'tickLineRight',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-switch',
                label: 'X轴线',
                name: 'xLineRight',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorXRight',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-switch',
                label: '竖分割线',
                name: 'SplitLineRight',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'SplitLineColorRight',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
              {
                type: 'el-input-number',
                label: '分割线宽度',
                name: 'SplitLinefontSizeRight',
                required: false,
                placeholder: '',
                value: 1,
              },
              {
                type: 'el-switch',
                label: '边框线',
                name: 'frameLineRight',
                require: false,
                placeholder: '',
                value: false,
              },
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'hideY',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '数值颜色',
                name: 'colorY',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '数值字号',
                name: 'fontSizeY',
                required: false,
                placeholder: '',
                value: 14,
              },
              {
                type: 'el-select',
                label: '数值对齐',
                name: 'textAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-switch',
                label: '刻度线',
                name: 'tickLineY',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'el-switch',
                label: 'Y轴线',
                name: 'lineY',
                require: false,
                placeholder: '',
                value: false,
              },
              {
                type: 'vue-color',
                label: '轴颜色',
                name: 'lineColorY',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,0.5)',
              },
            ],
          },
          {
            name: '数值设定',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: true
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: 14
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'subTextColor',
                required: false,
                placeholder: '',
                value: '#fff'
              },
              {
                type: 'el-select',
                label: '字体粗细',
                name: 'fontWeight',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'normal', name: '正常'},
                  {code: 'bold', name: '粗体'},
                  {code: 'bolder', name: '特粗体'},
                  {code: 'lighter', name: '细体'}
                ],
                value: 'normal'
              },
            ],
          },
          {
            name: '提示语设置',
            list: [
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'tipsFontSize',
                required: false,
                placeholder: '',
                value: 16
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(255,255,255,1)'
              },
            ],
          },
          {
            name: '坐标轴边距设置',
            list: [
              {
                type: 'el-slider',
                label: '左右边距(像素)',
                name: 'marginLeftRight',
                required: false,
                placeholder: '',
                value: 10,
              },
              {
                type: 'el-slider',
                label: '顶边距(像素)',
                name: 'marginTop',
                required: false,
                placeholder: '',
                value: 40,
              },
              {
                type: 'el-slider',
                label: '底边距(像素)',
                name: 'marginBottom',
                required: false,
                placeholder: '',
                value: 10,
              },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: 'top'
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: 'horizontal'
              },
            ],
          },
          {
            name: '自定义配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
        ],
      ],
      // 数据
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: [
            {"axis":"07-25","name":"success","data":"2"},
            {"axis":"07-25","name":"fail","data":"10"},
            {"axis":"07-26","name":"success","data":"5"},
            {"axis":"07-26","name":"fail","data":"20"},
            {"axis":"07-27","name":"success","data":"15"},
            {"axis":"07-27","name":"fail","data":"30"},
            {"axis":"07-28","name":"success","data":"10"},
            {"axis":"07-28","name":"fail","data":"12"},
            {"axis":"07-29","name":"success","data":"9"},
            {"axis":"07-29","name":"fail","data":"16"},
          ],
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          chartType: 'widget-stackchart',
          dictKey: 'STACK_PROPERTIES',
          value: {},
        },
      ],
      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 200,
        },
      ],
    }
  },
  {
    code: 'widget-pictographchart',
    type: 'chart',
    label: '象形百分图',
    icon: 'icon020kongxinbingtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '象形图',
        },
        {
          type: 'el-input-textarea',
          label: 'svg图形',
          name: 'pathD',
          required: false,
          placeholder: '',
          value: 'M359.7,410.8c-1.7-0.8-3.5-0.8-5.2-3.2c-3-5.4-6.1-13.4-7.6-17.9c-1.6-4.5-2.1-10.1-4-13.8c-1.9-3.7-4.3-7.3-5.7-10.9c-1.4-3.6-3-9.1-5-16.6c-0.8-7.6-1.2-15.6-3.2-20.3c-2-4.7-10.9-5.3-14.9-5.5c-4-0.2-8.4-6.9-8.4-6.9c-0.3-3.3,0.4-10.2,0.6-11.3c0.2-1.1,0.3-3,0.4-4.4c0.2,0,0.3,0,1,0c3-2.1,3.3-6.3,3.2-7c-0.1-0.7-0.6-0.8-2-0.7c0.1-8.2-2.4-12-4.1-14c-1.7-1.9-5.1-2.6-7.5-2.6c-2.4,0-5.8,0.7-7.5,2.6c-1.7,1.9-4.2,5.8-4.1,14c-1.4-0.1-1.8,0-2,0.7c-0.1,0.7,0.2,4.9,3.2,7c0.7,0,0.8,0,1,0c0,1.4,0.2,3.6,0.4,4.4c0.2,1.1,0.9,7.9,0.6,11.3c0,0-4.4,6.6-8.4,6.9c-4,0.2-12.9,0.8-14.9,5.5c-2,4.7-2.4,12.6-3.2,20.3c-2.1,7.5-3.6,13-5,16.6c-1.4,3.6-3.8,7.2-5.7,10.9c-1.9,3.7-2.5,9.3-4,13.8c-1.6,4.5-4.7,12.5-7.6,17.9c-1.7,2.4-3.4,2.4-5.2,3.2c-1.7,0.8-6.3,6.5-6.7,7c0,1.1,0,2,1.5,1.3c1.1-0.5,4.5-3.7,4.9-4.1c0.3,0.2,0.2,1.2-2.1,6c-2,4.2-3.6,6.9-3.5,8.4c0,0.5,0.5,0.7,1.2,0.2c1.1-0.8,5-7.3,5.4-8c0.5-1.2,0.7,0,0.5,0.4c-0.4,0.7-3.7,10.1-3.8,11.1c0,1.5,1.7,0.2,1.7,0.2c1.3-0.8,4.6-9.7,5.1-9.5c0.9,0.1,0.5,1.6,0.1,2.8c-0.5,1.5-1.5,7.9-1.5,7.9s0.2,1.1,1.5,0c0.9-1.5,2.5-7.6,3.2-9c0.5-0.4,1,0.1,0.9,1.4c0.3,1.8,0.2,5.5,0.6,6c0.5,0.5,1.1-0.7,1.2-1.7c0.2-4.2,0.6-7.4,1.1-9.9c0.4-1.4,1.1-3.5,1.2-7.3c0.1-2.5-1-2.5,1.4-6.8c1.3-2.4,13.5-23.6,15.6-27.3c2.1-3.8,0.9-4.7,2.4-8c1.5-3.4,5.6-15.4,6.6-16.7c0,0,1.3-0.2,1.8,0c-0.2,2.3,1,12.3,1.5,14c0.5,1.7,1,16,0.2,18.7c-0.8,2.7-4.2,5.5-5.8,15.2c-1.6,9.7-2.9,32-1.9,47.1c1,15.2,4.4,28.7,2.6,33.8c-1.8,5.1-4.1,11.1-3.8,19.2c0.2,8.1,0.2,34.7,0.5,39.1c-1.2,1.7-1.7,1.8-1.7,3.1c0,1.2,0.6,2.5,0.2,4.4c-0.4,2-3.5,9.1-4.1,10.8c0,2.6-0.5,1.7,0,2.6c0.5,0.9,2.7,0.9,3.1,1.2c0.4,0.4,3.1,1.1,4.1,1.1c1,0,3.7,1.1,4.8-0.9c1.1-2,1.5-9.6,1.8-16.4c0.6-1.1,0.5-1.4,0.5-3.8c0-2.5-0.7-3,0.1-6.3c0.9-3.3,1.4-9.5,3.8-15.4c2.5-5.9,4.6-13.3,4.6-16c0-2.7-0.5-12.3,0.5-17.3c0.7-3.5,2.7-5.7,3.4-8.1c0.9-3,1.6-27.2,2.5-32.8c0.9-5.5,2.7-11,3-15.5c0.2-4.6,0.3-7.8,0.9-8.4c1,0.2,1.2,0.2,1.2,0.2s0.2,0.1,1.2-0.2c0.5,0.6,0.6,3.8,0.9,8.4c0.2,4.6,2.1,10,3,15.5c0.9,5.5,1.5,29.8,2.5,32.8c0.7,2.4,2.7,4.6,3.4,8.1c1,5,0.5,14.6,0.5,17.3c0,2.7,2.1,10.1,4.6,16c2.5,5.9,3,12.1,3.8,15.4c0.9,3.3,0.1,3.8,0.1,6.3c0,2.5-0.1,2.7,0.5,3.8c0.4,6.8,0.7,14.4,1.8,16.4c1.1,2,3.8,0.9,4.8,0.9c1,0,3.7-0.7,4.1-1.1c0.4-0.4,2.6-0.4,3.1-1.2c0.5-0.9,0,0,0-2.6c-0.6-1.7-3.7-8.9-4.1-10.8c-0.4-2,0.2-3.2,0.2-4.4c0-1.2-0.5-1.4-1.7-3.1c0.2-4.3,0.2-30.9,0.5-39.1c0.2-8.1-2-14.2-3.8-19.2c-1.8-5.1,1.6-18.6,2.6-33.8c1-15.2-0.3-37.5-1.9-47.1c-1.6-9.7-5-12.4-5.8-15.2c-0.8-2.6-0.3-17,0.2-18.7c0.5-1.7,1.6-11.7,1.5-14c0.5-0.2,1.8,0,1.8,0c1,1.3,5.1,13.3,6.6,16.7c1.5,3.4,0.2,4.3,2.4,8c2.1,3.8,14.3,24.9,15.6,27.3c2.4,4.3,1.3,4.3,1.4,6.8c0.1,3.8,0.8,5.9,1.2,7.3c0.6,2.4,1,5.7,1.1,9.9c0.2,1,0.7,2.2,1.2,1.7c0.5-0.5,0.3-4.2,0.6-6c-0.1-1.3,0.4-1.8,0.9-1.4c0.7,1.4,2.3,7.5,3.2,9c1.3,1,1.5,0,1.5,0s-1-6.4-1.5-7.9c-0.4-1.2-0.8-2.7,0.1-2.8c0.5-0.2,3.9,8.7,5.1,9.5c0,0,1.7,1.2,1.7-0.2c-0.1-1-3.4-10.4-3.8-11.1c-0.2-0.4,0-1.6,0.5-0.4c0.4,0.7,4.3,7.2,5.4,8c0.7,0.5,1.2,0.3,1.2-0.2c0.2-1.5-1.5-4.2-3.5-8.4c-2.3-4.8-2.4-5.8-2.1-6c0.4,0.4,3.9,3.6,4.9,4.1c1.5,0.7,1.5-0.2,1.5-1.3C366,417.4,361.4,411.6,359.7,410.8z',
        },
        [
          {
            name: '图形设置',
            list: [
              {
                type: 'el-input-number',
                label: '数值',
                name: 'Number',
                required: false,
                placeholder: '',
                value: 66,
              },
              {
                type: 'el-input-number',
                label: '图形宽度',
                name: 'barWidth',
                required: false,
                placeholder: '',
                value: '200',
              },
              {
                type: 'vue-color',
                label: '背景颜色',
                name: 'bacColor',
                required: false,
                placeholder: '',
                value: 'rgba(44, 42, 33, 1)',
              },
              {
                type: 'vue-color',
                label: '填充颜色',
                name: 'picColor',
                required: false,
                placeholder: '',
                value: 'rgba(20, 107, 191, 1)',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'numSize',
                required: false,
                placeholder: '',
                value: '26',
              },
              {
                type: 'el-input-number',
                label: '描边宽度',
                name: 'borderWidth',
                required: false,
                placeholder: '',
                value: 1,
              },
              {
                type: 'vue-color',
                label: '描边颜色',
                name: 'borderColor',
                required: false,
                placeholder: '',
                value: 'rgba(44, 42, 33, 1)',
              },
            ],
          },
          {
            name: 'Y轴设置',
            list: [
              {
                type: 'el-switch',
                label: '是否开启',
                name: 'isShow',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'el-input-number',
                label: '坐标轴距离',
                name: 'offSet',
                required: false,
                placeholder: '',
                value: '0',
              },
              {
                type: 'vue-color',
                label: '坐标轴颜色',
                name: 'lineColor',
                required: false,
                placeholder: '',
                value: 'rgba(0, 0, 0, 1)',
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'textColor',
                required: false,
                placeholder: '',
                value: 'rgba(0, 0, 0, 1)',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: '26',
              },
              {
                type: 'el-switch',
                label: '分割线开关',
                name: 'isShows',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '分割线颜色',
                name: 'splitColor',
                required: false,
                placeholder: '',
                value: 'rgba(0, 0, 0, 1)',
              },
              {
                type: 'el-select',
                label: '分割线类型',
                name: 'splitAlign',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'solid', name: '实线'},
                  {code: 'dashed', name: '虚线'},
                ],
                value: 'solid'
              },
            ],
          },
        ]

      ],
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[{ "name": "百分比" ,"value": 66}]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-details',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 800
        },
      ],
    },
  },
  {
    code: 'widget-radarchart',
    type: 'chart',
    label: '雷达图',
    icon: 'icon020kongxinbingtu',
    options: {
      // 配置
      setup: [
        {
          type: 'el-input-text',
          label: '图层名称',
          name: 'layerName',
          required: false,
          placeholder: '',
          value: '雷达图',
        },
        {
          type: 'el-input-number',
          label: '背景圈数',
          name: 'Number',
          required: false,
          placeholder: '',
          value: 4,
        },
        [
			{
			  name: '图表位置大小',
			  list: [
				{
				  type: 'el-input-number',
				  label: '半径大小',
				  name: 'radiusValue',
				  required: false,
				  placeholder: '',
				  value: '80',
				},
			  ],
			},
          {
            name: '线条配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'customColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
          {
            name: '背景配色',
            list: [
              {
                type: 'customColor',
                label: '',
                name: 'backgroundColor',
                required: false,
                value: [{color: '#016DF3'}, {color: '#1ED2F7'}, {color: '#5747B6'}, {color: '#3538A3'}, {color: '#541669'}],
              },
            ],
          },
          {
            name: '字体设置',
            list: [
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'color',
                required: false,
                placeholder: '',
                value: '#FAD400',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'fontSize',
                required: false,
                placeholder: '',
                value: '18',
              },
			  {
			    type: 'vue-color',
			    label: '字体背景色',
			    name: 'fontBackgroundColor',
			    required: false,
			    placeholder: '',
			    value: 'rgba(0, 0, 0, .3)',
			  },
            ],
          },
          {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'bottom', name: '底部'},
                  {code: 'middle', name: '中心'},
                ],
                value: 'top'
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: 'horizontal'
              },
            ],
          },
        ],
        {
          type: 'vue-color',
          label: '网格线颜色',
          name: 'lineColor',
          required: false,
          placeholder: '',
          value: 'rgba(0, 0, 0, 1)',
        },
        {
            name: '图例操作',
            list: [
              {
                type: 'el-switch',
                label: '显示',
                name: 'isShowLegend',
                required: false,
                placeholder: '',
                value: true,
              },
              {
                type: 'vue-color',
                label: '字体颜色',
                name: 'lengedColor',
                required: false,
                placeholder: '',
                value: '#fff',
              },
              {
                type: 'el-input-number',
                label: '字体大小',
                name: 'lengedFontSize',
                required: false,
                placeholder: '',
                value: 16,
              },
              {
                type: 'el-input-number',
                label: '图例宽度',
                name: 'lengedWidth',
                required: false,
                placeholder: '',
                value: 15,
              },
              {
                type: 'el-select',
                label: '横向位置',
                name: 'lateralPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'center', name: '居中'},
                  {code: 'left', name: '左对齐'},
                  {code: 'right', name: '右对齐'},
                ],
                value: 'center'
              },
              {
                type: 'el-select',
                label: '纵向位置',
                name: 'longitudinalPosition',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'top', name: '顶部'},
                  {code: 'bottom', name: '底部'},
                ],
                value: 'top'
              },
              {
                type: 'el-select',
                label: '布局前置',
                name: 'layoutFront',
                required: false,
                placeholder: '',
                selectOptions: [
                  {code: 'vertical', name: '竖排'},
                  {code: 'horizontal', name: '横排'},
                ],
                value: 'horizontal'
              },
            ],
          },

      ],
      data: [
        {
          type: 'el-radio-group',
          label: '数据类型',
          name: 'dataType',
          require: false,
          placeholder: '',
          selectValue: true,
          selectOptions: [
            {
              code: 'staticData',
              name: '静态数据',
            },
            {
              code: 'dynamicData',
              name: '动态数据',
            },
          ],
          value: 'staticData',
        },
        {
          type: 'el-input-number',
          label: '刷新时间(毫秒)',
          name: 'refreshTime',
          relactiveDom: 'dataType',
          relactiveDomValue: 'dynamicData',
          value: 300000
        },
        {
          type: 'el-button',
          label: '静态数据',
          name: 'staticData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          relactiveDomValue: 'staticData',
          value: '[{\"name\": \"最大\",\"德育素质\": 100,\"智育素质\": 100,\"体育素质\": 100,\"社会活动\": 100,\"创新素质\": 100,\"心理素质\": 100},{\"name\": \"个人发展分\",\"德育素质\": 87,\"贰\": 85,\"叁\": 76,\"肆\": 83,\"伍\": 67,\"心理素质\": 79},{\"name\": \"平均发展分\",\"德育素质\": 77,\"贰\": 75,\"叁\": 79,\"肆\": 73,\"伍\": 75,\"心理素质\": 75}]',
        },
        {
          type: 'dycustComponents',
          label: '',
          name: 'dynamicData',
          required: false,
          placeholder: 'px',
          relactiveDom: 'dataType',
          chartType: 'widget-leidatu',
          relactiveDomValue: 'dynamicData',
          value: {},
        },
      ],

      // 坐标
      position: [
        {
          type: 'el-input-number',
          label: '左边距',
          name: 'left',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '上边距',
          name: 'top',
          required: false,
          placeholder: 'px',
          value: 0,
        },
        {
          type: 'el-input-number',
          label: '宽度',
          name: 'width',
          required: false,
          placeholder: '该容器在1920px大屏中的宽度',
          value: 400,
        },
        {
          type: 'el-input-number',
          label: '高度',
          name: 'height',
          required: false,
          placeholder: '该容器在1080px大屏中的高度',
          value: 400
        },
      ],
    },
  },
]

const getToolByCode = function (code) {
  // 获取大屏底层设置属性
  if (code == 'screen') {
    return screenConfig
  }
  // 获取组件
  var item = widgetTools.find(function (item, index, arrs) {
    return item.code === code
  })
  return item
}

export {widgetTools, getToolByCode}
