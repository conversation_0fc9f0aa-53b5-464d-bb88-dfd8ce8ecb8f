<template>
  <basic-container>
    <h3 class="pageTitle">学生特殊人群管理</h3>
    <el-container>
      <el-main>
        <avue-crud :data="data"
                   :option="option"
                   :page.sync="page"
                   :table-loading="loading"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @row-del="deleteItemHandle"
                   @refresh-change="refresh"
                   @search-change="searchChange">
          <template slot="menuLeft">
            <el-button type="primary"
                       size="mini"
                       icon="el-icon-plus"
                       @click="openUserHandle">新增
            </el-button>
          </template>
          <template slot="menuLeft">
            <el-button type="success"
                       size="mini"
                       icon="el-icon-upload2"
                       @click="handleImport">导入
            </el-button>
          </template>
          <template slot="menuLeft">
            <el-button type="success"
                       size="mini"
                       icon="el-icon-download"
                       @click="handleExport">导出
            </el-button>
          </template>
          <template slot-scope="{row}" slot="menu" >
            <el-button style="margin-left:10px;" size="small" type="text" icon="el-icon-refresh" @click.native="updateHandle(row)">更新</el-button>
          </template>
        </avue-crud>
      </el-main>
    </el-container>

    <el-dialog title="数据导入"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
      <avue-form :option="excelOption"
                 v-model="excelForm"
                 :upload-after="uploadAfter">
        <template slot="menuForm">
          <el-button type="primary" @click="handleTemplate()">
            点击下载模板<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>

    <el-dialog title="学生信息" :visible.sync="userOpen" width="70%" append-to-body>
      <UserSelector @selectedUser="selectedUser" @close="closeUserSelector" :selected-user-param="selectedUserParam"></UserSelector>
    </el-dialog>
  </basic-container>
</template>

<script>
import {addBatchIds, deleteInfo, editInfo, exportData, queryPage,} from "@/api/xsInfoSpecial";
import UserSelector from "@/components/selector/UserSelector";

export default {
  components:{
    UserSelector
  },
  data() {
    return {
      // tree
      filterText: "",
      param: {},
      // form
      data: [],
      page: {
        currentPage: 1,
        total: 0,
        pageSize: 10,
      },
      loading: false,
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "文件上传",
            prop: "file",
            type: "upload",
            drag: true,
            loadText: "文件上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "info",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/stXsInfoSpecial/import",
          },
        ],
      },
      userOpen: false,
      selectedUserParam: {},
      userListData:[],
    };
  },
  created() {
    this.onLoad();
  },
  mounted() {
  },
  methods: {
    pageParam() {
      let queryParam = this.param;
      const context = {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam,
      };
      return context;
    },
    searchChange(param, done) {
      this.param = param;
      this.onLoad()
      done()
    },
    onLoad() {
      this.loading = true;
      let context = this.pageParam();
      queryPage(context).then((res) => {
        this.loading = false;
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.data = data.records;
      });
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad();
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad();
    },
    refresh() {
      this.onLoad()
    },

    //tree filter
    filterNode(value, data) {
      if (!value) return true;
      return data.rolename.indexOf(value) !== -1;
    },

    // 添加
    openUserHandle() {
      this.userOpen=true;
    },
    addUserHandle(data) {
      const context = {
        id: data
      };
      addBatchIds(context).then((res) => {
        if (res.data.code == "00000") {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.onLoad();
        } else {
          this.$message.error("操作失败");
        }
      });
    },

    // 更新数据
    updateHandle(row) {
      this.$confirm(
          "此操作将更新数据【" + row.xh + "】, 是否继续?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
      ).then(() => {
        const context = {
          xh: row.xh,
        };
        editInfo(context).then((res) => {
          if (res.data.code == "00000") {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.onLoad();
          } else {
            this.$message.error("操作失败");
          }
        });
      }).catch(() => {
      });
    },

    // 删除
    deleteItemHandle(row) {
      this.$confirm(
          "此操作将删除数据【" + row.xh + "】, 是否继续?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
      ).then(() => {
        deleteInfo({id: row.id}).then((res) => {
          if (res.data.code == "00000") {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.refresh();
          } else {
            this.$message.error("删除失败");
          }
        });
      }).catch(() => {
      });
    },

    // 导入
    handleImport() {
      this.excelBox = true;
    },

    uploadAfter(res, done, loading) {
      if (res === "success") {
        this.excelBox = false;
        this.refresh();
        done();
      } else if (res === undefined) {
        this.$message.error("上传内容格式有误！");
        loading();
      } else {
        this.$message.warning("请上传 .xls,.xlsx 标准格式文件");
        loading();
      }
    },

    handleTemplate() {
      window.open(`/stXsInfoSpecial/downImportTemplate`);
    },
    handleExport() {
      const context = this.param;
      const fileName = "学生特殊人群信息.xlsx";
      exportData(context).then((res) => {
        const blob = new Blob([res], {type: "application/octet-stream"});
        if (window.navigator.msSaveOrOpenBlob) {
          //msSaveOrOpenBlob方法返回bool值
          navigator.msSaveBlob(blob, fileName); //本地保存
        } else {
          const link = document.createElement("a"); //a标签下载
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          window.URL.revokeObjectURL(link.href);
        }
      })
    },

    selectedUser(data){
      console.log("selectedUser");
      if(data.selectedUserDate.length==0){
        this.$message.error("请选择要添加的学生");
        return;
      }
      this.userListData=[];
      for (let i = 0; i < data.selectedUserDate.length; i++) {
        this.userListData.push(data.selectedUserDate[i].xh);
      }
      this.userOpen = data.userOpen;
      this.addUserHandle(this.userListData.join(","));
    },
    closeUserSelector(data) {
      this.userOpen = data.userOpen;
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  computed: {
    option() {
      return {
        // dialogWidth: 700,
        size: "mini",
        index: true,
        border: true,
        // tabs: true,
        stripe: true,
        dialogClickModal: false,
        menuWidth: 230,
        align: "center",
        // span: 24,
        // menuType: "button",
        addBtn: false,
        editBtn: false,
        delBtnText: "删除",
        refreshBtn: true,
        columnBtn: true,
        searchBtn: true,
        searchShow: true,
        searchIndex:3,
        searchIcon:true,
        column: [
          {
            label: "学号",
            prop: "xh",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "姓名",
            prop: "xm",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "性别",
            prop: "xb",
            type: "select",
            align: "center",
            addDisplay: false,
            editDisplay: false,
            search: true,
            dicData: [
              {label: "男", value: "男"},
              {label: "女", value: "女"},
              {label: "请选择", value: ""},
            ],
          },
          {
            label: "所在校区",
            prop: "szxym",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "所在单位",
            prop: "szxy",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "班级",
            prop: "bjmc",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "手机号码",
            prop: "yddh",
            addDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "培养层次",
            prop: "pycc",
            addDisplay: false,
            editDisplay: false,
            // search: true,
          },
        ],
      };
    },
  },
};
</script>

<style scoped>
.basic-container {
  height: 100%;
}

.basic-container::v-deep .el-card {
  overflow: auto;
  height: 101%;
}
</style>
