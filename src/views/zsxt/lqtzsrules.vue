<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @refresh-change="refresh"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="menuLeft">
                <el-button type="danger"
                           size="small"
                           icon="el-icon-delete"
                           plain
                           v-if="permission.lqtzsrules_delete"
                           @click="handleDelete">删 除
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/zsxt/lqtzsrules";
import {mapGetters} from "vuex";

export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 210,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: true,
                viewBtn: true,
                selection: true,
                column: [
                    {
                        label: "年份",
                        prop: "nf",
                        addDisplay: false,
                        editDisplay: false,
                    },
                    /*{
                        label: "前缀规则",
                        prop: "prefixRule",
                        rules: [{
                            required: true,
                            message: "请输入前缀规则",
                            trigger: "blur"
                        }]
                    },*/
                    {
                        label: "前缀",
                        placeholder: '固定文本直接输入',
                        prop: "ruleOrder",
                        type: "select",
                        multiple: true,
                        filterable: true,
                        allowCreate: true,
                        slot: true,
                        dicData: [
                            {label: "省份首字母", value: "sfszm"},
                            {label: "省份代码", value: "sfdm"},
                            {label: "学院代码", value: "xydm"},
                        ],
                        rules: [{
                            required: true,
                            message: "请输入前缀",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "年份格式",
                        prop: "yearFormat",
                        type: "number",
                        minRows: 1,
                        maxRows: 4,
                        step: 1,
                        rules: [{
                            required: true,
                            message: "请输入年份格式",
                            trigger: "change"
                        }]
                    },
                    {
                        label: "顺序号长度",
                        prop: "sequenceLength",
                        type: "number",
                        minRows: 1,
                        maxRows: 5,
                        step: 1,
                        rules: [{
                            required: true,
                            message: "请输入顺序号长度",
                            trigger: "change"
                        }]
                    },

                ]
            },
            data: []
        };
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.vaildData(this.permission.lqtzsrules_add, false),
                viewBtn: this.vaildData(this.permission.lqtzsrules_view, false),
                delBtn: this.vaildData(this.permission.lqtzsrules_delete, false),
                editBtn: this.vaildData(this.permission.lqtzsrules_edit, false)
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        rowSave(row, done, loading) {
            console.log(row.ruleOrder)
            row.ruleOrder=row.ruleOrder.join(',');
            add(row).then((res) => {
                done();
                console.log(res)
                if (res.data.code == 200) {
                    this.$message({type: "success",message: "操作成功!"});
                } else {
                    this.$message({type: "error",message: res.data.msg});
                }
                this.onLoad(this.page);
            }, error => {
                window.console.log(error);
                loading();
            });
        },
        rowUpdate(row, index, done, loading) {
            console.log(row.ruleOrder)
            // row.ruleOrder=row.ruleOrder.join(',');
            update(row).then(() => {
                done();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                window.console.log(error);
                loading();
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page);
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        refresh() {
            this.onLoad(this.page);
        },
    }
};
</script>

<style>
</style>
