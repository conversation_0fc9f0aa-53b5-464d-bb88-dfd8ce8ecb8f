<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @refresh-change="refresh"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   :upload-after="uploadAfter"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="contentForm" slot-scope="scope">
                <div class="codemirror" style="height: 150px;overflow: scroll;">
                    <!--<codemirror v-model.trim="form.dynSentence" :options="optionsSql"/>-->
                    <monaco-editor
                        v-model.trim="form.content"
                        language="html"
                        style="height: 800px"
                    />
                </div>
            </template>
            <template slot="menuLeft">
                <el-button type="danger"
                           size="small"
                           icon="el-icon-delete"
                           plain
                           v-if="permission.templates_delete"
                           @click="handleDelete">删 除
                </el-button>
            </template>
            <template slot-scope="scope" slot="menu">
<!--                <el-button icon="el-icon-magic-stick" :size="scope.size" type="text" @click.stop="design(scope.row)">设计</el-button>-->
<!--                <el-button icon="el-icon-view" :size="scope.size" type="text" @click.stop="view(scope.row)">预览</el-button>
                <el-button icon="el-icon-view" :size="scope.size" type="text" @click.stop="copy(scope.row)">复制</el-button>-->
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/zsxt/templates";
import {mapGetters} from "vuex";
import {codemirror} from 'vue-codemirror' // 引入codeMirror全局实例
import 'codemirror/mode/htmlmixed/htmlmixed.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/lib/codemirror.css' // 核心样式
import 'codemirror/theme/cobalt.css' // 引入主题后还需要在 options 中指定主题才会生效
import vueJsonEditor from 'vue-json-editor'
import MonacoEditor from "@/components/MonacoEditor";
import {toBase64} from "js-base64";

export default {
    components: {codemirror, vueJsonEditor, MonacoEditor},
    data() {
        return {
            uploadObj: {},
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 210,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: true,
                viewBtn: true,
                selection: true,
                column: [
                    {
                        label: "编码",
                        prop: "code",
                        rules: [{
                            required: true,
                            message: "请输入编码",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "名称",
                        prop: "name",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请输入名称",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "模块参数",
                        prop: "moduleParam",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请输入模块参数",
                            trigger: "blur"
                        }]
                    },

                    {
                        label: "排序",
                        prop: "sort",
                        type: "number",
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "radio",
                        dicData: [
                            {
                                value: "禁用",
                                label: "禁用"
                            },
                            {
                                value: "启用",
                                label: "启用"
                            }
                        ],
                        value: '启用',
                        rules: [{
                            required: true,
                            message: "请输入状态",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "内容",
                        prop: "content",
                        span: 24,
                        hide: true,
                        /*type: "upload",
                        accept:'ftl',
                        tip: '请上传ftl模板',
                        uploadError: (error, column) => {
                            if(error == '文件太大不符合'){
                                this.$message.error('文件大小超过限制')
                            }
                            else{
                                this.$message.error(error)
                            }
                        },
                        uploadBefore:(file, done, loading,column) =>{
                            const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
                            const isFlag = suffixName === "ftl";
                            if (!isFlag) {
                                this.$message({
                                    message: "格式不支持!",
                                    type: "error",
                                });
                                loading()  //阻断上传
                            }
                            else{
                                done()  //继续上传
                            }
                        },
                        showColumn: false,
                        propsHttp: {
                            home: window.location.origin,
                            res: "info"
                        },
                        action: "/file/upload"*/
                        minRows: 3,
                        type: "textarea",
                        rules: [{
                            required: true,
                            message: "请输入内容",
                            trigger: "blur"
                        }]
                    },
                    {
                        label: "打印机参数",
                        prop: "printerParameters",
                        span: 24,
                        minRows: 3,
                        type: "textarea",
                    },
                    {
                        label: "背景",
                        prop: "background",
                        span: 24,
                        // hide: true,
                        type: "upload",
                        listType: "picture-img",
                        accept:'image/png, image/jpeg',
                        tip: '只能上传jpg/png',
                        uploadError: (error, column) => {
                            if(error == '文件太大不符合'){
                                this.$message.error('文件大小超过限制')
                            }
                            else{
                                this.$message.error(error)
                            }
                        },
                        uploadBefore:(file, done, loading,column) =>{
                            const suffixName = file.name.substring(file.name.lastIndexOf(".") + 1);
                            const isFlag =
                                suffixName === "jpg" || suffixName === "jpeg" || suffixName === "png";
                            if (!isFlag) {
                                this.$message({
                                    message: "格式不支持!",
                                    type: "error",
                                });
                                loading()  //阻断上传
                            }
                            else{
                                done()  //继续上传
                            }
                        },
                        showColumn: false,
                        propsHttp: {
                            home: window.location.origin,
                            res: "info"
                        },
                        action: "/file/upload"
                    },

                    {
                        label: "备注",
                        prop: "bz",
                        span: 24,
                        minRows: 3,
                        hide: true,
                        type: "textarea"
                    },
                ]
            },
            data: []
        };
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.vaildData(this.permission.templates_add, false),
                viewBtn: this.vaildData(this.permission.templates_view, false),
                delBtn: this.vaildData(this.permission.templates_delete, false),
                editBtn: this.vaildData(this.permission.templates_edit, false)
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        rowSave(row, done, loading) {
            row.content = toBase64(row.content);
            add(row).then(() => {
                done();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                window.console.log(error);
                loading();
            });
        },
        rowUpdate(row, index, done, loading) {
            row.content = toBase64(row.content);
            update(row).then(() => {
                done();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                window.console.log(error);
                loading();
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page);
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        uploadAfter(res, done) {
            if (res) {
                this.uploadObj = res;
                done();
                this.$message.success("上传成功");
            }
        },
        refresh() {
            this.onLoad(this.page);
        },
        design(row) {
            let designUrl = "/templates/tmpledit"
            var routeUrl = this.$router.resolve({
                path: designUrl,
                query: {
                    tmplId: row.id
                }
            });
            window.open(routeUrl.href, "_blank");
        },
    }
};
</script>

<style>
</style>
