<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @refresh-change="refresh"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.jhk_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {add, getDetail, getList, remove, update} from "@/api/zsxt/jhk";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "批次代码",
              prop: "pcdm",
              search:true,
              rules: [{
                required: true,
                message: "请输入批次代码",
                trigger: "blur"
              }]
            },
            {
              label: "科类代码",
              prop: "kldm",
              search:true,
              rules: [{
                required: true,
                message: "请输入科类代码",
                trigger: "blur"
              }]
            },
            {
              label: "计划性质",
              prop: "jhxz",
              rules: [{
                required: true,
                message: "请输入计划性质",
                trigger: "blur"
              }]
            },
            {
              label: "投档档位",
              prop: "tddw",
              rules: [{
                required: true,
                message: "请输入投档档位",
                trigger: "blur"
              }]
            },
            {
              label: "专业代号",
              prop: "zydh",
              rules: [{
                required: true,
                message: "请输入专业代号",
                trigger: "blur"
              }]
            },
            {
              label: "专业代码",
              prop: "zydm",
              rules: [{
                required: true,
                message: "请输入专业代码",
                trigger: "blur"
              }]
            },
            {
              label: "专业代码2",
              prop: "zydm2",
              rules: [{
                required: true,
                message: "请输入专业代码2",
                trigger: "blur"
              }]
            },
            {
              label: "专业名称",
              prop: "zymc",
              rules: [{
                required: true,
                message: "请输入专业名称",
                trigger: "blur"
              }]
            },
            {
              label: "专业类型",
              prop: "zylb",
              rules: [{
                required: true,
                message: "请输入专业类型",
                trigger: "blur"
              }]
            },
            {
              label: "学制年限",
              prop: "xznx",
              rules: [{
                required: true,
                message: "请输入学制年限",
                trigger: "blur"
              }]
            },
            {
              label: "计划人数",
              prop: "jhrs",
              rules: [{
                required: true,
                message: "请输入计划人数",
                trigger: "blur"
              }]
            },
            {
              label: "计划招生书",
              prop: "jhzxs",
              rules: [{
                required: true,
                message: "请输入计划招生书",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.jhk_add, false),
          viewBtn: this.vaildData(this.permission.jhk_view, false),
          delBtn: this.vaildData(this.permission.jhk_delete, false),
          editBtn: this.vaildData(this.permission.jhk_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh() {
        this.onLoad(this.page);
      },
    }
  };
</script>

<style>
</style>
