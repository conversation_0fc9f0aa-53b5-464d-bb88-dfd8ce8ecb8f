/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios'
import {serialize} from '@/util/util'
import website from '@/config/website';
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css'
import qs from "qs";
import {RSA_SIGN} from "@/util/sign";
import {Message,} from 'element-ui';
// import {Message, Loading} from 'element-ui';

// 定义加载动画方法
// let loading;
// function startLoading() {
//     loading = Loading.service({
//         lock: true,
//         text: "正在加载资源2222",
//         spinner: 'el-icon-loading',
//         background: 'rgba(0, 0, 0, 0.7)'
//     });
// }
// function endLoading() {
//     loading.close();
// }

const instance = axios.create({
    timeout: 1000000,
    validateStatus: function (status) {
        return status >= 200 && status <= 500;
    },
    withCredentials: true,
    paramsSerializer: function (params) {
        return qs.stringify(params, {allowDots: true})
    }
});
// axios.defaults.timeout = 10000;
// // 返回其他状态吗
// axios.defaults.validateStatus = function (status) {
//     return status >= 200 && status <= 500; // 默认的
// };
// // 跨域请求，允许保存cookie
// axios.defaults.withCredentials = true;

// NProgress Configuration
NProgress.configure({
    showSpinner: false
});
// HTTPrequest拦截
instance.interceptors.request.use(config => {
    // startLoading();
    NProgress.start()
    // const meta = (config.meta || {});
    //headers中配置serialize为true开启序列化
    if (config.method === 'post' && config.headers['Content-Type'] === 'application/x-www-form-urlencoded; charset=UTF-8') {
        config.data = serialize(config.data);
    } else {
        config.headers['sign'] = RSA_SIGN(config.data ? JSON.stringify(config.data) : "{}");
    }
    return config;
}, error => {
    return Promise.reject(error)
});
// HTTPresponse拦截
instance.interceptors.response.use(res => {
    // endLoading()
    NProgress.done();

    // 开发环境输出请求信息
    if (process.env.NODE_ENV === 'development') {
        console.log("axios response", res);
    }

    const status = res.status;
    const statusWhiteList = website.statusWhiteList || [];

    //如果在白名单里则自行catch逻辑处理
    if (statusWhiteList.includes(status)) return Promise.reject(res);

    let message = '服务异常';
    let resData = res.data;
    if (resData) {
        if (resData.info) {
            message = resData.info;
        }

        //如果是401则跳转到登录页面
        if (status === 401) {
            if (process.env.NODE_ENV === 'production') {
                window.location.href = '/logout'
            }
        } else if (resData.code && (resData.code !== '00000' && resData.code !== 200 )) {
            /*Message({
                message: message,
                type: 'warning'
            });*/
        }

        if (resData.status !== undefined) {
            if (resData.status) {
                let method = res.config.method;
                switch (method) {
                    case "post":
                    case "put":
                        Message({
                            message: '操作成功',
                            type: 'success'
                        });
                        break;
                    case "delete":
                        Message({
                            message: '删除成功',
                            type: 'success'
                        });
                        break;
                }
            } else {
                Message({
                    message: resData.msg,
                    type: 'error'
                });
            }
        }
    }

    // 如果请求为非200否者默认统一处理
    if (status !== 200) {
        Message({
            message: message,
            type: 'error'
        })
        return Promise.reject(new Error(message))
    }

    return res;
}, error => {
    // endLoading()
    NProgress.done();
    return Promise.reject(new Error(error));
})

export default instance;
