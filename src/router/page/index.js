import Layout from '@/page/index/'

export default [
    {
        path: '/login',
        name: '登录页',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/login/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    // {
    //     path: '/mobile/',
    //     name: '移动预警记录',
    //     component: () =>
    //         import( /* webpackChunkName: "page" */ '@/views/mobile/index'),
    //     meta: {
    //         keepAlive: true,
    //         isTab: false,
    //         isAuth: false
    //     }
    // },
    {
        path: '/mobile/warning/:taskRecordId/:roleId',
        name: '移动预警记录',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/mobile/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/mobile/details/:list',
        name: '移动名单详情',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/mobile/details/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/mobile/handle/index',
        name: '移动处理',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/mobile/handle/index'),
        meta: {
            keepAlive: false,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/mobile/xueshenghuaxiang/index',
        name: '学生画像列表',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/mobile/xueshenghuaxiang/index'),
        meta: {
            keepAlive: false,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/lock',
        name: '锁屏页',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/lock/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/404',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/error-page/404'),
        name: '404',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }

    },
    {
        path: '/403',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/error-page/403'),
        name: '403',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/500',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/error-page/500'),
        name: '500',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/',
        // component: () =>
        //     import( /* webpackChunkName: "page" */ '@/page/zsxtreception/dataimport'),
        name: '主页',
        redirect: '/dataimport/index',
        // meta: {
        //     keepAlive: true,
        //     isTab: true,
        //     isAuth: true
        // }
    },
    // {
    //     path: '/dataimport/index',
    //     name: '数据导入',
    //     component: () =>
    //         import( /* webpackChunkName: "page" */ '@/page/zsxtreception/dataimport'),
    // },
    /*{
        path: '/',
        name: '主页',
        // redirect: '/data-screen',
        redirect: '/data-warning-information',
    },*/
    {
        path: '/myiframe',
        component: Layout,
        redirect: '/myiframe',
        children: [{
            path: ":routerPath",
            name: 'iframe',
            component: () =>
                import( /* webpackChunkName: "page" */ '@/components/iframe/main'),
            props: true
        }]

    },
    {
        path: '/ksgl/print',
        name: '通知书打印',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/ksgl/print')
    },
    {
        path: '/templates/tmpledit',
        name: '模板设计',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/zsxt/tmpledit')
    },
    {
        path: '/bigscreen/designer',
        name: '大屏设计',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/report/bigscreen/designer/index')
    },
    {
        path: '/bigscreen/viewer',
        name: '大屏预览',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/report/bigscreen/viewer/index')
    },
    {
        path: '/bigscreen/layout',
        name: '报表设计',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/report/bigscreen/designer/layout')
    },
    {
        path: '/bigscreen/layoutviewer',
        name: '报表预览',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/report/bigscreen/viewer/layoutViewer')
    },
    {
        path: '/bigscreen/detail',
        name: '数据钻取',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/report/bigscreen/viewer/data-detail')
    },
    {
        path: '/bigscreen/user-detail',
        name: '学生/教师信息详情',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/report/bigscreen/viewer/user-detail')
    },
    {
        path: '*',
        redirect: '/404'
    },
    // {
    //     path: '/dataPortal',
    //     name: '数据门户',
    //     component: () =>
    //         import( '@/page/dataPortal')
    // },
    {
        path: '/data-analysis',
        name: '数据分析',
        component: () =>
            import('@/page/dataPortal/data-analysis'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-analysis-warning/:reportId',
        name: '预警分析',
        component: () =>
            import('@/page/dataPortal/data-analysis-warning'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },

    {
        path: '/data-analysis/detail',
        name: '数据分析详情',
        component: () =>
            import('@/page/dataPortal/data-analysis-detail'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-screen',
        name: '数据大屏',
        component: () =>
            import('@/page/dataPortal/data-screen'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-analysis',
        name: '数据分析',
        component: () =>
            import('@/page/dataPortal/data-analysis'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-analysis/detail',
        name: '数据分析详情',
        component: () =>
            import('@/page/dataPortal/data-analysis-detail'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-analysis/ifram',
        name: '数据分析链接',
        component: () =>
            import('@/page/dataPortal/data-analysis-ifram'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-decision',
        name: '数据决策',
        component: () =>
            import('@/page/dataPortal/data-decision'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-warning-information',
        name: '预警信息',
        component: () =>
            import('@/page/dataPortal/data-warning-information'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-warning-special-list',
        name: '特殊名单',
        component: () =>
            import('@/page/dataPortal/data-warning-special-list'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-warning-statistics',
        name: '预警信息',
        component: () =>
            import('@/page/dataPortal/data-warning-statistics'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-student-portrait/:employeeType/:reportId',
        name: '画像信息',
        component: () =>
            import('@/page/dataPortal/data-student-portrait'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/data-student-portrait/personal',
        name: '个人画像',
        component: () =>
            import('@/page/dataPortal/data-personal-portrait'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/mobiles/portrait',
        name: '移动画像',
        component: () =>
            import('@/views/mobile/details/portrait'),
        meta: {
            isTab: false,
            isAuth: false
        }
    },
    // {
    //     path: '/zsxt-index',
    //     name: '招生首页',
    //     component: () =>
    //         import('@/page/zsxtreception/index'),
    //     meta: {
    //         isTab: true,
    //         isAuth: false
    //     }
    // },
    // {
    //     path: '/zsxt-analysis',
    //     name: '招生数据分析',
    //     component: () =>
    //         import('@/page/zsxtreception/analysis'),
    //     meta: {
    //         isTab: false,
    //         isAuth: false
    //     }
    // },
]
