export default {
  tip: '提示',
  title: '招生系统',
  logoutTip: '退出系统, 是否继续?',
  submitText: '确定',
  cancelText: '取消',
  search: '请输入搜索内容',
  menuTip: '没有发现菜单',
  feedback: '意见反馈',
  submit: '提交',
  wel: {
    info: '你好，张萌萌，欢迎使用招生系统！',
    dept: '今天是2020年6月5日',
    team: '认证应用数',
    project: '系统访问人数',
    count: '账号总数',
    data: {
      subtitle: '实时',
      column1: '分类统计',
      column2: '附件统计',
      column3: '文章统计',
      key1: '分',
      key2: '附',
      key3: '评',
      text1: '当前分类总记录数',
      text2: '当前上传的附件数',
      text3: '评论次数'
    },
    data2: {
      column1: '今日注册',
      column2: '今日登录',
      column3: '今日订阅',
      column4: '今日评论'
    },
    data3: {
      column1: '转化率（日同比 28%）',
      column2: '签到率（日同比 11%）',
      column3: '签到率（日同比 11%）'
    },
    data4: {
      column1: '错误日志',
      column2: '数据展示',
      column3: '权限管理',
      column4: '用户管理'
    },
    table: {
      rw: '工作任务',
      nr: '工作内容',
      sj: '工作时间'
    }
  },
  route: {
    settings: '系统设置',
    setting: '个人设置',
    detail: '详情页',
    info: '个人信息',
    website: '官网',
    dashboard: '首页',
    more: '更多',
    tags: '标签',
    store: '本地存储',
    api: '全局函数',
    logs: '日志监控',
    table: '表格',
    form: '表单',
    data: '数据展示',
    permission: '权限',
    top: '返回顶部',
    affix: '图钉',
    crudForm: '表格表单',
    cache: '页面缓冲',
    error: '异常页面',
    test: '测试页面'
  },
  login: {
    title: '登录 ',
    info: '通用管理系统快速开发框架',
    username: '请输入账号',
    password: '请输入密码',
    wechat: '微信',
    qq: 'QQ',
    phone: '请输入手机号',
    code: '请输入验证码',
    submit: '登录',
    faceLogin: '刷脸登录',
    userLogin: '账号密码',
    phoneLogin: '扫码登录',
    thirdLogin: '第三方登录',
    msgText: '发送验证码',
    msgSuccess: '秒后重发',
  },
  navbar: {
    setting: '个人设置',
    logOut: '退出登录',
    userinfo: '个人信息',
    dashboard: '首页',
    lock: '锁屏',
    bug: '没有错误日志',
    bugs: '条错误日志',
    screenfullF: '退出全屏',
    screenfull: '全屏',
    language: '中英文',
    notice: '消息通知',
    theme: '主题',
    color: '换色'
  },
  tagsView: {
    search: '搜索',
    menu: '更多',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  }
}
