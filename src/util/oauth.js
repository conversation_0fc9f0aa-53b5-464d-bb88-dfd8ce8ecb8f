import {oauthClient, GetToken} from '@/api/user';
// import {baseUrl} from '@/config/env';
import {setStore, removeStore} from '@/util/store';
import {Message} from 'element-ui'
import {setUid, getUid, setToken, removeUid} from '@/util/auth';

export function toLogin(redirectPath) {  
  // redirectPath = 'http://182.92.64.176/#' + redirectPath
  redirectPath = '/#' + redirectPath

  // uid + url
  const uid = getUid()
  // alert(uid)
  if(!uid) {
    oauthClient({service: redirectPath}).then(res => {
      const data = res.data.info
      if(res.data.code == '00000') {      
        // setStore({name: 'uid', content: data.uid})
        setUid(data.uid)
        removeStore({name: "user-info"})  
        window.location.href = data.url
      } else {
        // console.log(res.data.info);
        Message.error(res.data.info)        
      }
    })
  }  
}

export function getPassToken(next) {
  const uid = getUid()  
  if(uid) {       
    GetToken({uid}).then(res => {
      // console.log("res",res);
      
      if(res.data.code == '00000') {
        setStore({name: 'user-info', content: res.data.info})
        // window.sessionStorage.setItem('user-info', JSON.stringify(res.data.info));
        setToken(res.headers.token)        
        // setStore({name: "tokenFlag", content: 1})        
        removeUid()
        next()
      } else {
        removeUid()
        next()
      }
    })  
  }
}