<template>
  <section>
    <div class="wrapAll clearfix">
      <topNav></topNav>
      <iframe :src="src" frameborder="0"></iframe>
      <!-- <foot></foot> -->
    </div>
  </section>
</template>

<script>
import widget from "@/views/report/bigscreen/designer/widget/temp";
import viewer from "@/views/report/bigscreen/viewer/index"
import {GetAnalysisList} from "@/api/analysisItem";
import topNav from "./component/nav";
import foot from "./component/foot";

export default {
  name: "dataAnalysisDetail",
  components: {
    widget,
    viewer,
    topNav,
    foot
  },
  data() {
    return {
      bigScreenStyle: {},
      widgets: [],
      params: {},
      liList: [],
      reportId:'',
      src:``,
    }
  },
  watch: {},
  mounted() {
    if (this.$route.query.param) {
      this.params = JSON.parse(this.$route.query.param);
    }
    this.onLoad(this.params);
  },
  methods: {
    async onLoad(item) {
        this.src = item.url;
    },
  }
}

</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style scoped lang="scss">
.footer {
  width: 100%;
  line-height: 30px;
  font-size: 12px;
  color: #999;
  width: 100%;
  text-align: center;
  position: absolute;
  left: 0;
  bottom: 0px;
  z-index: 99;
}

.list {
  width: 200px;
  border: 1px solid;
  float: left
}
iframe{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
}
</style>