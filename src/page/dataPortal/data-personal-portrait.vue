<template>
  <section>
    <div class="wrapAlls">
      <topNav></topNav>
      <div class="boxs">
        <div class="content">
          <viewer class="viewer" :report-id="this.reportId" :humancode="this.humancode"></viewer>
        </div>
        <footer class="foot">
          <span>
            {{ dbxx }}
          </span>
        </footer>
      </div>
    </div>
  </section>
</template>

<script>
import topNav from "./component/nav";
import { GetSysParamNoLogin } from "@/api/sysParam";
import viewer from "@/views/report/bigscreen/viewer/layoutViewer.vue";
export default {
  name: "GitSanythReportUiDataWarning",
  components: {
    topNav,
    viewer
  },
  data() {
    return {
      dbxx: ``,
      reportId: '',
      humancode: '',
    };
  },
  computed: {
    
  },
  created() {
    this.footP(this.footParam());

    this.getcharts()
  },
  mounted() {},

  methods: {
    getcharts() {
      document.title = '个人画像';
      let param = JSON.parse(this.$route.query.param)
      this.reportId = param.id;
      this.humancode = param.humancode;
    },
    footP(param) {
      GetSysParamNoLogin(param).then((res) => {
        this.dbxx = res.data.info.value;
      });
    },
    footParam() {
      return {
        idOrNameOrType: "dbxx",
      };
    },
  },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
.viewer{
  width: 100%;
  // height: 100%;

  // height: 800px;
}
.wrapAlls {
  // height: calc(100% - 60px);
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  //   bottom: -60px;
  // height: 100vh;
  overflow: hidden;
}
.boxs {
  height: calc(100vh - 60px);
  overflow-y: scroll;
}
.foot {
  background: rgb(240, 242, 245);
  font-size: 12px;
  line-height: 30px;
  color: rgb(147, 150, 149);
  text-align: center;
}
.nav {
  height: 50px;
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #E9E9E9;
}
.nav_h1 {
  line-height: 50px;
  font-size: 18px;
  font-weight: bold;
  margin-left: 50px;
}
.nav_h2 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 50px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h3 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h4 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
}
.content {
  width: 100%;
  // height: 100%;
  min-height: calc(100vh - 90px);
  //padding: 15px;
  background: rgb(236, 235, 235);
}
</style>