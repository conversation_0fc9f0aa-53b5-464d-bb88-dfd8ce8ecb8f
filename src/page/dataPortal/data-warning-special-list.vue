<template>
  <section>
    <div class="wrapAlls">
      <topNav></topNav>
      <div class="boxs">
        <div class="nav">
          <span class="nav_h2">预警信息</span> <span class="nav_h5">/</span>
          <span class="nav_h3">预警查看</span> <span class="nav_h5">/</span>
          <span class="nav_h4">特殊名单</span>
        </div>
        <div class="content">
          <div class="contentinner">
            <div
              :style="{
                padding: ' 0 30px 0px 30px ',
                height: '60px',
                'line-height': '60px'
              }"
            >
              <div
                  :style="{
                    'border-bottom': '1px solid #EBEEF5',
                    height: '60px',
                    'line-height': '60px'
              }">
                <span class="content_titleLeft">名单列表</span>
              </div>
            </div>
            <div class="content_body">
              <avue-crud :data="data"
                         :option="option"
                         :page.sync="page"
                         :table-loading="loading"
                         @size-change="sizeChange"
                         @current-change="currentChange"
                         @row-save="addUserHandle"
                         @row-del="deleteHandlelist"
                         @refresh-change="refresh"
                         @search-change="searchChange">
                <template slot="index" slot-scope="{row,index}">
                  <p :style="{textAlign: 'center'}">{{index+1}}</p>
                </template>
                <template slot="rolename" slot-scope="scope">
                  <el-tag style="margin-right: 5px"
                          v-for="item in scope.row.rolename"
                          :key="item.value"
                          size="medium">{{ item.label }}
                  </el-tag>
                </template>
              </avue-crud>
            </div>
          </div>
        </div>
        <footer class="foot">
          <span>
            {{ dbxx }}
          </span>
        </footer>
      </div>
    </div>
  </section>
</template>

<script>
import topNav from "./component/nav";
import {GetSysParamNoLogin} from "@/api/sysParam";
import {warnspecialListdelete, warnspecialListPage} from "@/api/specialList";

export default {
  name: "GitSanythReportUiDataWarningSpecialList",
  components: {
    topNav,
  },
  data() {
    return {
      dbxx: ``,
      activeName1: '1',
      loading: false,
      queryParam: {},
      value: "",
      page: {
        currentPage: 1,
        total: 0,
        pageSize: 10,
      },
      data: [],
    };
  },
  computed: {
    option() {
      return {
        // dialogWidth: 700,
        size: "mini",
        border: true,
        // tabs: true,
        stripe: true,
        dialogClickModal: false,
        // menu:false,
        menuWidth: 230,
        align: "center",
        delBtnText: '删除',
        // span: 24,
        // menuType: "button",
        editBtn: false,
        // delBtn: false,
        addBtn: false,

        refreshBtn: false,
        searchShowBtn: false,
        columnBtn: false,
        searchBtn: true,
        searchShow: true,
        column: [
          {
            label: '预警规则',
            prop: "ruleid",
            search: true,
            type: "select",
            // multiple: true,
            dicMethod: "post",
            dicUrl: '/dataWarning/rule/list',
            props: {
              label: "setName",
              value: "id",
            },
          },
          {
            label: "登录账号",
            prop: "humancode",
            search: true,
            // overHidden:true,
            // hide: true
          },
          {
            label: "人员名称",
            prop: "humanname",
            search: true,
            // overHidden:true,
            // hide: true
          },
          {
            label: "所属机构",
            prop: "organizationnames",
            // addDisplay: false,
            // editDisplay: false,
            slot: true,
            align: "left",
            type: "tree",
            dicMethod: "post",
            dicUrl: "/sytSysOrganization/treeListJsonArray",
            // dicData: this.orgList,
            props: {
              value: "id",
            },
            span: 12,
            // search: true,
          },
        ],
      };
    },
  },
  created() {
    this.onLoad();
    this.footP(this.footParam());
  },
  mounted() {},

  methods: {
    onLoad() {
      document.title = '特殊名单';
      this.loading = true;
      let context = this.pageParam(this.queryParam);
      warnspecialListPage(context).then((res) => {
        this.loading = false;
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.data = data.records;
        // console.log(this.data)
        this.data.forEach((item) => {
          item.rolename = item.role;
          /*let arr = [];
          item.role.forEach((item1) => {
            arr.push(item1.value);
          });
          this.$set(item, "role", arr);*/
        });
      });
    },
    footP(param) {
      GetSysParamNoLogin(param).then((res) => {
        this.dbxx = res.data.info.value;
      });
    },
    footParam() {
      return {
        idOrNameOrType: "dbxx",
      };
    },
    //搜索
    searchChange(param, done) {
      this.queryParam = param;
      this.onLoad()
      done()
    },
    refresh() {
      this.onLoad()
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad();
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad();
    },
    pageParam(queryParam) {
      this.queryParam.employeeType = this.$route.params.employeeType
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: queryParam
      }
    },
    //删除名单
    deleteHandlelist(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        warnspecialListdelete({"id": row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: "success", message: "删除成功"});
            // this.warnrulelistget()
            this.onLoad(this.pageParam())
          }
        })
      }).catch(() => {
        console.log('已取消删除操作')
      });
    },
  },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
//::v-deep .avue-crud__menu {
//    display: none;
//}
.wrapAlls {
  // height: calc(100% - 60px);
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  //   bottom: -60px;
  // height: 100vh;
  height: calc(100vh - 60px);
  background: rgba(240, 239, 239, 0.7);
  overflow: hidden;
}
.boxs {
  height: calc(100vh - 60px);
  overflow-y: auto;
}
.foot {
  background: rgba(240, 239, 239, 0.7);
  font-size: 12px;
  line-height: 30px;
  color: rgb(147, 150, 149);
  text-align: center;
}
.nav {
  height: 50px;
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #E9E9E9;
}
.nav_h1 {
  line-height: 50px;
  font-size: 18px;
  font-weight: bold;
  margin-left: 50px;
}
.nav_h2 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 50px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h3 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h4 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
}
.nav_h5{
	font-size: 14px;
  color: rgb(147, 150, 149);
}
.content {
  width: 100%;
  // height: 100%;
  min-height: calc(100vh - 150px);
  padding: 15px;
  background: rgba(240, 239, 239, 0.7);
}
.wrapAll {
  padding: 0 !important;
}
.contentinner {
  position: relative;
  height: 100%;
  border-radius: 5px;
  // min-height: calc(100vh - 150px);
  width: 100%;
  background-color: #fff;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.content_title {
  height: 60px;
  border-radius: 5px;
  // border-bottom: 1px solid rgb(228, 225, 225);
  margin: 0 30px 0 30px;
  line-height: 60px;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.content_titleLeft {
  font-size: 20px;
  font-weight: bold;
}
.content_titleRigth {
  // float: right;
  font-size: 14px;
  position: absolute;
  right: 55px;
}
.content_body {
  margin: 15px 30px 0 30px;
}
.content_span1 {
  font-size: 16px;
}
.content_span2 {
  display: inline-block;
  width: 100px;
  line-height: 30px;
  background-color: rgb(148, 151, 151);
  border-radius: 3px;
  color: #fff;
  text-align: center;
  margin-right: 5px;
}
.content_span3 {
  display: inline-block;
  width: 100px;
  line-height: 30px;
  background-color: rgba(233, 233, 233, 0.795);
  border-radius: 3px;
  color: #333;
  text-align: center;
  margin-right: 5px;
}
.content_line {
  margin-bottom: 10px;
}
.content_select {
  margin-right: 5px;
}
.content_picker {
  height: 30px;
  // margin-top: 10px;
  vertical-align: top;
}
::v-deep .avue-crud .el-table th {
  font-size: 14px;}
::v-deep .avue-crud .el-table td {
font-size: 12px;}

::v-deep .el-input__icon {
  line-height: 22px;
}
::v-deep .el-range-separator {
  line-height: 22px;
  width: 20px;
}
.content_input {
  display: inline-block;
  width: 200px;
  margin-right: 5px;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0;
}
::v-deep .avue-crud__menu {
  display: none !important;
}
//::v-deep .el-card__body .el-collapse-item__wrap{
//  display: none !important;
//}
</style>
