<template>
	<!-- <div>数据决策</div> -->
	<section>
		<div class="wrapAll clearfix">
			<topNav></topNav>
			<div class="centers" v-if="showed">
<!-- 				<div class="analyseSelectWrap">
						<p class="text1">大数据应用与决策</p>
						<p class="text2">基于校级大数据分析平台，通过建设符合本校情的各场景化应用，为学校管理与服务提供智能化的决策依据，完成数据价值转换。</p>		
				</div> -->
				<div class="con_head">
					<h1>大数据应用与决策</h1>
					<p>基于校级大招生系统，通过建设符合本校情的各场景化应用，为学校管理与服务提供智能化的决策依据，完成数据价值转换。</p>
				</div>
				<div class="analyseContent">
					<el-row :gutter="28">
						<el-col :span="4" v-for="entry in entryData" :key="entry.id" >
							<div class="analyse" @click="iframes(entry)">
								<div class="analyseIcon"><img :src= entry.src alt=""></div>
								<div class="analyseName">{{entry.name}}</div>
								<div class="analyseDetial">
									{{entry.desc}}
								</div>
							</div>
						</el-col>
					</el-row>
				</div>
				<foot></foot>
			</div>
			<!-- <foot></foot> -->
		</div>
	</section>
</template>

<script>
import topNav from "./component/nav";
import foot from "./component/foot";
export default {
	components: {
			topNav,
			foot
		},
	data() {
			return {
				show1: false,
				show2: false,
				selectSkin: 1,
				showed: true,
				current: "",
				// local: "http://localhost:8080/static/bddy-data/start.html#",
				local: "http://" + window.location.host + "/static/bddy-data/start.html#",
				entryData: []
			};
		},
		created() {
			this.setLocal();
		},
		methods: {
			setLocal() {
				let datas = [{
						id: 1,
						name: "学生画像",
						desc: "提供全方面各类学生事务数据的分析报表服务",
						src: "/img/code/u88.png",
						Shows: false,
						href: this.local + "id=mv8xhy&p=%E5%AD%A6%E7%94%9F%E7%BE%A4%E4%BD%93%E7%94%BB%E5%83%8F&sc=1&c=1",
					},
					{
						id: 2,
						name: "教师画像",
						desc: "提供全方面各类学生事务数据的分析报表服务",
						src: "/img/code/u85.png",
						Shows: false,
						href: this.local + `id=p196lh&p=%E6%95%99%E5%B8%88%E7%94%BB%E5%83%8F%EF%BC%88%E8%BE%85%E5%AF%BC%E5%91%98%EF%BC%89&sc=1&c=1`,

					},
					{
						id: 3,
						name: "学业预警",
						desc: "提供全方面各类学生事务数据的分析报表服务",
						src: "/img/code/u86.png",
						Shows: false,
						href: this.local + `id=gnw8fu&p=%E9%A2%84%E8%AD%A6%E7%BB%9F%E8%AE%A1&sc=1&c=1`,
					},
					{
						id: 4,
						name: "考勤预警",
						desc: "提供全方面各类学生事务数据的分析报表服务",
						src: "/img/code/u83.png",
						Shows: false,
						href: this.local + `id=6gdqcb&p=%E9%A2%84%E8%AD%A6%E5%A4%84%E7%90%86%EF%BC%88%E7%AE%80%E5%8C%96%E7%89%88%EF%BC%89&sc=1&c=1`,
					},
					{
						id: 5,
						name: "消费预警",
						desc: "提供全方面各类学生事务数据的分析报表服务",
						src: "/img/code/u87.png",
						Shows: false,
						href: this.local + `id=be7wkh&p=%E9%A2%84%E8%AD%A6%E8%AE%BE%E7%BD%AE&sc=1&c=1`,

					},
					{
						id: 6,
						name: "精准资助",
						desc: "提供全方面各类学生事务数据的分析报表服务",
						src: "/img/code/u84.png",
						Shows: false,
						href: this.local + `id=yljp2b&p=%E7%B2%BE%E5%87%86%E8%B5%84%E5%8A%A9%E8%BE%85%E5%8A%A9%E5%88%86%E6%9E%90&sc=1&c=1`,

					},
				];
				this.entryData = datas;
			},
			iframes(item){
				window.open(item.href)
			}
		},
}
</script>

<style scoped>
	@import url(../../styles/assets/css/reset.css);
	@import url(../../styles/assets/css/head.css);
	@import url(../../styles/assets/css/index.css);
</style>
<style scoped>
	.footer {
		width: 100%;
		line-height: 30px;
		font-size: 12px;
		color: #999;
		width: 100%;
		text-align: center;
		position: absolute;
		left: 0;
		bottom: 0px;
		z-index: 99;
	}
	body {
		overflow: scroll;
	}
	img {
		width: 100%;
		height: 100%;
	}
	.wrapAll{
		width: 100%;
	}
	.centers{
		width: 80%;
		min-width: 1200px;
		margin: auto;
		margin-bottom: 50px;
		height: calc(100vh - 200px);
	}
	.analyseContent{
		width: 100%;
	}
	.analyseIcon {
		width: 35%;
		margin: auto;
	}
	.analyse {
		width: 100%;
		height: 32vh;
		min-height: 240px;
		cursor: pointer;
		position: relative;
		text-align: center;
		padding-top: 26%;
		background: rgba(255, 255, 255, 1);
		border-radius: 8px;
		box-shadow: 3px 3px 7px rgba(30, 48, 76, 0.1);
		overflow: hidden;
	}
	.analyse:hover {
		width: 120%;
		height: 37vh;
		min-height: 270px;
		border: 4px solid rgba(39, 178, 180, 1);
		transform: translate(-9%, -2vh);
	}
	.analyse:hover i {
		line-height: 200px;
		font-size: 130px;
	}
	.analyse:hover .analyseName {
		height: 70px;
		line-height: 70px;
		font-size: 28px;
		font-weight: bold;
		text-align: center;
		color: #333;
	}
	.analyse:hover .analyseDetial {
		margin: auto;
		width: 75%;
		height: 60px;
		font-size: 14px;
		color: #999;
		padding-right: 5px;
		display: -webkit-box;
		/* 弹性盒模型*/
		-webkit-box-orient: vertical;
		/* 文字垂直排列 */
		-webkit-line-clamp: 2;
		/*文字显示的行数*/
		overflow: hidden;
		/*超出部分溢出隐藏*/
	}
	.analyseName {
		height: 50px;
		line-height: 50px;
		font-size: 150%;
		font-weight: bold;
		text-align: center;
		color: #333;
		margin-top:0.4em;
		margin-bottom:5px;
		/* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
	}
	.analyse i {
		line-height: 170px;
		color: rgba(39, 178, 180, 1);
		text-align: center;
		font-size: 100px;

	}

	.analyseDetial {
		margin: auto;
		width: 65%;
		font-size: 12px;
		color: #999;
		padding-right: 5px;
		display: -webkit-box;
		text-align: left;

		/* 弹性盒模型*/
		-webkit-box-orient: vertical;
		/* 文字垂直排列 */
		-webkit-line-clamp: 2;
		/*文字显示的行数*/
		overflow: hidden;
		/*超出部分溢出隐藏*/
	}
	.analyseName{
		border-bottom: none !important;
	}
	.con_head {
		text-align: center;
	}
	
	.con_head h1 {
		font-size: 36px;
		padding: 20px;
		color: #fff;
	}
	
	.con_head p {
		font-size: 13px;
		color: rgba(255, 255, 255, .4);
		width: 80%;
		padding: 20px 0 50px 0;
		margin: auto;
	}
	
</style>
