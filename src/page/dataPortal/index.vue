<template>

</template>

<script>
import {getEMenuNologin} from "@/api/user";

export default {
  data() {

  },
  created(){
    this.onLoad();
  },
  methods: {
    onLoad(){
      getEMenuNologin().then((res)=>{
        this.menuList = res.data.info;
        console.log(`this.menuList`, this.menuList);
        if (this.menuList.length > 0) {
          this.$router.push(this.menuList[0]);
        } else {
          this.$router.push("/");
        }
      });
      this.$store.dispatch("GetUserInfo").then(() => {});
    },
  },
}
</script>

<style scoped>

</style>