<template>
  <section>
    <div class="wrapAlls">
      <topNav></topNav>
      <div class="boxs">
        <div class="nav">
          <span class="nav_h2">预警统计</span> <span class="nav_h5">/</span>
          <span class="nav_h4">按预警类型统计</span>
        </div>

        <div class="content">
          <div class="content_title">
            <span class="head_style">
              <img width="50" src="../../styles/assets/image/u18.png">
            </span>
              <div class="title_left">
                  <p class="title_left_p1">预警结果数据统计</p>
                  <p class="title_left_p2">统计范围：截至到当前全部数据</p>
              </div>
              <div class="title_right">
<!--                  <span>预警数据：</span>-->
<!--                  <el-select-->
<!--                        class="content_select"-->
<!--                        v-model="value"-->
<!--                        placeholder="请选择"-->
<!--                    >-->
<!--                        <el-option-->
<!--                        v-for="item in options"-->
<!--                        :key="item.value"-->
<!--                        :label="item.label"-->
<!--                        :value="item.value"-->
<!--                        >-->
<!--                        </el-option>-->
<!--                    </el-select>-->
                    <el-date-picker
                        class="content_picker"
                        v-model="value"
                        type="daterange"
                        align="right"
                        unlink-panels
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :picker-options="pickerOptions"
                        @change="changetime">
                    </el-date-picker>
                    <el-button type="primary" @click="searchtime">查询</el-button>
                    <el-button type="primary" @click="crudexport">导出</el-button>
              </div>
          </div>
          <div class="contentinner">
            <div class="content_body">
                <div class="content_body_box">
                    <span class="content_body_span">按预警类型统计</span>
<!--                    <el-select-->
<!--                        class="content_select"-->
<!--                        v-model="value"-->
<!--                        placeholder="请选择"-->
<!--                    >-->
<!--                        <el-option-->
<!--                        v-for="item in options"-->
<!--                        :key="item.value"-->
<!--                        :label="item.label"-->
<!--                        :value="item.value"-->
<!--                        >-->
<!--                        </el-option>-->
<!--                    </el-select>-->
                </div>
              <avue-crud
                :data="data"
                :option="option"
                :page.sync="page"
              >
                <template slot="index" slot-scope="{row,index}">
                  <p :style="{textAlign: 'center'}">{{index+1}}</p>
                </template>
              </avue-crud>
<!--                <el-dialog :title="caseResultTitle" :modal="false" :visible.sync="dialogHandle" width="60%">-->
<!--                    <avue-crud-->
<!--                      :data="data"-->
<!--                      :option="option"-->
<!--                      :page.sync="page1"-->
<!--                      @size-change="sizeChange"-->
<!--                      @current-change="currentChange"-->
<!--                    ></avue-crud>-->
<!--                  &lt;!&ndash; <span slot="footer" class="dialog-footer">-->
<!--                    <el-button type="primary" @click="dialogHandle = false">关闭</el-button>-->
<!--                  </span> &ndash;&gt;-->
<!--                </el-dialog>-->
            </div>
          </div>
        </div>
        <footer class="foot">
          <span>
            {{ dbxx }}
          </span>
        </footer>
      </div>
    </div>
  </section>
</template>

<script>
import topNav from "./component/nav";
import {getStatisticsData} from "@/api/dataWarningWarnInfo";
import {GetSysParamNoLogin} from "@/api/sysParam";

export default {
  name: "GitSanythReportUiDataWarning",
  components: {
    topNav,
  },
  data() {
    return {
      dbxx: ``,
      dialogHandle: false,
      // caseResultTitle: `111`,
      queryParam:{},
      value: "",
      page: {
        currentPage: 1,
        total: 5,
        pageSize: 20,
      },
      data: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      option: {
        searchShowBtn:false,
        menu: false,
        align: "left",
        menuAlign: "center",
        border: true,
        addBtn: false,
        searchBtn: false,
        refreshBtn: false,
        columnBtn: false,
        stripe: true,
        column: [
          {
            label:'序号',
            prop:'index',
            width: 50,
            fixed:true
          },
          {
            label: "预警类型",
            prop: "warnType",
          },
          {
            label: "预警人数",
            prop: "rs",
          },
          {
            label: "预警人次",
            prop: "rc",
          },
          {
            label: "总人数",
            prop: "zrs",
          },
          {
            label: "总人次",
            prop: "zrc",
          },
          {
            label: "预警人数百分比",
            prop: "yjrsb",
          },
          {
            label: "预警人次百分比",
            prop: "yjrcb",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    // this.onLoad(this.pageParam());
    this.footP(this.footParam());
  },
  mounted() {},

  methods: {
    footP(param) {
      GetSysParamNoLogin(param).then((res) => {
        this.dbxx = res.data.info.value;
      });
    },
    footParam() {
      return {
        idOrNameOrType: "dbxx",
      };
    },
    getList(param) {
      document.title = '预警统计';
      getStatisticsData(param).then(res => {
        const data = res.data.info;
        this.page.currentPage = 1;
        this.page.total = 1;
        this.page.pageSize = 20;
        this.data = data;
      })
    },
    changetime(){
      if( this.value !== null ) {
        let a = new Date(this.value[0]);
        let b = new Date(this.value[1]);
        let startM = (a.getMonth() + 1) < 10 ? '0' + (a.getMonth() + 1) : (a.getMonth() + 1)
        let startD = a.getDate() < 10 ? '0' + a.getDate() : a.getDate()
        let endM = (b.getMonth() + 1) < 10 ? '0' + (b.getMonth() + 1) : (b.getMonth() + 1)
        let endD = b.getDate() < 10 ? '0' + b.getDate() : b.getDate()
        let start = a.getFullYear() + '-' + startM + '-' + startD;
        let end = b.getFullYear() + '-' + endM + '-' + endD;
        this.queryParam.startTime = start;
        this.queryParam.endTime = end;
      } else {
        this.queryParam.startTime = '';
        this.queryParam.endTime = '';
      }
    },
    searchtime(){
        this.getList(this.queryParam)
    },
    crudexport(){
      window.open(`/dataWarning/warnInfo/exportStatisticsData`);
    },
    // sizeChange(val) {
    //   this.page.currentPage = 1;
    //   this.page.pageSize = val;
    //   this.getList(this.pageParam(this.queryParam));
    // },
    // currentChange(val) {
    //   this.page.currentPage = val;
    //   this.getList(this.pageParam(this.queryParam));
    // },
    // pageParam(queryParam) {
    //   return {
    //     page: this.page.currentPage,
    //     pageSize: this.page.pageSize,
    //     queryParam: queryParam
    //   }
    // },
  },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
::v-deep .avue-crud__menu {
  display: none;
}
.content_body_box{
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    font-weight: bold;
	border-bottom: 1px solid #EBEEF5;
}
.content_body_span{
    margin-right: 40px;
}
.wrapAlls {
  // height: calc(100% - 60px);
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  height: calc(100vh - 60px);
  //   bottom: -60px;
  // height: 100vh;
  overflow: hidden;
  background: #F0F2F5;
}
.boxs {
  height: calc(100vh - 60px);
  overflow-y: auto;
  background: #F0F2F5;
}
.foot {
  //background: rgba(240, 239, 239, 0.7);
  background: rgb(240, 242, 245);
  font-size: 12px;
  line-height: 30px;
  color: rgb(147, 150, 149);
  text-align: center;
}
.nav {
  height: 50px;
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #E9E9E9;
}
.nav_h1 {
  line-height: 50px;
  font-size: 18px;
  font-weight: bold;
  margin-left: 50px;
}
.nav_h2 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 50px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h3 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h4 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
}
.nav_h5{
	font-size: 14px;
  color: rgb(147, 150, 149);
}
.content {
  width: 100%;
  // height: 100%;
  min-height: calc(100vh - 150px);
  padding: 15px;
  background: rgba(240, 239, 239, 0.7);
}
.wrapAll {
  padding: 0 !important;
}
.contentinner {
  position: relative;
  height: 100%;
  border-radius: 5px;
  // min-height: calc(100vh - 150px);
  width: 100%;
  background-color: #fff;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.content_title {
  height: 90px;
  margin-bottom: 15px;
  border-radius: 5px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.head_style {
	display: inline-block;
	float: left;
	width:90px;
	height:90px;
	background: linear-gradient(-57.3792725189961deg, rgba(255, 255, 255, 1) 34%, rgba(251, 209, 231, 1) 100%);
}
.head_style img {position: relative;top:18px;left:20px;}
.title_left{
    float: left;
	padding-top:12px;
}
.title_left_p1{
    font-size: 22px;
    font-weight: bold;
    margin-left: 5px;
    margin-top: 5px;
}
.title_left_p2{
	font-size: 14px;
    color: #b4b2b2;
    margin-left: 5px;
    margin-top: 8px;
}
.title_right{
    float: right;
    margin-top: 30px;
}
.title_right>button{
    margin: 0 10px 0 10px;
}
.content_titleLeft {
  font-size: 20px;
  font-weight: bold;
}
.content_titleRigth {
  // float: right;
  font-size: 18px;
  position: absolute;
  right: 55px;
}
.content_body {
  margin: 0 30px 0 30px;
}
.content_span1 {
  font-size: 16px;
}
.content_span2 {
  display: inline-block;
  width: 100px;
  line-height: 30px;
  background-color: rgb(148, 151, 151);
  border-radius: 3px;
  color: #fff;
  text-align: center;
  margin-right: 5px;
}
.content_span3 {
  display: inline-block;
  width: 100px;
  line-height: 30px;
  background-color: rgba(233, 233, 233, 0.795);
  border-radius: 3px;
  color: #333;
  text-align: center;
  margin-right: 5px;
}
.content_line {
  margin-bottom: 10px;
}
.content_select {
  margin-right: 5px;
}
.content_picker {
  height: 30px;
  // margin-top: 10px;
  vertical-align: top;
}
::v-deep .avue-crud .el-table th {
  font-size: 14px;}
::v-deep .avue-crud .el-table td {
font-size: 12px;}
::v-deep .avue-crud{
  margin-top: 10px;
  }
::v-deep .el-input__icon {
  line-height: 22px;
}
::v-deep .el-range-separator {
  line-height: 22px;
  width: 20px;
}
.content_input {
  display: inline-block;
  width: 200px;
  margin-right: 5px;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0;
}
::v-deep  .cell-point{
  color: #255bc6;
  text-decoration: underline;
  cursor: pointer;
}
::v-deep .el-card__body .el-collapse-item__wrap{
  display: none !important;
}
</style>
