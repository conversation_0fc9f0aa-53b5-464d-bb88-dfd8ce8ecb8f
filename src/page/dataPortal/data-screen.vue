<template>
	<section>
		<div class="wrapAll clearfix">
			<topNav></topNav>
			<div class="center">
				<div class="con_head" v-html="descriptor">
<!--					<h1>疫情大数据</h1>
					<p>基于校级大数据平台，通过数据整合、汇总分析，多维分析，数据预警等多种可视化呈现方式，为学校管理部门，校领导决策提供多部门、跨业务的一站式数据服务，时实全面掌握校情，提升学校的管理决策效率。</p>-->
				</div>
				<div class="con_body">
					<el-carousel :interval="3000" type="card" height="320px">
						<el-carousel-item v-for="(item,index) in list" :key="index" class="items">
							<a :href="item.href" target="_blank" @click="viewDesign(item)">
								<img :src="item.reportImage == null || item.reportImage == ''? require('../../assets/images/charts.jpg') : item.reportImage" />
								<p>{{item.reportName}}</p>
							</a>
						</el-carousel-item>
					</el-carousel>
				</div>
				<foot></foot>
			</div>
			<!-- <foot></foot> -->
		</div>
	</section>
</template>

<script>
import topNav from "./component/nav";
import foot from "./component/foot";
import {ListReportNoLogin} from "@/api/report";
import {GetSysParamNoLogin} from "@/api/sysParam";

export default {
		components: {
			topNav,
			foot
		},
		name: "Carousel",
		data() {
			return {
        descriptor:"",
				list: [],
				params: {
					page: 1,
					pageSize: 50,
					queryParam: {
						reportName: "",
						reportRroup: "前台"
					}
				},
			};
		},
		created() {
			this.queryByPage();
      this.getDescription();
		},
		methods: {
			async queryByPage() {
				const res = await ListReportNoLogin(this.params);
				if (res.data.code != "00000") return;
				this.listLoading = true;
				this.list = res.data.info.records;
				this.listLoading = false;
			},
			viewDesign(val) {
				var routeUrl = this.$router.resolve({
					path: "/bigscreen/viewer",
					query: {
						reportId: val.id
					}
				});
				window.open(routeUrl.href, "_blank");
			},
      getDescription(){
        GetSysParamNoLogin({idOrNameOrType:'dataScreenDesc'}).then((res) => {
          this.descriptor = res.data.info.value;
        });
      }
		}
	};
</script>

<style scoped>
	@import url(../../styles/assets/css/reset.css);
	@import url(../../styles/assets/css/head.css);
	@import url(../../styles/assets/css/index.css);
</style>
<style scoped>
	.footer {
		width: 100%;
		line-height: 30px;
		font-size: 12px;
		color: #999;
		width: 100%;
		text-align: center;
		position: absolute;
		left: 0;
		bottom: -20px;
		z-index: 99;
	}
  .center {
		width: 80%;
		margin: auto;
		height: 100%;
		min-height: 600px;
		position: relative;
	}
	.con_head {
		text-align: center;
	}

	.con_head h1 {
		font-size: 36px;
		padding: 20px;
		color: #fff;
	}

	.con_head p {
		font-size: 12px;
		color: rgba(255, 255, 255, .4);
		width: 80%;
		padding: 20px 0 50px 0;
		margin: auto;
	}

	.el-carousel__item {
		box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.3);
		border-radius: 5px;
	}

	.el-carousel__item a {
		display: block;
		height: 320px;
		position: relative;
	}

	.el-carousel__item a img {
		width: 100%;
		height: 100%;
	}

	.el-carousel__item a p {
		color: #fff;
		font-size: 16px;
		opacity: 0.65;
		line-height: 30px;
		padding-left: 20px;
		height: 30px;
		width: 100%;
		background-color: #001528;
		display: block;
		margin: 0;
		position: absolute;
		bottom: 0px;
		z-index: 10000;
	}
	.el-carousel__item:nth-child(2n) {
		background-color: #99a9bf;
	}

	.el-carousel__item:nth-child(2n+1) {
		background-color: #d3dce6;
	}
</style>
