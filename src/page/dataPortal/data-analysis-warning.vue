<template>
  <section>
    <div class="wrapAlls">
      <topNav></topNav>
      <div class="boxs">
        <div class="nav">
          <span class="nav_h2">数据分析</span> <span class="nav_h5">/</span>
          <span class="nav_h3">预警专题</span> <span class="nav_h5">/</span>
          <span class="nav_h4">预警综合分析</span>
        </div>
        <!-- <viewer class="viewer" :report-id="`1496684733181038593`"></viewer> -->
        <div class="content">
            <viewer class="viewer" :report-id="this.reportId"></viewer>
            <footer class="footers">
              <span>
                {{ dbxx }}
              </span>
            </footer>
        </div>
        <!-- <footer class="footers">
          <span>
            {{ dbxx }}
          </span>
        </footer> -->
      </div>
    </div>
  </section>
</template>

<script>
import topNav from "./component/nav";
import foot from "./component/foot";
import {GetSysParamNoLogin} from "@/api/sysParam";
import viewer from "@/views/report/bigscreen/viewer/layoutViewer.vue";

export default {
  name: "GitSanythReportUiDataWarning",
  components: {
    topNav,
    foot,
    viewer
  },
  data() {
    return {
      dbxx: ``,
      reportId:'',
    };
  },
  computed: {
    
  },
  created() {
    document.title = '预警分析';
    this.reportId = this.$route.params.reportId
    // this.getList();
    // this.onLoad(this.pageParam());
    this.footP(this.footParam());
    console.log(this.$route.params.reportId)
  },
  mounted() {},

  methods: {
    footP(param) {
      GetSysParamNoLogin(param).then((res) => {
        this.dbxx = res.data.info.value;
      });
    },
    footParam() {
      return {
        idOrNameOrType: "dbxx",
      };
    },
  },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>

.wrapAlls {
  // height: calc(100% - 60px);
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  //   bottom: -60px;
  // height: 100vh;
  overflow: hidden;
}
.boxs {
  height: calc(100vh - 60px);
  overflow-y: scroll;
}
.footers {
  background: rgb(240, 242, 245);
  font-size: 12px;
  line-height: 30px;
  color: rgb(147, 150, 149);
  text-align: center;
}
.nav {
  height: 50px;
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #E9E9E9;
}
.nav_h1 {
  line-height: 50px;
  font-size: 18px;
  font-weight: bold;
  margin-left: 50px;
}
.nav_h2 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 50px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h3 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
  color: rgb(147, 150, 149);
}
.nav_h4 {
  line-height: 50px;
  font-size: 14px;
  margin-left: 15px;
  margin-right: 15px;
}
.nav_h5{
	font-size: 14px;
  color: rgb(147, 150, 149);
}
.content {
  width: 100%;
  height: 100%;
  // min-height: 800px;

}
.viewer{
    width: 100%;
  // height: 100%;

    // height: 800px;
}
</style>