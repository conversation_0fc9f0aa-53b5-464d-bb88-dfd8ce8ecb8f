<template>
  <div class="topbar">
    <div class="imgWrap">
      <img class="logo" :src="schoolLogo" alt />
    </div>
    <p class="schoolName">{{ systemName }}</p>
    <ul class="toolbar">
      <router-link v-for="item in menuList" :key="item" tag="li" :to="item.event ==null ?item.path:''"
                   @click.native="item.event ==null ?refresh($event):''" :activeClass="item.event ==null ?'toolbarClick':''">
        <p v-text="item.label" v-if="item.event !=null" @click="handleClick(item.event)"></p>
        <p v-text="item.label" v-else></p>
        <span></span>
      </router-link>
    </ul>

    <div class="stateBar">
      <div
        class="changeSkin"
        :class="show1 ? 'headerBackground' : ''"
        @mouseover="changeSkinShowTrue()"
        @mouseout="changeSkinShowFalse()"
      >
        <img src="../../../styles/assets/image/change.png" alt />
        <el-collapse-transition>
          <div class="skinBox" v-show="show1">
            <p class="skinTitle">选择主题</p>
            <ul id="header-skin" class="header-skin">
              <li
                @click="changeSkin9"
                class="header-skin-auto header-skin-1"
                :class="selectSkin == 9 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>深海蓝</p>
              </li>
              <li
                @click="changeSkin1"
                class="header-skin-auto header-skin-4"
                :class="selectSkin == 4 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>经典白</p>
              </li>
              <li
                @click="changeSkin2"
                class="header-skin-auto header-skin-2"
                :class="selectSkin == 2 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>琥珀金</p>
              </li>
              <li
                @click="changeSkin3"
                class="header-skin-auto header-skin-3"
                :class="selectSkin == 3 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>钛钢灰</p>
              </li>
              <li
                @click="changeSkin5"
                class="header-skin-auto header-skin-5"
                :class="selectSkin == 5 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>水晶紫</p>
              </li>
              <li
                @click="changeSkin6"
                class="header-skin-auto header-skin-6"
                :class="selectSkin == 6 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>火山红</p>
              </li>
              <li
                @click="changeSkin7"
                class="header-skin-auto header-skin-7"
                :class="selectSkin == 7 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <!-- <p>青葱绿</p> -->
                <p>北工红</p>
              </li>
              <li
                @click="changeSkin8"
                class="header-skin-auto header-skin-8"
                :class="selectSkin == 8 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>樱花粉</p>
              </li>
              <li
                @click="changeSkin"
                class="header-skin-auto header-skin-10"
                :class="selectSkin == 1 ? 'header-skin-selected' : ''"
              >
                <div></div>
                <p>环保绿</p>
              </li>
            </ul>
            <input class="skinInput" id="skinInput" type="hidden" value />
          </div>
        </el-collapse-transition>
      </div>
      <div class="user_info name" v-if="info.humanname != null">
        <!--			<img class="headImg" src="../../../styles/assets/image/touxiangf.png" width="24"/>-->
        <div
          class="name"
          :class="show2 ? 'headerBackground' : ''"
          @mouseover="changePopupShowTrue()"
          @mouseout="changePopupShowFalse()"
        >
          <!--                <img class="headImg" :onerror="defaultImg" :src="'/file/view/' + info.avater" />-->
          <img
            class="headImg"
            src="../../../styles/assets/image/touxiangf.png"
            width="24"
          />
          <span class="nameSpan">{{ info.humanname }}</span>
          <i class="el-icon-caret-bottom"></i>
          <!-- <img src="../../../styles/assets/image/arrow.png" alt /> -->
          <el-collapse-transition>
            <div class="minePopup" v-show="show2">
              <p @click="toEnd"><i class="el-icon-set-up"></i>进入后台</p>
              <p @click="checkRoleHandle"><i class="el-icon-set-up"></i>选择角色</p>
              <p @click="logoutHandle">
                <i class="el-icon-switch-button"></i>{{ $t("navbar.logOut") }}
              </p>
            </div>
          </el-collapse-transition>
        </div>
        <el-dialog class="role-container"
                   title="选择角色"
                   append-to-body
                   :visible.sync="isChangeRole"
                   width="300px"
                   :close-on-click-modal="false">
          <div class="role-item"
               v-for="item in roleList"
               :key="item"
               :class="{ 'is-active': roleName === item }"
               @click="changeRoleHandle(item)">
            {{ item }}
          </div>
        </el-dialog>
      </div>
      <div class="caidanImg" @click="drawerleft = true">
        <img src="../../../styles/assets/image/caidan.png" alt="" />
      </div>
    </div>

    <!--    <div class="searchWrap">-->
    <!--      <input-->
    <!--        class="form-control"-->
    <!--        type="text"-->
    <!--        placeholder="搜索服务"-->
    <!--      />-->
    <!--      <span class="searchButton"></span>-->
    <!--    </div>-->
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import {removeStore, setStore} from "@/util/store";
import {GetSysParam, GetSysParamNoLogin} from "@/api/sysParam";
import {GetOtherPlatformUrl} from "@/api/settings";
import {getEMenuNologin, getRoleList, getUserInfo, switchRole} from "@/api/user";
import {isLogin} from "@/api/index";

export default {
  data() {
    return {
      isChangeRole: false,
      roleList: [],
      userdata: null,
      roleName: "",
      username: "",
      info: {},
      schoolLogo: "",
      show1: false,
      show2: false,
      dataskinID: '',
      selectSkin: 1,
      current: "",
      entryData: [],
      menuList: [],
      systemName: ``,
      showitem: 1 ,
      routerL:``
    };
  },
  computed: {
    ...mapGetters(["website", "keyCollapse"]),
  },
  inject: ["appReload"],
  created() {
    let param = this.pageParam().arr;
    this.onLoad(param);
    // this.onLoad2(this.pageParam2());
    this.getRoleInfo();
    isLogin().then((res) => {
      if (res.data) {
        getUserInfo()
          .then((res) => {
            const data = res.data.info;
            this.info = data
          })
          .catch((err) => {});
      }
    });
    var data = JSON.parse(localStorage.getItem("skinID"));
    if(data == null) {
      GetSysParam({idOrNameOrType: 'skinID'}).then(res => {
        this.dataskinID = res.data.info.value;
        data = {
          skinID : this.dataskinID
        }
        localStorage.setItem("skinID", JSON.stringify(data));
        if(data.skinID == 1){
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-10.css");
        } else if (data.skinID == 2) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-2.css");
        } else if (data.skinID == 3) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-3.css");
        } else if (data.skinID == 4) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-4.css");
        } else if (data.skinID == 5) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-5.css");
        } else if (data.skinID == 6) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-6.css");
        } else if (data.skinID == 7) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-11.css");
        } else if (data.skinID == 8) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-8.css");
        } else if (data.skinID == 9) {
          this.selectSkin = data.skinID;
          var Link = document.querySelector(".skinLink");
          Link.setAttribute("href", "./static/css/header-skin-1.css");
        }
      })
    } else if(data.skinID == 1){
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-10.css");
    } else if (data.skinID == 2) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-2.css");
    } else if (data.skinID == 3) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-3.css");
    } else if (data.skinID == 4) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-4.css");
    } else if (data.skinID == 5) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-5.css");
    } else if (data.skinID == 6) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-6.css");
    } else if (data.skinID == 7) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-11.css");
    } else if (data.skinID == 8) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-8.css");
    } else if (data.skinID == 9) {
      this.selectSkin = data.skinID;
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-1.css");
    }
  },
  methods: {
    getRoleInfo() {
      getRoleList().then((res) => {
        if (res.data.code === "00000") {
          this.userdata = res.data.info;
          this.roleName = this.userdata.roleName;
          this.username = this.userdata.humanname;
          this.roleList = this.userdata.roleList;
          setStore({name: "user-info", content: this.userdata});
        }
      });
    },
    // 切换角色
    checkRoleHandle() {
      this.isChangeRole = true;
    },
    changeRoleHandle(item) {
      switchRole({role: item}).then((res) => {
        if (res.data && res.data.code === "00000") {
          this.getRoleInfo();
          this.isChangeRole = false;
          this.$store.commit("DEL_ALL_TAG");
          // this.$store.commit('SET_MENUID', {})
          // this.$store.commit('SET_MENUALL', []);
          // this.$store.commit('SET_MENU', [])
          // this.$store.commit('SET_ROLES', [])


          // window.location.href = "/index";
          this.$message.success("角色切换成功！");
          this.$router.replace({
            path: this.$router.$avueRouter.getPath({
              name: "首页",
              src: "/",
            }),
            query: {},
          });
          this.appReload();
        }
      });
    },
    handleClick(o){
      let split = o.split(",");
      this[split[0]](split[1]);
    },
    onLoad(param) {
      GetSysParamNoLogin(param[0]).then((res) => {
        // console.log(res);
        const data = res.data.info;
        let img = JSON.parse(data.img);
        if (img.length > 0) {
          this.schoolLogo = img[0].url;
        }
      });
      GetSysParamNoLogin(param[1]).then((res) => {
        this.systemName = res.data.info.value;
        document.title = this.systemName
      });
      getEMenuNologin().then((res)=>{
        this.menuList = res.data.info;
      });
    },
    pageParam() {
      return {
        arr: [
          { idOrNameOrType: "frontlogo" },
          { idOrNameOrType: "systemName" },
        ],
      };
    },
    onMousteIn: function (index) {
      this.show2 = true; //鼠标移入显示
      this.current = index;
    },
    onMousteOut: function () {
      this.show2 = false; //鼠标移出隐藏
      this.current = "";
    },
    changeSkinShowTrue() {
      this.show1 = true;
    },
    changeSkinShowFalse() {
      this.show1 = false;
    },
    changePopupShowTrue() {
      this.show2 = true;
    },
    changePopupShowFalse() {
      this.show2 = false;
    },
    toEnd() {
      this.$router.push({
        path: "/wel/index",
      });
    },
    logoutHandle() {
      this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
        confirmButtonText: this.$t("submitText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then((res) => {
          removeStore({ name: "user-info" });
          window.location.href = "/logout";
        });
      });
    },
    changeSkin() {
      var Link = document.querySelector(".skinLink");
      // console.log(Link);
      Link.setAttribute("href", "./static/css/header-skin-10.css");
      this.selectSkin = 1;
      const info = {
        skinID: this.selectSkin,
      };
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin1() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-4.css");
      this.selectSkin = 4;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin2() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-2.css");
      this.selectSkin = 2;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin3() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-3.css");
      this.selectSkin = 3;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin5() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-5.css");
      this.selectSkin = 5;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin6() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-6.css");
      this.selectSkin = 6;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin7() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-11.css");
      this.selectSkin = 7;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin8() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-8.css");
      this.selectSkin = 8;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    changeSkin9() {
      var Link = document.querySelector(".skinLink");
      Link.setAttribute("href", "./static/css/header-skin-1.css");
      this.selectSkin = 9;
      const info = {
        skinID: this.selectSkin,
      };
      // console.log(info);
      localStorage.setItem("skinID", JSON.stringify(info));
    },
    goLink(e) {
      // window.location.href = e
      var win = window.open();
      win.location.href = e;
    },
    gotoOtherPlatform(type) {
      GetOtherPlatformUrl({ platform: type }).then((res) => {
        const data = res.data;
        console.log(data)
        this.goLink(data);
        /*var routeUrl = this.$router.resolve({
          path: data,
          query: {}
        });
        window.open(routeUrl.href, "_blank");*/
      });
    },
    refresh(e) {
      let el = e.currentTarget;
      if(el.classList.length > 0){
        this.$router.go(0);
      }
    },
  },
};
</script>

<style>
.is-active {
  color: #00a5ec;
  font-weight: 700;
}
.role-item {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  cursor: pointer;
}
.role-item:hover {
  background-color: #eee;
}
</style>
