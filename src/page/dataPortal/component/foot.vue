<template>
	<div class="footer">
		<p>
			<!-- ©2021 {{website.websiteName}} 版权所有  技术支持：{{website.corporateName}} -->
      {{dbxx}}
		</p>
	</div>
</template>

<script>
import { mapGetters } from "vuex";
import {GetSysParamNoLogin} from "@/api/sysParam";

export default {
  data() {
    return {
      dbxx:``
    };
  },
  computed: {
    ...mapGetters(["website", "keyCollapse"])
  },
  created(){
    this.onLoad(this.pageParam());
  },
  methods:{
    onLoad(param){
      GetSysParamNoLogin(param).then(res=>{
        this.dbxx = res.data.info.value 
      })
    }, 
    pageParam() {
      return {
        idOrNameOrType: "dbxx"
      }
    },
  }
  }
</script>

<style>
</style>
