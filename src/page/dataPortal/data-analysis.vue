<template>
  <section>
    <div class="wrapAll clearfix">
      <topNav></topNav>
      <div class="centers">
        <div class="analyseSelectWrap">
          <div class="sortWrap clearfix">
            <p>分析专题：</p>
            <div class="sortContent clearfix">
              <span
                class="sort"
                v-for="(item, index) in topicList"
                :key="item.TOPIC"
                @click="toentryDatas(item)"
                :class="{
                  tabClick:
                    item.TOPIC === checkindex ||
                    (index == 0 && checkindex == '')
                      ? true
                      : false,
                }"
                >{{ item.TOPIC }}</span
              >
            </div>
          </div>
        </div>
        <div class="analyseContent">
          <el-row :gutter="30">
            <el-col :span="6" v-for="entry in entryDatas" :key="entry.id">
              <div
                class="analyse"
                @click="iframes(entry)"
                @mouseenter="onMousteIn(entry.id, $event)"
              >
                <div>
                  <i class="iconfont" :class="entry.style"></i>
                  <span class="analyseName">{{ entry.name }}</span>
                </div>
                <div class="analyseDetial">
                  {{ entry.bz }}
                </div>
                <transition name="el-zoom-in-center">
                  <div
                    class="analyseHover layui-anim"
                    @mouseleave="onMousteOut()"
                    v-show="entry.id == current"
                  >
                    <div>
                      <i class="iconfont" :class="entry.style"></i>
                      <span class="analyseName">{{ entry.name }}</span>
                    </div>
                    <div class="analyseDetial">
                      {{ entry.bz }}
                    </div>
                  </div>
                </transition>
              </div>
            </el-col>
          </el-row>
        </div>
        <foot></foot>
      </div>
      <!-- <foot id="foot"></foot> -->
    </div>
  </section>
</template>

<script>
import topNav from "./component/nav";
import foot from "./component/foot";
import { GetAnalysisList, ListTopics } from "@/api/analysisItem";

export default {
  components: {
    topNav,
    foot,
  },
  data() {
    return {
      checkindex: "",
      selectSkin: 1,
      current: "",
      entryDatas: [],
      topicList: [{ TOPIC: "全部" }],
      entryData: [],
    };
  },
  created() {
    this.onLoad();
  },
  methods: {
    onLoad() {
      ListTopics().then((res) => {
        this.topicList = this.topicList.concat(res.data.info);
      });
      this.getAnalysisList({ topic: this.checkindex });
    },
    getAnalysisList(param) {
      GetAnalysisList(param).then((res) => {
        const data = res.data.info;
        this.entryDatas = data;
      });
    },
    onMousteIn: function (id) {
      this.current = id;
    },
    onMousteOut: function () {
      this.current = "";
    },
    iframes(item) {
      if ( item.url !== null ) {
        let path = `/data-analysis/ifram`;
        this.$router.push({
        path: path,
        query: {
          param: JSON.stringify(item),
        },
        });
      } else {
        let path = `/data-analysis/detail`;
        this.$router.push({
        path: path,
        query: {
          param: JSON.stringify(item),
        },
        });
      }
		// if (item.reportId) {
		// 	let path = `/data-analysis/detail`;
		// 	this.$router.push({
		// 	path: path,
		// 	query: {
		// 		param: JSON.stringify(item),
		// 	},
		// 	});
		// }
    },
    toentryDatas(item) {
      this.checkindex = item.TOPIC === "全部" ? "" : item.TOPIC;
      this.getAnalysisList({ topic: this.checkindex });
    },
  },
};
</script>

<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style scoped>
.footer {
  width: 100%;
  line-height: 30px;
  font-size: 12px;
  color: #999;
  width: 100%;
  text-align: center;
  position: absolute;
  left: 0;
  bottom: -63px;
  z-index: 99;
}
.wrapAll {
  padding: 0;
  /* overflow-y: hidden; */
}
.centers {
  position: relative;
  width: 75%;
  margin: auto;
  min-height: calc(100% - 150px);
}
.bot {
  width: 85px;
  height: 31px;
  background: #ddd;
  position: absolute;
  left: calc(50% - 50px);
  bottom: -70px;
  border-radius: 10px;
}
.bot > span {
  display: inline-block;
  width: 15px;
  height: 15px;
  background: #999;
  border-radius: 50%;
  z-index: 999;
  /* margin-top: 7px; */
}
.bot > span:hover {
  background: #58b9c7;
}
.span1 {
  margin-top: 8px;
  margin-left: 10px;
}
.span2 {
  margin-top: 8px;
  margin-left: 10px;
}
.span3 {
  margin-top: 5px;
  margin-left: 10px;
}

.you {
  position: absolute;
  background: #ddd;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  top: 280px;
  right: -150px;
}
.you:hover {
  background: #58b9c7;
}
.right {
  position: absolute;
  font-size: 55px;
  text-align: center;
  line-height: 50px;
  left: -5px;
  top: 0px;
  color: #fff;
  z-index: 999;
}
.zuo {
  position: absolute;
  background: #ddd;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  top: 280px;
  left: -150px;
}
.zuo:hover {
  background: #58b9c7;
}
.lefts {
  position: absolute;
  font-size: 55px;
  text-align: center;
  line-height: 50px;
  left: -3px;
  color: #fff;
}
iframe {
  width: 100%;
  height: calc(100% - 3px);
}
.analyseContent {
  margin-top: 50px;
  position: relative;
}

.sortWrap {
  width: 100%;
  /* padding: 20px 30px; */
  margin-bottom: 10px;
  margin-top: 100px;
  font-size: 18px;
  margin-top: calc(6%);
  font-size: 14px;
}

.sortWrap p {
  float: left;
  width: 90px;
  height: 30px;
  line-height: 30px;
  color: #fff;
  text-align: left;
}

.sortContent {
  float: right;
  width: calc(100% - 90px);
}

.sort {
  float: left;
  color: #999;
  padding: 7px 12px;
  border-radius: 3px;
  margin-right: 25px;
  cursor: pointer;
}

.sort:hover {
  background: #58b9c7;
  color: #fff;
}

.sortClick {
  background: #fff;
}
.tabClick {
  color: #fff !important;
}

.tabClick p {
  color: #fff;
}

.tabClick .iconfont {
  color: #fff;
}
.analyse {
  float: left;
  width: 100%;
  height: 130px;
  line-height: 75px;
  padding-right: 2px;
  padding-top: 0.1px;
  /* padding-left: 5px; */
  margin-bottom: 20px;
  line-height: 0px;
  color: #333;
  cursor: pointer;
  position: relative;
  text-align: left;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  box-shadow: 3px 3px 7px rgba(30, 48, 76, 0.1);
}

.analyseName {
  display: inline-block;
  width: calc(100% - 70px);
  height: 50px;
  line-height: 50px;
  vertical-align: middle;
  margin-top: 5px;
  font-size: 18px;
  margin-left: 5px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-bottom: 1px dotted rgb(255, 255, 255, 0.3);
  color: #fff;
  /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
}

.analyse i {
  font-size: 40px;
  line-height: 45px;
  margin-left: 10px;
  color: #58b9c7;
  float: left;
  margin-top: 10px;
}

.analyseDetial {
  width: calc(100% - 10px);
  height: 40px;
  font-size: 12px;
  line-height: 20px;
  padding-left: 55px;
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.3);
  padding-right: 25px;
  display: -webkit-box;
  /* 弹性盒模型*/
  -webkit-box-orient: vertical;
  /* 文字垂直排列 */
  -webkit-line-clamp: 2;
  /*文字显示的行数*/
  overflow: hidden;
  /*超出部分溢出隐藏*/
}

.analyseHover {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 5px;
  width: 100%;
  height: 100%;
  background: #f9c62d;
  box-shadow: 2px 2px 5px rgba(30, 48, 76, 0.2);
  padding-right: 2px;
  padding-top: 0.1px;
  /* padding-left: 5px; */
}

.analyseHover .analyseName {
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.analyseHover .analyseDetial {
  color: #e8e8e8;
}
</style>
