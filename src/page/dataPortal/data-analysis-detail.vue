<template>
  <section>
    <div class="wrapAll clearfix">
      <topNav></topNav>
      <div class="native">
        <span class="navcolor1">{{navtext1}}分析</span> <span class="navcolor2">数据分析</span> <span class="navcolor2">/</span> <span class="navcolor2">{{navtext1}}</span> <span class="navcolor4">/</span> <span class="navcolor3">{{navtext2}}</span>
      </div>
      <div class="list">
        <ul>
          <li v-for="(item,index) in liList" :key="index" :class="{activeDetail:isActive === item.id}" @click="getData(item)"><i class="iconfont" :class="item.style"></i><span class="litext" :title="item.name">{{ item.name }}</span></li>
        </ul>
      </div>
      <div>
        <viewer v-if="layoutShow" class="viewer" :report-id="reportId"></viewer>
        <widgetViewer v-if="widgetShow" class="viewer" :report-id="reportId"></widgetViewer>
      </div>
      <foot></foot>
    </div>
  </section>
</template>

<script>
import widget from "@/views/report/bigscreen/designer/widget/temp";
import widgetViewer from "@/views/report/bigscreen/viewer/index.vue";
import viewer from "@/views/report/bigscreen/viewer/layoutViewer.vue";
import {GetAnalysisList} from "@/api/analysisItem";
import {GetReport} from "@/api/report";
import topNav from "./component/nav";
import foot from "./component/foot";

export default {
  name: "dataAnalysisDetail",
  components: {
    widget,
    widgetViewer,
    viewer,
    topNav,
    foot
  },
  data() {
    return {
      bigScreenStyle: {},
      widgets: [],
      params: {},
      liList: [],
      reportId:'',
      isActive: ``,
      navtext1: ``,
      navtext2: ``,
      widgetShow: false,
      layoutShow: false,
    }
  },
  watch: {
    reportId: {
      handler() {
      }
    }
  },
  created() {
    if (this.$route.query.param) {
      this.params = JSON.parse(this.$route.query.param);
    }
    this.onLoad(this.params);
  },
  methods: {
    async onLoad(item) {
      GetAnalysisList({topic: item.topic}).then(res => {
        const data = res.data.info;
        this.liList = data;
      })
      this.getData(item);
    },
    async getData(item) {
      //TODO 获取报表类型区分view页面
      await GetReport({id:item.reportId}).then(res=>{
        if( res.data.info.reportType == '大屏' ) {
          this.widgetShow = true;
          this.layoutShow = false;
        }
        if( res.data.info.reportType == '报表' ) {
          this.layoutShow = true;
          this.widgetShow = false;
        }
      })
      this.navtext1 = item.topic;
      this.navtext2 = item.name;
      this.isActive = item.id;
      this.reportId = item.reportId;
    },
  }
}


</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style scoped lang="scss">
.footer {
  width: 100%;
  line-height: 30px;
  font-size: 12px;
  color: #999;
  text-align: center;
  position: fixed;
  left: 0;
  bottom: 0px;
  z-index: 77;
}
.wrapAll {
  // overflow: hidden;
  padding: 0;
  background-color: rgb(240, 242, 245) !important;
}
.native{
  width: 100%;
  height: 50px;
  background-color: rgb(255, 255, 255);
  position: fixed;
  top: 60px;
  z-index: 96;
  line-height: 45px;
  border-bottom: 1px solid rgba(216, 216, 219, 0.74);
}
.navcolor1{
  font-size: 18px;
  font-weight: bold;
  margin-left: 40px;
  // cursor: pointer;
}
// .navcolor1:hover{
//   color: rgb(52, 159, 167);
// }
.navcolor2{
  font-size: 14px;
  margin-left: 20px;
  color: rgba(85, 87, 87, 0.753);
}
// .navcolor2:hover{
//   color: rgb(52, 159, 167);
// }
.navcolor3{
  font-size: 14px;
  margin-left: 20px;
}
.iconfont{
  font-size: 20px;
  margin-left: 15px;
}
.litext{
  margin-left: 5px;
}
// .navcolor3:hover{
//   color: rgb(52, 159, 167);
// }
.navcolor4{
  font-size: 12px;
  margin-left: 20px;
  color: rgba(85, 87, 87, 0.548);
}
.list {
  width: 14%;
  // min-width: 240px;
  white-space: nowrap;
  float: left;
  background-color: rgb(255, 255, 255);
  height: 100%;
  position: fixed;
  top: 110px;
  z-index: 99;
}
.list>ul{
  padding: 15px 12px 15px 10px;
  // text-align: center;
}
.list>ul>li{
	height: 45px;
  line-height: 45px;
  background-color: rgba(19, 119, 126, 1);
  color: #fff;
  margin-bottom: 2px;
  font-size: 15px;
  border-radius: 3px;
  overflow: hidden;
  text-overflow:ellipsis;
  cursor: pointer;
}
.list>ul>li:hover{
  background-color: #48B9C0;
  text-overflow:ellipsis;
}
.viewer{
  width: calc(100% - 14%);
  height: 100%;
  min-height: calc(100vh - 110px);
  float: right;
  margin-top: 50px;
  padding-bottom: 60px;
  // z-index: 99;
  background-color: rgb(240, 242, 245);
}
</style>