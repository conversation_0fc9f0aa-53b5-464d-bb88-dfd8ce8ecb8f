import "../../../../node_modules/echarts/map/js/china.js"
import echarts from "echarts";

export default {
    dituoptions: {
        title: {
            text: '中国各省份人数分布',
            left: 'center'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            data:['人数']
        },
        visualMap: {
            type: 'piecewise',
            pieces: [
                {min: 5000, label: '5000人以上', color: '#BD0103'},
                {min: 2000, max: 4999, label: '2000 - 4999人', color: '#C13531'},
                {min: 1000, max: 1999, label: '1000 - 1999人', color: '#E55A4E'},
                {min: 500, max: 999, label: '500 - 999人', color: '#F09898'},
                {max: 499, label: '499人以下', color: '#FADBD8'}
            ],
            color: ['#E0022B', '#E09107', '#A3E00B']
        },
        series: [
            {
                name: '人数',
                type: 'map',
                mapType: 'china',
                roam: false,
                data: [
                    {name: '北京', value: 2154},
                    {name: '天津', value: 1135},
                    {name: '上海', value: 2424},
                    {name: '重庆', value: 1303},
                    {name: '河北', value: 7185},
                    // 其他省份数据...
                ],
                label: {
                    show: true,
                    formatter: '{b}\n{c}'
                }
            }
        ]
    },
    luqvoptions: {
        title: {
            text: '月度销售额'
        },
        tooltip: {},
        xAxis: {
            data: ['一月', '二月', '三月', '四月', '五月']
        },
        yAxis: {},
        series: [{
            name: '销售额',
            type: 'bar',
            data: [120, 200, 150, 80, 70]
        }]
    }
}
