<template>
    <section>
        <div class="wrapAlls">
            <div class="content">
                <div class="leftmenu">
                    <el-menu
                        default-active="2"
                        class="el-menu-vertical-demo"
                        @open="handleOpen"
                        @close="handleClose">
                        <el-submenu index="1">
                            <template slot="title">
                                <i class="el-icon-location"></i>
                                <span>导航一</span>
                            </template>
                            <el-menu-item-group>
                                <template slot="title">分组一</template>
                                <el-menu-item index="1-1">选项1</el-menu-item>
                                <el-menu-item index="1-2">选项2</el-menu-item>
                            </el-menu-item-group>
                            <el-menu-item-group title="分组2">
                                <el-menu-item index="1-3">选项3</el-menu-item>
                            </el-menu-item-group>
                            <el-submenu index="1-4">
                                <template slot="title">选项4</template>
                                <el-menu-item index="1-4-1">选项1</el-menu-item>
                            </el-submenu>
                        </el-submenu>
                        <el-menu-item index="2">
                            <i class="el-icon-menu"></i>
                            <span slot="title">导航二</span>
                        </el-menu-item>
                        <el-menu-item index="3" disabled>
                            <i class="el-icon-document"></i>
                            <span slot="title">导航三</span>
                        </el-menu-item>
                        <el-menu-item index="4">
                            <i class="el-icon-setting"></i>
                            <span slot="title">导航四</span>
                        </el-menu-item>
                    </el-menu>
                </div>

                <div class="rightview">
                    <div class="content_title">
                        <div class="title_left">
                            <div class="title_left_p1">各省情况分析</div>
                        </div>
                        <div class="title_right">
<!--                            <span>统计规则：</span>-->
                            <el-select
                                class="content_select"
                                v-model="value"
                                placeholder="选择年份"
                            >
                                <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-select
                                class="content_select"
                                v-model="value"
                                placeholder="选择批次"
                            >
                                <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-button type="primary">查询</el-button>
                        </div>
                    </div>
                    <div class="dituchart">
                        <div class="content_body">
                            <div class="content_body_box">
                                <span class="content_body_span">第一志愿录取率</span>
                            </div>
                            <div style="height: 600px;">
                                <v-chart :options="dituoptions" style="height: 100%;width: 100%;" autoresize/>
                            </div>
                        </div>
                    </div>
                    <div class="dituchart">
                        <div class="content_body">
                            <div class="content_body_box">
                                <span class="content_body_span">数据明细</span>
                            </div>
                            <div style="height: 600px;">
                                <avue-crud
                                    :data="data"
                                    :option="option"
                                    :page.sync="page"
                                >
                                    <template slot="index" slot-scope="{row,index}">
                                        <p :style="{textAlign: 'center'}">{{index+1}}</p>
                                    </template>
                                </avue-crud>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import echarts from "echarts";
import "../../../node_modules/echarts/map/js/china.js"
var data = [
    {name: '北京', value: 2154},
    {name: '天津', value: 1135},
    {name: '上海', value: 2424},
    {name: '重庆', value: 1303},
    {name: '河北', value: 7185},
    // 其他省份数据...
];
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
export default {
  //import引入的组件需要注入到对象中才能使用",
  components: {
      // topNav
  },
  data() {
    //这里存放数据",
    return {
        value: "",
        options: ['123'],
            dituoptions: {
                title: {
                 //   text: '中国各省份人数分布',
                  //  left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: false,
                    orient: 'vertical',
                    left: 'left',
                    data:['人数'],
                },
                visualMap: {
                    type: 'piecewise',
                    pieces: [
                        {min: 5000, label: '5000人以上', color: '#45A5F8'},
                        {min: 2000, max: 4999, label: '2000 - 4999人', color: '#8AC6FD'},
                        {min: 1000, max: 1999, label: '1000 - 1999人', color: '#D2EAFF'},
                        {min: 500, max: 999, label: '500 - 999人', color: '#B2CAE0'},
                        {max: 499, label: '499人以下', color: '#eeeeee'}
                    ],
                    color: ['#E0022B', '#E09107', '#A3E00B'],
                },
                series: [
                    {
                        name: '人数',
                        type: 'map',
                        mapType: 'china',
                        roam: false,
                        data: data,
                        label: {
                            show: true,
                            formatter: '{b}\n{c}'
                        }
                    }
                ]
            },
        page: {
            currentPage: 1,
            total: 5,
            pageSize: 20,
        },
        data: [],
        option: {
            searchShowBtn:false,
            menu: false,
            align: "left",
            menuAlign: "center",
            border: true,
            addBtn: false,
            searchBtn: false,
            refreshBtn: false,
            columnBtn: false,
            stripe: true,
            column: [
                {
                    label:'序号',
                    prop:'index',
                    width: 50,
                    fixed:true
                },
                {
                    label: "预警类型",
                    prop: "warnType",
                },
                {
                    label: "预警人数",
                    prop: "rs",
                },
                {
                    label: "预警人次",
                    prop: "rc",
                },
                {
                    label: "总人数",
                    prop: "zrs",
                },
                {
                    label: "总人次",
                    prop: "zrc",
                },
                {
                    label: "预警人数百分比",
                    prop: "yjrsb",
                },
                {
                    label: "预警人次百分比",
                    prop: "yjrcb",
                },
            ],
        },
    };
  },
  //监听属性 类似于data概念",
  computed: {

  },
  //监控data中的数据变化",
  watch: {

  },
  //方法集合",
  methods: {

  },
  //生命周期 - 创建之前",数据模型未加载,方法未加载,html模板未加载
  beforeCreate() {
  },

  //生命周期 - 创建完成（可以访问当前this实例）",数据模型已加载，方法已加载,html模板已加载,html模板未渲染
  created() {

  },
  //生命周期 - 挂载之前",html模板未渲染
  beforeMount() {

  },

  //生命周期 - 挂载完成（可以访问DOM元素）",html模板已渲染
  mounted() {

  },

  //生命周期 - 更新之前",数据模型已更新,html模板未更新
  beforeUpdate() {

  },
  //生命周期 - 更新之后",数据模型已更新,html模板已更新
  updated() {

  },
  //生命周期 - 销毁之前",
  beforeDestroy() {

  },
  destroyed() {

  },
  //生命周期 - 销毁完成",
  //如果页面有keep-alive缓存功能，这个函数会触发",
  activated() {

  },
}


</script>

<style scoped>
    .wrapAlls {
        height: 100vh;
        overflow: hidden;
        background: #F0F2F5;
    }
    .boxs {
        height: calc(100vh);
        /*height: 100%;*/
        /*margin-top: 60px;*/
        overflow-y: auto;
        /*background: rgba(240, 239, 239, 0.7)*/
    }
    .content {
        width: 100%;
        /*height: 100%;*/
        min-height: calc(100vh);
        /*margin-top: 60px;*/
        display: flex;
        /*background: rgba(240, 239, 239, 0.7);*/
    }
    .leftmenu{
        width: 250px;
        height: calc(100vh);
        background-color: #fff;
    }
    .rightview{
        width: calc(100% - 250px);
        height: calc(100vh);
        padding: 0px 15px;
        overflow-y: auto;
    }
    .content_title {
        height: 80px;
        margin-bottom: 15px;
        border-radius: 5px;
        overflow: hidden;
        background-color: #fff;
        box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    }
    .head_style {
        display: inline-block;
        float: left;
        width:80px;
        height:90px;
        background: linear-gradient(-57.3792725189961deg, rgba(255, 255, 255, 1) 34%, rgba(251, 209, 231, 1) 100%);
    }
    .head_style img {position: relative;top:18px;left:20px;}
    .title_left{
        float: left;
        height: 80px;
    }
    .title_left_p1{
        font-size: 18px;
        font-weight: bold;
        margin-left: 35px;
        line-height: 75px;
    }
    .title_left_p2{
        font-size: 14px;
        color: #b4b2b2;
        margin-left: 5px;
        margin-top: 8px;
    }
    .title_right{
        float: right;
        margin-top: 25px;
        margin-right: 15px;
    }
    .title_right>button{
        margin: 0 10px 0 10px;
    }
    .content_titleLeft {
        font-size: 20px;
        font-weight: bold;
    }
    .content_titleRigth {
        font-size: 18px;
        position: absolute;
        right: 55px;
    }
    .content_body {
        margin: 0 30px 0 30px;
    }
    .content_span1 {
        font-size: 16px;
    }
    .content_span2 {
        display: inline-block;
        width: 100px;
        line-height: 30px;
        background-color: rgb(148, 151, 151);
        border-radius: 3px;
        color: #fff;
        text-align: center;
        margin-right: 5px;
    }
    .content_span3 {
        display: inline-block;
        width: 100px;
        line-height: 30px;
        background-color: rgba(233, 233, 233, 0.795);
        border-radius: 3px;
        color: #333;
        text-align: center;
        margin-right: 5px;
    }
    .content_line {
        margin-bottom: 10px;
    }
    .content_select {
        margin-right: 5px;
    }
    .content_picker {
        height: 30px;
        vertical-align: top;
    }
    .dituchart{
        width: 100%;
        /*height: 700px;*/
        /*border: 1px solid #00a5ec;*/
        background-color: #fff;
        border-radius: 10px;
        padding: 10px 0;
        margin-bottom: 20px;
    }
    .content_body {
        margin: 0 30px 0 30px;
    }
    .content_body_box{
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        border-bottom: 1px solid #EBEEF5;
    }
    .content_body_span{
        margin-right: 40px;
    }
</style>
