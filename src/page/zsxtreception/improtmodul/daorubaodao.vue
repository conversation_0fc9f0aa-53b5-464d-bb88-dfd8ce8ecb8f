<template>
    <div v-loading="loading">
        <div style="font-size: 14px;line-height: 30px;">
          说明： 按报到状态批量导入学生，以统计报到情况
        </div>
        <el-upload
            class="upload-demo"
            drag
            ref="upload"
            accept=".xlsx"
            action="/ksgl/tdd/importBdsj"
            :on-change="handlechange"
            :on-remove="handleRemove"
            :on-success="handlesuccess"
            :on-error="handleerror"
            :file-list="fileList"
            :limit="1"
            :auto-upload="false">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
          <div style="margin-top: 10px;">
            <el-button type="primary" @click="handleTemplate()">
              点击下载模板<i class="el-icon-download el-icon--right"></i>
            </el-button>
          </div>
        </el-upload>
      <div class="dialog-footer" style="margin-top: 20px;">
        <el-button type="primary" @click="submitform">导 入</el-button>
      </div>
    </div>
</template>

<script>
import {baseUrl} from "@/config/env";

export default {
    name: "daoruluqv",
    data() {
        return {
            activeName: 'first',
            loading: false,
            dialogImageUrl: '',
            dialogVisible: false,
            disabled: false,
            uploadUrl:
                baseUrl +
                "/report/dashboard/import",
            fileList: [],

        };
    },
    computed: {
        headers() {
            return {
                // Authorization: getToken() // 直接从本地获取token就行
            };
        },
    },
    methods: {
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      handleDownload(file) {
        console.log(file);
      },
        handleClick(tab, event) {
            console.log(tab, event);
        },
      handlechange(file, fileList){
        console.log(`handlechange`,file, fileList);
        this.fileList = fileList;
      },
      handleRemove(file, fileList) {
        console.log(file, fileList);
        this.fileList = [];
      },
      submitform(){
        this.loading = true;
        this.$refs.upload.submit();

      },
      handlesuccess(response){
        console.log(`handlesuccess`,response)
        if(response.code == 200 || response.code == '00000'){
          this.loading = false;
          this.$message({
            message: '导入成功',
            type: 'success'
          });
          this.$emit('onclose',true)
        }else {
          this.loading = false;
          this.$message.error('导入失败');
          this.$emit('onclose',true)
        }
      },
      handleerror(err){
        console.log(`handleerror`,err)
        this.loading = false;
        this.$message.error('导入失败');
        this.$emit('onclose',true)
      },
      handleTemplate() {
        window.open(`/ksgl/tdd/downBdsjTemplate`);
      },
    }
}
</script>

<style scoped>

</style>