<template>
    <div>
      <el-select v-model="nf" clearable placeholder="请选择年份">
        <el-option
          v-for="item in nflist"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <div style="height: 20px;line-height: 20px;color: #333;margin: 20px 0;">请选择需要清空的数据：</div>
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="tdd">清空全部录取数据(包含学生基本信息、照片、体检表)</el-checkbox>
        <el-checkbox label="zsjh">清空招生计划数据</el-checkbox>
      </el-checkbox-group>
      <div style="height: 20px;line-height: 20px;color: red;margin: 20px 0;">请注意: 已清空数据不可恢复</div>
      <div class="dialog-footer">
        <el-button type="primary" @click="clearData">清 空</el-button>
      </div>
    </div>
</template>

<script>
import {baseUrl} from "@/config/env";
import {tddclear} from "@/api/ksgl/tdd";

export default {
    name: "daoruluqv",
    data() {
        return {
            activeName: 'first',
            uploadUrl:
                baseUrl +
                "/report/dashboard/import",
            form: {
                name: '',
                region: '',
                date1: '',
                date2: '',
                delivery: false,
                type: [],
                resource: '',
                desc: ''
            },
            checkList:[],
            nflist: [],
            nf:'',
        };
    },
    computed: {
        headers() {
            return {
                // Authorization: getToken() // 直接从本地获取token就行
            };
        },
    },
  created() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const specifiedNumber = currentYear;
    this.nflist = this.generateNumberArray(specifiedNumber);
  },
    methods: {
        clearData(){
            let data = this.checkList.join(',')
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                console.log(data)
                tddclear({
                    nf: this.nf,
                    qklx: data
                }).then(res=>{
                    if(res.data.success){
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                        this.$emit('onclose',true)
                    }else {
                        this.$message.error("操作失败！");
                    }
                })
            }).catch(() => {

            });
        },
        generateNumberArray(num) {
            const result = [];
            for (let i = num; i > num - 10; i--) {
                result.push({
                    label: i,
                    value: i,
                });
            }

            return result;
        },
    }
}
</script>

<style scoped>

</style>
