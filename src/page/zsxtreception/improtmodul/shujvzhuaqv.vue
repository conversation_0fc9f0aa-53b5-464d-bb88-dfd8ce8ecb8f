<template>
    <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="校验单" name="first">
                <el-form ref="form" :model="form" label-width="80px">
                    <el-form-item label="自动修正">
                        <el-switch v-model="form.delivery"></el-switch>
                    </el-form-item>
                    <el-form-item label="年份">
                        <el-select v-model="form.region" placeholder="请选择年份">
                            <el-option label="2020" value="2020"></el-option>
                            <el-option label="2021" value="2021"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="层次">
                        <el-radio-group v-model="form.resource">
                            <el-radio label="以控为主"></el-radio>
                            <el-radio label="自定义设置"></el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="类型">
                        <el-select v-model="form.region" placeholder="请选择类型">
                            <el-option label="2020" value="2020"></el-option>
                            <el-option label="2021" value="2021"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="">
                        <el-upload
                            class="upload-demo"
                            ref="upload"
                            :action="uploadUrl"
                            :headers="headers"
                            :on-success="handleUpload"
                            :on-error="handleError"
                            :show-file-list="false"
                            :limit="1"
                        >
                            <el-button size="small" type="primary">点击上传</el-button>
                            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
                        </el-upload>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="自定义" name="second">自定义</el-tab-pane>
            <el-tab-pane label="人像核验" name="third">人像核验</el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import {baseUrl} from "@/config/env";

export default {
    name: "daoruluqv",
    data() {
        return {
            activeName: 'first',
            uploadUrl:
                baseUrl +
                "/report/dashboard/import",
            form: {
                name: '',
                region: '',
                date1: '',
                date2: '',
                delivery: false,
                type: [],
                resource: '',
                desc: ''
            }
        };
    },
    computed: {
        headers() {
            return {
                // Authorization: getToken() // 直接从本地获取token就行
            };
        },
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        },
        // 上传成功的回调
        handleUpload(response, file, fileList) {
            //清除el-upload组件中的文件
            this.$refs.upload.clearFiles();
            //刷新大屏页面
            this.initEchartData();
            this.$message({
                message: "导入成功！",
                type: "success",
            });
        },
        handleError() {
            this.$message({
                message: "上传失败！",
                type: "error",
            });
        },
    }
}
</script>

<style scoped>

</style>