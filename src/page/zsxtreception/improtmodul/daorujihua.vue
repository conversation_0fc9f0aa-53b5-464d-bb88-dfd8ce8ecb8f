<template>
  <div v-loading="loading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="计划编制" name="first">
        <avue-form v-model="jhform" :option="jhoption">
          <template v-slot:file>
            <el-upload
                class="upload-demo"
                ref="jhupload"
                accept=".xlsx"
                action="/ksgl/enrollplan/import"
                :data="jhform"
                :on-change="handlechange"
                :on-remove="handleRemove"
                :on-success="handlesuccess"
                :on-error="handleerror"
                :file-list="fileList"
                :limit="1"
                :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
            </el-upload>
            <div class="tishibox">
              <p>系统支持导入教育部计划来源网导出完整“计划编制”</p>
              <p>下载路径：【来源计划网】计划编制管理→计划数编制→导出Excel</p>
            </div>
          </template>
        </avue-form>
      </el-tab-pane>
      <el-tab-pane label="计划调整" name="second">
        <avue-form v-model="jhform" :option="jhoption">
          <template v-slot:file>
            <el-upload
                class="upload-demo"
                ref="jhupload"
                accept=".xls/.xlsx"
                action="/ksgl/enrollplan/import"
                :data="jhform"
                :on-change="handlechange"
                :on-remove="handleRemove"
                :on-success="handlesuccess"
                :on-error="handleerror"
                :file-list="fileList"
                :limit="1"
                :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
            </el-upload>
            <div class="tishibox">
              <p>系统支持导入教育部计划来源网导出完整“计划调整”</p>
              <p>下载路径：【来源计划网】计划调整→调整明细→查询→导出Excel</p>
            </div>
          </template>
        </avue-form>
      </el-tab-pane>
      <el-tab-pane label="自定义计划" name="third">
        <avue-form v-model="jhform" :option="jhoption">
          <template v-slot:file>
            <el-upload
                class="upload-demo"
                ref="jhupload"
                accept=".xls/.xlsx"
                action="/ksgl/enrollplan/import"
                :data="jhform"
                :on-change="handlechange"
                :on-remove="handleRemove"
                :on-success="handlesuccess"
                :on-error="handleerror"
                :file-list="fileList"
                :limit="1"
                :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
              <div style="margin-top: 10px;">
                <el-button type="primary" @click="handleTemplate()">
                  点击下载模板<i class="el-icon-download el-icon--right"></i>
                </el-button>
              </div>
            </el-upload>
            <div class="tishibox">
              <p>系统支持导入自定义计划数据（自主招生、高校专项计划、保送生）</p>
            </div>
          </template>
        </avue-form>
      </el-tab-pane>
    </el-tabs>
    <div class="dialog-footer">
      <el-button type="primary" @click="submitform">导 入</el-button>
    </div>
  </div>
</template>

<script>

import {getAll} from "@/api/zsxt/ccdm";

export default {
  name: "daoruluqv",
  data() {
    return {
      fileList: [],
      loading: false,
      nflist:[],
      activeName: 'first',
      radio: 'tdd',
      value: null,
      dzyshow:false,
      options: [],
      jhform: {
      },
    };
  },
  computed: {
    headers() {
      return {
        // Authorization: getToken() // 直接从本地获取token就行
      };
    },
    jhoption(){
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: '年份',
            prop: 'nf',
            type: 'select',
            span: 24,
            dicData: this.nflist
          },
          {
            label: '',
            prop: 'file',
            type: 'upload',
            span: 24,
            slot: true
          },
        ]
      }
    },
  },
  created() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const specifiedNumber = currentYear;
    this.nflist = this.generateNumberArray(specifiedNumber);
    getAll().then(res=>{
      console.log(res.data)
      this.options = res.data.data;
    })
  },
  methods: {
    generateNumberArray(num) {
      const result = [];
      for (let i = num; i > num -10; i--) {
        result.push({
          label: i,
          value: i,
        });
      }
      return result;
    },
    handlechange(file, fileList){
      console.log(`handlechange`,file, fileList);
      this.fileList = fileList;
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileList = [];
    },
    submitform(){
      this.loading = true;
      if(this.activeName == 'first'){
        this.jhform.type = 'jhbz'
        this.$refs.jhupload.submit();
      }else if(this.activeName == 'second'){
        this.jhform.type = 'jhtz'
        this.$refs.jhupload.submit();
      }else if(this.activeName == 'third'){
        this.jhform.type = 'zdy'
        if(!this.jhform.nf){
          this.$message({
            message: '请选择年份',
            type: 'warning'
          });
        }
        this.$refs.jhupload.submit();
      }

    },
    handlesuccess(response){
      console.log(`handlesuccess`,response)
      if(response.code == 200 || response.code == '00000'){
        this.loading = false;
        this.$message({
          message: '导入成功',
          type: 'success'
        });
        this.$emit('onclose',true)
      }else {
        this.loading = false;
        this.$message.error('导入失败');
        this.$emit('onclose',true)
      }
    },
    handleerror(err){
      console.log(`handleerror`,err)
      this.loading = false;
      this.$message.error('导入失败');
      this.$emit('onclose',true)
    },
    handleTemplate() {
      window.open(`/ksgl/enrollplan/downImportTemplate`);
    },
  }
}
</script>

<style scoped>
.tishibox{
  width: 100%;
  height: 100%;
  border: 1px solid #00a5ec;
  background-color: #E6F7FF;
  font-size: 12px;
}
.tishibox p{
  margin: 5px 10px;
  line-height: 20px;
}
</style>