<template>
  <div v-loading="loading">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="类型">
        <el-radio-group v-model="form.fileType">
          <el-radio label="zsxt_bmxx">报名表</el-radio>
          <el-radio label="zsxt_tjxx">体检表</el-radio>
          <el-radio label="zsxt_cjyzyxx">成绩与志愿表</el-radio>
          <el-radio label="zsxt_sfz">身份证</el-radio>
          <el-radio label="zsxt_fjb">附加表</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="">
        <el-upload
            class="upload-demo"
            drag
            ref="upload"
            accept=".zip"
            :data="form"
            action="/ksgl/tdd/importPhotos"
            :on-change="handlechange"
            :on-remove="handleRemove"
            :on-success="handlesuccess"
            :on-error="handleerror"
            :file-list="fileList"
            :limit="1"
            :auto-upload="false">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
<!--          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>-->
          <div slot="tip" class="el-upload__tip">请选择 zip/rar压缩包，最大不能超过500M</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="primary" @click="submitform">导 入</el-button>
    </div>
  </div>
</template>

<script>
import {baseUrl} from "@/config/env";

export default {
  name: "daoruluqv",
  data() {
    return {
      activeName: 'first',
      loading: false,
      uploadUrl:
          baseUrl +
          "/report/dashboard/import",
      form: {

      },
      fileList: [],

    };
  },
  computed: {
    headers() {
      return {
        // Authorization: getToken() // 直接从本地获取token就行
      };
    },
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    handlechange(file, fileList){
      console.log(`handlechange`,file, fileList);
      this.fileList = fileList;
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileList = [];
    },
    submitform(){
      console.log(this.form.fileType)
      this.loading = true;
      this.$refs.upload.submit();

    },
    handlesuccess(response){
      console.log(`handlesuccess`,response)
      if(response.code == 200 || response.code == '00000'){
        this.loading = false;
        this.$message({
          message: '导入成功',
          type: 'success'
        });
        this.$emit('onclose',true)
      }else {
        this.loading = false;
        this.$message.error('导入失败');
        this.$emit('onclose',true)
      }
    },
    handleerror(err){
      console.log(`handleerror`,err)
      this.loading = false;
      this.$message.error('导入失败');
      this.$emit('onclose',true)
    },
  }
}
</script>

<style scoped>

</style>