<template>
  <div v-loading="loading">
    <el-upload
        class="upload-demo"
        drag
        ref="upload"
        accept=".jpg,.png"
        action="/file/uploadByType"
        :data="form"
        :on-change="handlechange"
        :on-remove="handleRemove"
        :on-success="handlesuccess"
        :on-error="handleerror"
        :file-list="fileList"
        :limit="1"
        :auto-upload="false">
      <!--      <el-button slot="trigger" size="small" type="primary">选取文件</el-button>-->
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div slot="tip" class="el-upload__tip">图片仅支持jpg和png格式，最大不能超过1M</div>
    </el-upload>
    <div class="dialog-footer" style="margin-top: 20px;">
      <el-button type="primary" @click="submitform">导 入</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "daoruluqv",
  props:{
    fileType: String,
  },
  data() {
    return {
      activeName: 'first',
      loading: false,
      form: {
        fileType: '',
      },
      fileList: [],

    };
  },
  computed: {
    headers() {
      return {
        // Authorization: getToken() // 直接从本地获取token就行
      };
    },
  },
  methods: {
    handlechange(file, fileList){
      console.log(`handlechange`,file, fileList);
      this.fileList = fileList;
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileList = [];
    },
    submitform(){
      this.loading = true;
      this.form.fileType = this.fileType
      this.$refs.upload.submit();
    },
    handlesuccess(response){
      console.log(`handlesuccess`,response)
      if(response.code == 200 || response.code == '00000'){
        this.loading = false;
        this.$message({
          message: '导入成功',
          type: 'success'
        });
        this.$emit('onclose',true)
      }else {
        this.loading = false;
        this.$message.error('导入失败');
        this.$emit('onclose',true)
      }
    },
    handleerror(err){
      console.log(`handleerror`,err)
      this.loading = false;
      this.$message.error('导入失败');
      this.$emit('onclose',true)
    },
  }
}
</script>

<style scoped>

</style>