<template>
    <section>
        <div class="wrapAlls">
            <div class="boxs">
                <div class="content">
                    <div class="content_title">
                        <span class="head_style">
                          <img width="50" src="../../styles/assets/image/u64.png">
                        </span>
                        <div class="title_left">
                            <p class="title_left_p1">招生数据总览</p>
                            <p class="title_left_p2">范围：全院系学生</p>
                        </div>
                        <div class="title_right">
                          <span>统计规则：</span>
<!--                          <el-select-->
<!--                                class="content_select"-->
<!--                                v-model="value"-->
<!--                                placeholder="年份范围"-->
<!--                            >-->
<!--                                <el-option-->
<!--                                v-for="item in options"-->
<!--                                :key="item.value"-->
<!--                                :label="item.label"-->
<!--                                :value="item.value"-->
<!--                                >-->
<!--                                </el-option>-->
<!--                            </el-select>-->
<!--                            <el-select-->
<!--                                class="content_select"-->
<!--                                v-model="value"-->
<!--                                placeholder="分数范围"-->
<!--                            >-->
<!--                                <el-option-->
<!--                                    v-for="item in options"-->
<!--                                    :key="item.value"-->
<!--                                    :label="item.label"-->
<!--                                    :value="item.value"-->
<!--                                >-->
<!--                                </el-option>-->
<!--                            </el-select>-->
<!--                            <el-select-->
<!--                                class="content_select"-->
<!--                                v-model="value"-->
<!--                                placeholder="统计规则"-->
<!--                            >-->
<!--                                <el-option-->
<!--                                    v-for="item in options"-->
<!--                                    :key="item.value"-->
<!--                                    :label="item.label"-->
<!--                                    :value="item.value"-->
<!--                                >-->
<!--                                </el-option>-->
<!--                            </el-select>-->
<!--                            <el-date-picker-->
<!--                                class="content_picker"-->
<!--                                v-model="value"-->
<!--                                type="daterange"-->
<!--                                align="right"-->
<!--                                unlink-panels-->
<!--                                range-separator="至"-->
<!--                                start-placeholder="开始日期"-->
<!--                                end-placeholder="结束日期"-->
<!--                                :picker-options="pickerOptions"-->
<!--                                @change="changetime">-->
<!--                            </el-date-picker>-->
                            <el-button type="primary" @click="searchtime">查询</el-button>
<!--                            <el-button type="primary" @click="crudexport">导出</el-button>-->
                        </div>
                    </div>
                    <div style="display: flex;flex-flow:row wrap;">
                        <div style="height: 100%;border-radius: 5px;width: 49.5%;">
                            <div class="contentinner0">
                                <div class="content_body">
                                    <div class="content_body_box">
                                        <span class="content_body_span">年度招生数据</span>
                                    </div>
                                    <div style="height: 210px;display: flex;justify-content: space-around">
                                        <div style="width: 33%;height: 100%;text-align: center;">
                                            <div style="font-size: 16px;color: #7F7F7F;height: 60px;line-height: 60px;">
                                                计划人数（含调整）
                                            </div>
                                            <div style="font-size: 30px;font-weight: bold;height: 80px;line-height: 80px;border-right: 1px solid #EBEEF5;">
                                                3689
                                            </div>
                                            <div style="font-size: 16px;color: #7F7F7F;height: 60px;line-height: 60px;">
                                                比去年↑46
                                            </div>
                                        </div>
                                        <div style="width: 33%;height: 100%;text-align: center;">
                                            <div style="font-size: 16px;color: #7F7F7F;height: 60px;line-height: 60px;">
                                                录取人数
                                            </div>
                                            <div style="font-size: 30px;font-weight: bold;height: 80px;line-height: 80px;border-right: 1px solid #EBEEF5;">
                                                3689
                                            </div>
                                            <div style="font-size: 16px;color: #7F7F7F;height: 60px;line-height: 60px;">
                                                比去年↑46
                                            </div>
                                        </div>
                                        <div style="width: 33%;height: 100%;text-align: center;">
                                            <div style="font-size: 16px;color: #7F7F7F;height: 60px;line-height: 60px;">
                                                报到人数
                                            </div>
                                            <div style="font-size: 30px;font-weight: bold;height: 80px;line-height: 80px;">
                                                3689
                                            </div>
                                            <div style="font-size: 16px;color: #7F7F7F;height: 60px;line-height: 60px;">
                                                比去年↑46
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="contentinner_1">
                                <div class="contentinner_1_0">
                                    <div class="content_body">
                                        <div class="content_body_box">
                                            <span class="content_body_span">近3年录取人数</span>
                                        </div>
                                        <div style="height: 210px">
                                            <v-chart :options="luqvoptions" autoresize/>
                                        </div>
                                    </div>
                                </div>
                                <div class="contentinner_1_1">
                                    <div class="content_body">
                                        <div class="content_body_box">
                                            <span class="content_body_span">近3年报到人数</span>
                                        </div>
                                        <div style="height: 210px">
                                            <v-chart :options="baodaooptions" autoresize/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner1">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">各省份人数分布</span>
                                </div>
                                <div style="height: 500px;display: flex;justify-content: space-around">
                                    <v-chart :options="dituoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner2">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">按性别统计</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="xingbiepieoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner3">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">按民族统计</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="minzupieoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner2">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">考生类别统计</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="baodaooptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner3">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">按批次统计</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="picipieoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner4">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">按科类统计</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="treemapoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner4">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">各省招生计划</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="barlineoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner2">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">第一志愿报考率</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="baokaolvoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <div class="contentinner3">
                            <div class="content_body">
                                <div class="content_body_box">
                                    <span class="content_body_span">第一志愿录取率</span>
                                </div>
                                <div style="height: 270px">
                                    <v-chart :options="luqvlvoptions" autoresize/>
                                </div>
                            </div>
                        </div>
                        <footer class="foot" style="height: 30px;">
                              <span>
                                ©2024 武汉学院-版权所有-技术支持：三易拓科技
                              </span>
                        </footer>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import "../../../node_modules/echarts/map/js/china.js"
import {getStatisticsData} from "@/api/dataWarningWarnInfo";
import {GetSysParamNoLogin} from "@/api/sysParam";

var data = [
    {name: '北京', value: 2154},
    {name: '天津', value: 1135},
    {name: '上海', value: 2424},
    {name: '重庆', value: 1303},
    {name: '河北', value: 7185},
    // 其他省份数据...
];

export default {
    name: "index",
    components: {
    },
    data() {
        return {
            dbxx: ``,
            dialogHandle: false,
            queryParam:{},
            options: ['123'],
            value: "",
            page: {
                currentPage: 1,
                total: 5,
                pageSize: 20,
            },
            data: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            option: {
                searchShowBtn:false,
                menu: false,
                align: "left",
                menuAlign: "center",
                border: true,
                addBtn: false,
                searchBtn: false,
                refreshBtn: false,
                columnBtn: false,
                stripe: true,
                column: [
                    {
                        label:'序号',
                        prop:'index',
                        width: 50,
                        fixed:true
                    },
                    {
                        label: "预警类型",
                        prop: "warnType",
                    },
                    {
                        label: "预警人数",
                        prop: "rs",
                    },
                    {
                        label: "预警人次",
                        prop: "rc",
                    },
                    {
                        label: "总人数",
                        prop: "zrs",
                    },
                    {
                        label: "总人次",
                        prop: "zrc",
                    },
                    {
                        label: "预警人数百分比",
                        prop: "yjrsb",
                    },
                    {
                        label: "预警人次百分比",
                        prop: "yjrcb",
                    },
                ],
            },
            dituoptions: {
                title: {
                 //   text: '中国各省份人数分布',
                  //  left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: false,
                    orient: 'vertical',
                    left: 'left',
                    data:['人数'],
                },
                visualMap: {
                    type: 'piecewise',
                    pieces: [
                        {min: 5000, label: '5000人以上', color: '#45A5F8'},
                        {min: 2000, max: 4999, label: '2000 - 4999人', color: '#8AC6FD'},
                        {min: 1000, max: 1999, label: '1000 - 1999人', color: '#D2EAFF'},
                        {min: 500, max: 999, label: '500 - 999人', color: '#B2CAE0'},
                        {max: 499, label: '499人以下', color: '#eeeeee'}
                    ],
                    color: ['#E0022B', '#E09107', '#A3E00B'],
                },
                series: [
                    {
                        name: '人数',
                        type: 'map',
                        mapType: 'china',
                        roam: false,
                        data: data,
                        label: {
                            show: true,
                            formatter: '{b}\n{c}'
                        }
                    }
                ]
            },
            luqvoptions: {
                title: {
                },
                tooltip: {},
                xAxis: {
                    data: ['一月', '二月', '三月', '四月', '五月']
                },
                yAxis: {
                    axisLine: {
                        show: false,
                    }
                },
                series: [{
                    name: '销售额',
                    type: 'bar',
                    data: [120, 200, 150, 80, 70],
                    barWidth: '50%',
                    itemStyle: {
                        color: '#6395FA',
                        barBorderRadius: [25, 25, 0, 0] // 从左上角开始顺时针设置四个角的圆角半径
                    }
                }]
            },
            baodaooptions: {
                title: {
                },
                tooltip: {},
                xAxis: {
                    data: ['一月', '二月', '三月', '四月', '五月']
                },
                yAxis: {
                    axisLine: {
                        show: false,
                    }
                },
                series: [{
                    name: '销售额',
                    type: 'bar',
                    data: [120, 200, 150, 80, 70],
                    barWidth: '50%',
                    itemStyle: {
                        color: '#6395FA',
                        barBorderRadius: [25, 25, 0, 0] // 从左上角开始顺时针设置四个角的圆角半径
                    }
                }]
            },
            xingbiepieoptions:{
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    right: 'right',
                    top: 40,
                    itemWidth: 15,
                    itemHeight: 15,
                    // bottom: 20,
                    // data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 40,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 1048, name: 'Search Engine',itemStyle: { color: '#6395FA' } },
                            { value: 735, name: 'Direct',itemStyle: { color: '#62DAAB' } },
                            { value: 580, name: 'Email',itemStyle: { color: '#657798' } },
                            { value: 484, name: 'Union Ads',itemStyle: { color: '#F7C122' } },
                            { value: 300, name: 'Video Ads',itemStyle: { color: '#7666FA' } }
                        ]
                    }
                ]
            },
            minzupieoptions:{
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    right: 'right',
                    top: 40,
                    itemWidth: 15,
                    itemHeight: 15,
                    // bottom: 20,
                    // data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 40,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 1048, name: 'Search Engine',itemStyle: { color: '#6395FA' } },
                            { value: 735, name: 'Direct',itemStyle: { color: '#62DAAB' } },
                            { value: 580, name: 'Email',itemStyle: { color: '#657798' } },
                            { value: 484, name: 'Union Ads',itemStyle: { color: '#F7C122' } },
                            { value: 300, name: 'Video Ads',itemStyle: { color: '#7666FA' } }
                        ]
                    }
                ]
            },
            picipieoptions:{
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    right: 'right',
                    top: 40,
                    itemWidth: 15,
                    itemHeight: 15,
                    // bottom: 20,
                    // data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 40,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 1048, name: 'Search Engine',itemStyle: { color: '#6395FA' } },
                            { value: 735, name: 'Direct',itemStyle: { color: '#62DAAB' } },
                            { value: 580, name: 'Email',itemStyle: { color: '#657798' } },
                            { value: 484, name: 'Union Ads',itemStyle: { color: '#F7C122' } },
                            { value: 300, name: 'Video Ads',itemStyle: { color: '#7666FA' } }
                        ]
                    }
                ]
            },
            barlineoptions:{
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                legend: {
                    data: ['Evaporation', 'Precipitation', 'Temperature']
                },
                xAxis: [
                    {
                        type: 'category',
                        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        axisPointer: {
                            type: 'shadow'
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: 'Precipitation',
                        min: 0,
                        max: 250,
                        interval: 50,
                        axisLabel: {
                            formatter: '{value} ml'
                        }
                    },
                    {
                        type: 'value',
                        name: 'Temperature',
                        min: 0,
                        max: 25,
                        interval: 5,
                        axisLabel: {
                            formatter: '{value} °C'
                        }
                    }
                ],
                series: [
                    {
                        name: 'Evaporation',
                        type: 'bar',
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + ' ml';
                            }
                        },
                        itemStyle: {
                            color: '#6395FA',
                            barBorderRadius: [25, 25, 0, 0] // 从左上角开始顺时针设置四个角的圆角半径
                        },
                        data: [
                            2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                        ]
                    },
                    {
                        name: 'Precipitation',
                        type: 'bar',
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + ' ml';
                            }
                        },
                        itemStyle: {
                            color: '#62DAAB',
                            barBorderRadius: [25, 25, 0, 0] // 从左上角开始顺时针设置四个角的圆角半径
                        },
                        data: [
                            2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                        ]
                    },
                    {
                        name: 'Temperature',
                        type: 'line',
                        yAxisIndex: 1,
                        tooltip: {
                            valueFormatter: function (value) {
                                return value + ' °C';
                            }
                        },
                        lineStyle: {
                            type: 'dashed'  // 设置为虚线，可选值还包括 'solid', 'dotted' 等
                        },
                        itemStyle: {
                            color: '#F7C122',
                            barBorderRadius: [25, 25, 0, 0] // 从左上角开始顺时针设置四个角的圆角半径
                        },
                        data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
                    }
                ]
            },
            baokaolvoptions:{
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: false,
                    orient: 'vertical',
                    right: 'right',
                    top: 40,
                    itemWidth: 15,
                    itemHeight: 15,
                    // bottom: 20,
                    // data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 40,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 1048, name: 'Search Engine',itemStyle: { color: '#6395FA' } },
                            { value: 735, name: 'Direct',itemStyle: { color: '#62DAAB' } },
                            { value: 580, name: 'Email',itemStyle: { color: '#657798' } },
                            { value: 484, name: 'Union Ads',itemStyle: { color: '#F7C122' } },
                            { value: 300, name: 'Video Ads',itemStyle: { color: '#7666FA' } }
                        ]
                    }
                ]
            },
            luqvlvoptions:{
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: false,
                    orient: 'vertical',
                    right: 'right',
                    top: 40,
                    itemWidth: 15,
                    itemHeight: 15,
                    // bottom: 20,
                    // data: data.map(item => item.name)
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 40,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 1048, name: 'Search Engine',itemStyle: { color: '#6395FA' } },
                            { value: 735, name: 'Direct',itemStyle: { color: '#62DAAB' } },
                            { value: 580, name: 'Email',itemStyle: { color: '#657798' } },
                            { value: 484, name: 'Union Ads',itemStyle: { color: '#F7C122' } },
                            { value: 300, name: 'Video Ads',itemStyle: { color: '#7666FA' } }
                        ]
                    }
                ]
            },
            treemapoptions:{
                series: [
                    {
                        type: 'treemap',
                        data: [
                            {
                                name: '根节点',
                                children: [
                                    {
                                        name: '节点1',
                                        value: 100,
                                        children: [
                                            { name: '叶子节点1', value: 30 },
                                            { name: '叶子节点2', value: 40 }
                                        ]
                                    },
                                    {
                                        name: '节点2',
                                        value: 50,
                                        children: [
                                            { name: '叶子节点3', value: 20 },
                                            { name: '叶子节点4', value: 30 }
                                        ]
                                    }
                                ]
                            }
                        ],
                        color: ['#6395FA', '#62DAAB', '#657798', '#F7C122'] // 设置颜色数组，每个矩形对应一个颜色
                    }
                ]
            }
        };
    },
    created() {
        this.getList();
        this.footP(this.footParam());
    },
    mounted() {},

    methods: {
        footP(param) {
            GetSysParamNoLogin(param).then((res) => {
                this.dbxx = res.data.info.value;
            });
        },
        footParam() {
            return {
                idOrNameOrType: "dbxx",
            };
        },
        getList(param) {
            document.title = '预警统计';
            getStatisticsData(param).then(res => {
                const data = res.data.info;
                this.page.currentPage = 1;
                this.page.total = 1;
                this.page.pageSize = 20;
                this.data = data;
            })
        },
        changetime(){
            if( this.value !== null ) {
                let a = new Date(this.value[0]);
                let b = new Date(this.value[1]);
                let startM = (a.getMonth() + 1) < 10 ? '0' + (a.getMonth() + 1) : (a.getMonth() + 1)
                let startD = a.getDate() < 10 ? '0' + a.getDate() : a.getDate()
                let endM = (b.getMonth() + 1) < 10 ? '0' + (b.getMonth() + 1) : (b.getMonth() + 1)
                let endD = b.getDate() < 10 ? '0' + b.getDate() : b.getDate()
                let start = a.getFullYear() + '-' + startM + '-' + startD;
                let end = b.getFullYear() + '-' + endM + '-' + endD;
                this.queryParam.startTime = start;
                this.queryParam.endTime = end;
            } else {
                this.queryParam.startTime = '';
                this.queryParam.endTime = '';
            }
        },
        searchtime(){
            this.getList(this.queryParam)
        },
        crudexport(){
            window.open(`/dataWarning/warnInfo/exportStatisticsData`);
        },
    },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
::v-deep .avue-crud__menu {
    display: none;
}
.content_body_box{
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 1px solid #EBEEF5;
}
.content_body_span{
    margin-right: 40px;
}
.wrapAlls {
    // height: calc(100% - 60px);
    position: absolute;
    //top: 40px;
    left: 0;
    right: 0;
    height: calc(100vh - 60px);
    //   bottom: -60px;
    // height: 100vh;
    overflow: hidden;
    background: #F0F2F5;
}
.boxs {
    height: calc(100vh - 60px);
    overflow-y: auto;
    background: #F0F2F5;
}
.foot {
    height: 30px;
    //background: rgba(240, 239, 239, 0.7);
    //background: rgb(240, 242, 245);
    width: 100%;
    font-size: 12px;
    line-height: 30px;
    color: rgb(147, 150, 149);
    text-align: center;
}
.nav {
    height: 50px;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #E9E9E9;
}
.nav_h1 {
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    margin-left: 50px;
}
.nav_h2 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 50px;
    margin-right: 15px;
    color: rgb(147, 150, 149);
}
.nav_h3 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
    color: rgb(147, 150, 149);
}
.nav_h4 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
}
.nav_h5{
    font-size: 14px;
    color: rgb(147, 150, 149);
}
.content {
    width: 100%;
    // height: 100%;
    min-height: calc(100vh - 90px);
    padding: 15px;
    background: #F0F2F5;
}
.wrapAll {
    padding: 0 !important;
}
.contentinner {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 100%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.contentinner0 {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 100%;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.contentinner_1 {
    position: relative;
    height: 270px;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    //background-color: #fff;
    //box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    //border: 1px solid #00a5ec;
}
.contentinner_1_0{
    position: relative;
    height: 270px;
    border-radius: 5px;
    width: 49%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    //border: 1px solid #00a5ec;
}
.contentinner_1_1{
    position: relative;
    height: 270px;
    border-radius: 5px;
    width: 49%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    //border: 1px solid #00a5ec;
}
.contentinner1 {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 49.5%;
    margin-left: 1%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.contentinner2 {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 49.50%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    margin-bottom: 20px;
}
.contentinner3{
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 49.50%;
    margin-left: 1%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    margin-bottom: 20px;

}
.contentinner4{
    position: relative;
    height: 100%;
    border-radius: 5px;
    width: 100%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    margin-bottom: 20px;
}
.content_title {
    height: 90px;
    margin-bottom: 15px;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.head_style {
    display: inline-block;
    float: left;
    width:90px;
    height:90px;
    background: linear-gradient(-57.3792725189961deg, rgba(255, 255, 255, 1) 34%, rgba(204, 220, 253, 0.5) 100%);
}
.head_style img {position: relative;top:18px;left:20px;}
.title_left{
    float: left;
    padding-top:12px;
}
.title_left_p1{
    font-size: 22px;
    font-weight: bold;
    margin-left: 5px;
    margin-top: 5px;
}
.title_left_p2{
    font-size: 14px;
    color: #b4b2b2;
    margin-left: 5px;
    margin-top: 8px;
}
.title_right{
    float: right;
    margin-top: 30px;
    margin-right: 15px;
}
.title_right>button{
    margin: 0 10px 0 10px;
}
.content_titleLeft {
    font-size: 20px;
    font-weight: bold;
}
.content_titleRigth {
    // float: right;
    font-size: 18px;
    position: absolute;
    right: 55px;
}
.content_body {
    margin: 0 20px 0 20px;
}
.content_span1 {
    font-size: 16px;
}
.content_span2 {
    display: inline-block;
    width: 100px;
    line-height: 30px;
    background-color: rgb(148, 151, 151);
    border-radius: 3px;
    color: #fff;
    text-align: center;
    margin-right: 5px;
}
.content_span3 {
    display: inline-block;
    width: 100px;
    line-height: 30px;
    background-color: rgba(233, 233, 233, 0.795);
    border-radius: 3px;
    color: #333;
    text-align: center;
    margin-right: 5px;
}
.content_line {
    margin-bottom: 10px;
}
.content_select {
    margin-right: 5px;
}
.content_picker {
    height: 30px;
    // margin-top: 10px;
    vertical-align: top;
}
::v-deep .avue-crud .el-table th {
    font-size: 14px;}
::v-deep .avue-crud .el-table td {
    font-size: 12px;}
::v-deep .avue-crud{
    margin-top: 10px;
}
::v-deep .el-input__icon {
    line-height: 22px;
}
::v-deep .el-range-separator {
    line-height: 22px;
    width: 20px;
}
.content_input {
    display: inline-block;
    width: 200px;
    margin-right: 5px;
}
::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}
::v-deep  .cell-point{
    color: #255bc6;
    text-decoration: underline;
    cursor: pointer;
}
::v-deep .el-card__body .el-collapse-item__wrap{
    display: none !important;
}
.echarts {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
</style>
