<template>
    <section>
        <div class="wrapAlls">
            <div class="boxs">
                <div class="content">
                    <div class="content_title">
                        <div class="title_left" style="margin-left: 30px;">
                            <p class="title_left_p1">生源质量报告</p>
                        </div>
                        <div class="title_right">
                            <!--                          <el-select-->
                            <!--                                class="content_select"-->
                            <!--                                v-model="value"-->
                            <!--                                placeholder="年份范围"-->
                            <!--                            >-->
                            <!--                                <el-option-->
                            <!--                                v-for="item in options"-->
                            <!--                                :key="item.value"-->
                            <!--                                :label="item.label"-->
                            <!--                                :value="item.value"-->
                            <!--                                >-->
                            <!--                                </el-option>-->
                            <!--                            </el-select>-->
                            <!--                            <el-select-->
                            <!--                                class="content_select"-->
                            <!--                                v-model="value"-->
                            <!--                                placeholder="分数范围"-->
                            <!--                            >-->
                            <!--                                <el-option-->
                            <!--                                    v-for="item in options"-->
                            <!--                                    :key="item.value"-->
                            <!--                                    :label="item.label"-->
                            <!--                                    :value="item.value"-->
                            <!--                                >-->
                            <!--                                </el-option>-->
                            <!--                            </el-select>-->
                            <!--                            <el-select-->
                            <!--                                class="content_select"-->
                            <!--                                v-model="value"-->
                            <!--                                placeholder="统计规则"-->
                            <!--                            >-->
                            <!--                                <el-option-->
                            <!--                                    v-for="item in options"-->
                            <!--                                    :key="item.value"-->
                            <!--                                    :label="item.label"-->
                            <!--                                    :value="item.value"-->
                            <!--                                >-->
                            <!--                                </el-option>-->
                            <!--                            </el-select>-->
                            <!--                            <el-date-picker-->
                            <!--                                class="content_picker"-->
                            <!--                                v-model="value"-->
                            <!--                                type="daterange"-->
                            <!--                                align="right"-->
                            <!--                                unlink-panels-->
                            <!--                                range-separator="至"-->
                            <!--                                start-placeholder="开始日期"-->
                            <!--                                end-placeholder="结束日期"-->
                            <!--                                :picker-options="pickerOptions"-->
                            <!--                                @change="changetime">-->
                            <!--                            </el-date-picker>-->
                            <el-button type="primary" @click="searchtime">查询</el-button>
                            <!--                            <el-button type="primary" @click="crudexport">导出</el-button>-->
                        </div>
                    </div>
                    <div style="height: 100%;margin-bottom: 10px;display: flex;flex-flow:row wrap;justify-content: space-between;">
                        <div class="content_left">
                            <el-tree
                                :data="treedata"
                                show-checkbox
                                node-key="id"
                                :default-expanded-keys="[2, 3]"
                                :default-checked-keys="[5]"
                                :props="defaultProps">
                            </el-tree>
                        </div>
                        <div class="content_right">
                            <h5 style="text-align: center;font-weight: bold;font-size: 26px;line-height: 50px;">
                                武汉学院2023年招生数据分析报告
                            </h5>
                            <h4 style="text-align: left;font-weight: bold;font-size: 24px;line-height: 50px;">
                                一、招生基本情况
                            </h4>
                            <h3 style="text-align: left;font-weight: bold;font-size: 20px;line-height: 50px;">
                                (一)、我校招生情况
                            </h3>
                            <p style="line-height: 25px;">
                                2023年我校招生计划为3171人，录取人数为3271人（其中男生1398人，女生2317人，省内2581人，省个1134人）；面向全国21个省市，共38个专业招生。
                            </p>
                            <p style="line-height: 25px;">
                                2023年我校共有3个招生类型，其中普通类3230人，体育类100人，艺术类385人。
                            </p>
                            <avue-crud
                                :data="data"
                                :option="option"
                            >
                            </avue-crud>
                            <h3 style="text-align: center;font-weight: bold;font-size: 20px;line-height: 50px;">
                                2021-2023年招生类型人数对比
                            </h3>
                            <avue-crud
                                :data="data"
                                :option="option"
                            >
                            </avue-crud>
                        </div>
                    </div>

                    <div style="display: flex;flex-flow:row wrap;">
                        <footer class="foot" style="height: 30px;">
                              <span>
                                ©2024 武汉学院-版权所有-技术支持：三易拓科技
                              </span>
                        </footer>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import {getStatisticsData} from "@/api/dataWarningWarnInfo";
import {GetSysParamNoLogin} from "@/api/sysParam";


export default {
    name: "shenyuanzhilinag",
    components: {
    },
    data() {
        return {
            dbxx: ``,
            treedata: [{
                id: 1,
                label: '一级 1',
                children: [{
                    id: 4,
                    label: '二级 1-1',
                    children: [{
                        id: 9,
                        label: '三级 1-1-1'
                    }, {
                        id: 10,
                        label: '三级 1-1-2'
                    }]
                }]
            }, {
                id: 2,
                label: '一级 2',
                children: [{
                    id: 5,
                    label: '二级 2-1'
                }, {
                    id: 6,
                    label: '二级 2-2'
                }]
            }, {
                id: 3,
                label: '一级 3',
                children: [{
                    id: 7,
                    label: '二级 3-1'
                }, {
                    id: 8,
                    label: '二级 3-2'
                }]
            }],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            dialogHandle: false,
            queryParam:{},
            options: ['123'],
            value: "",
            page: {
                currentPage: 1,
                total: 5,
                pageSize: 20,
            },
            data: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            option: {
                searchShowBtn:false,
                menu: false,
                align: "left",
                menuAlign: "center",
                border: true,
                addBtn: false,
                searchBtn: false,
                refreshBtn: false,
                columnBtn: false,
                stripe: true,
                column: [
                    {
                        label:'序号',
                        prop:'index',
                        width: 50,
                        fixed:true
                    },
                    {
                        label: "预警类型",
                        prop: "warnType",
                    },
                    {
                        label: "预警人数",
                        prop: "rs",
                    },
                    {
                        label: "预警人次",
                        prop: "rc",
                    },
                    {
                        label: "总人数",
                        prop: "zrs",
                    },
                    {
                        label: "总人次",
                        prop: "zrc",
                    },
                    {
                        label: "预警人数百分比",
                        prop: "yjrsb",
                    },
                    {
                        label: "预警人次百分比",
                        prop: "yjrcb",
                    },
                ],
            },


        };
    },
    created() {
        this.getList();
        this.footP(this.footParam());
    },
    mounted() {},

    methods: {
        footP(param) {
            GetSysParamNoLogin(param).then((res) => {
                this.dbxx = res.data.info.value;
            });
        },
        footParam() {
            return {
                idOrNameOrType: "dbxx",
            };
        },
        getList(param) {
            document.title = '预警统计';
            getStatisticsData(param).then(res => {
                const data = res.data.info;
                this.page.currentPage = 1;
                this.page.total = 1;
                this.page.pageSize = 20;
                this.data = data;
            })
        },
        changetime(){
            if( this.value !== null ) {
                let a = new Date(this.value[0]);
                let b = new Date(this.value[1]);
                let startM = (a.getMonth() + 1) < 10 ? '0' + (a.getMonth() + 1) : (a.getMonth() + 1)
                let startD = a.getDate() < 10 ? '0' + a.getDate() : a.getDate()
                let endM = (b.getMonth() + 1) < 10 ? '0' + (b.getMonth() + 1) : (b.getMonth() + 1)
                let endD = b.getDate() < 10 ? '0' + b.getDate() : b.getDate()
                let start = a.getFullYear() + '-' + startM + '-' + startD;
                let end = b.getFullYear() + '-' + endM + '-' + endD;
                this.queryParam.startTime = start;
                this.queryParam.endTime = end;
            } else {
                this.queryParam.startTime = '';
                this.queryParam.endTime = '';
            }
        },
        searchtime(){
            this.getList(this.queryParam)
        },
        crudexport(){
            window.open(`/dataWarning/warnInfo/exportStatisticsData`);
        },
    },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
::v-deep .avue-crud__menu {
    display: none;
}
.content_body_box{
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid #EBEEF5;
}
.content_body_span{
    margin-right: 40px;
}
.wrapAlls {
    // height: calc(100% - 60px);
    position: absolute;
    //top: 40px;
    left: 0;
    right: 0;
    height: calc(100vh - 60px);
    //   bottom: -60px;
    // height: 100vh;
    overflow: hidden;
    background: #F0F2F5;
}
.boxs {
    height: calc(100vh - 60px);
    overflow-y: auto;
    background: #F0F2F5;
}
.foot {
    height: 30px;
    //background: rgba(240, 239, 239, 0.7);
    //background: rgb(240, 242, 245);
    width: 100%;
    font-size: 12px;
    line-height: 30px;
    color: rgb(147, 150, 149);
    text-align: center;
}
.nav {
    height: 50px;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #E9E9E9;
}
.nav_h1 {
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    margin-left: 50px;
}
.nav_h2 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 50px;
    margin-right: 15px;
    color: rgb(147, 150, 149);
}
.nav_h3 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
    color: rgb(147, 150, 149);
}
.nav_h4 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
}
.nav_h5{
    font-size: 14px;
    color: rgb(147, 150, 149);
}
.content {
    width: 100%;
    // height: 100%;
    min-height: calc(100vh - 90px);
    padding: 15px;
    background: rgba(240, 239, 239, 0.7);
}
.wrapAll {
    padding: 0 !important;
}
.contentinner {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 100%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.contentinner0 {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 100%;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.contentinner_1 {
    position: relative;
    height: 270px;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    //background-color: #fff;
    //box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    //border: 1px solid #00a5ec;
}
.contentinner_1_0{
    position: relative;
    height: 270px;
    border-radius: 5px;
    width: 49%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    //border: 1px solid #00a5ec;
}
.contentinner_1_1{
    position: relative;
    height: 270px;
    border-radius: 5px;
    width: 49%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    //border: 1px solid #00a5ec;
}
.contentinner1 {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 49.5%;
    margin-left: 1%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.contentinner2 {
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 49.50%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    margin-bottom: 20px;
}
.contentinner3{
    position: relative;
    height: 100%;
    border-radius: 5px;
    // min-height: calc(100vh - 150px);
    width: 49.50%;
    margin-left: 1%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    margin-bottom: 20px;

}
.contentinner4{
    position: relative;
    height: 100%;
    border-radius: 5px;
    width: 100%;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
    margin-bottom: 20px;
}
.content_title {
    height: 80px;
    margin-bottom: 15px;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.head_style {
    display: inline-block;
    float: left;
    width:90px;
    height:90px;
    background: linear-gradient(-57.3792725189961deg, rgba(255, 255, 255, 1) 34%, rgba(251, 209, 231, 1) 100%);
}
.head_style img {position: relative;top:18px;left:20px;}
.title_left{
    float: left;
    padding-top:5px;
}
.title_left_p1{
    font-size: 18px;
    font-weight: bold;
    margin-left: 5px;
    margin-top: 20px;
}
.title_left_p2{
    font-size: 14px;
    color: #b4b2b2;
    margin-left: 5px;
    margin-top: 8px;
}
.title_right{
    float: right;
    margin-top: 25px;
    margin-right: 15px;
}
.title_right>button{
    margin: 0 10px 0 10px;
}
.content_titleLeft {
    font-size: 20px;
    font-weight: bold;
}
.content_titleRigth {
    // float: right;
    font-size: 18px;
    position: absolute;
    right: 55px;
}
.content_body {
    margin: 0 30px 0 30px;
}
.content_span1 {
    font-size: 16px;
}
.content_span2 {
    display: inline-block;
    width: 100px;
    line-height: 30px;
    background-color: rgb(148, 151, 151);
    border-radius: 3px;
    color: #fff;
    text-align: center;
    margin-right: 5px;
}
.content_span3 {
    display: inline-block;
    width: 100px;
    line-height: 30px;
    background-color: rgba(233, 233, 233, 0.795);
    border-radius: 3px;
    color: #333;
    text-align: center;
    margin-right: 5px;
}
.content_line {
    margin-bottom: 10px;
}
.content_select {
    margin-right: 5px;
}
.content_picker {
    height: 30px;
    // margin-top: 10px;
    vertical-align: top;
}
::v-deep .avue-crud .el-table th {
    font-size: 14px;}
::v-deep .avue-crud .el-table td {
    font-size: 12px;}
::v-deep .avue-crud{
    margin-top: 10px;
}
::v-deep .el-input__icon {
    line-height: 22px;
}
::v-deep .el-range-separator {
    line-height: 22px;
    width: 20px;
}
.content_input {
    display: inline-block;
    width: 200px;
    margin-right: 5px;
}
::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}
::v-deep  .cell-point{
    color: #255bc6;
    text-decoration: underline;
    cursor: pointer;
}
::v-deep .el-card__body .el-collapse-item__wrap{
    display: none !important;
}
.echarts {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.content_left{
    width: 20%;
    //height: 200px;
    background-color: #fff;
    padding: 30px 20px;
    //float: left;
}
.content_right{
    width: 79%;
    //height: 200px;
    padding: 40px 30px;
    background-color: #fff;
    //float: right;
}
</style>
