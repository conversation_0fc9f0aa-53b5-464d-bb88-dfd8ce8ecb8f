<template>
    <section>
        <div class="wrapAlls">
            <div class="boxs">
                <div class="content_title">
                    <div class="title_left">
                        <div class="title_left_p1">各省情况分析</div>
                    </div>
                    <div class="title_right">
                        <!--                            <span>统计规则：</span>-->
                        <el-button type="primary">查询</el-button>
                    </div>
                </div>
                <div class="content">
                    <el-collapse
                        :style="{position: 'relative'}"
                        v-model="activeNames">
                        <el-collapse-item name="1">
                            <template
                                slot="title"
                                :style="{margin: '0 30px','border-bottom': '1px solid rgb(235, 238, 245)'}"
                            >
                                <span class="content_titleLeft">预警信息列表</span>
                                <span class="content_titleRigth">查询条件</span>
                            </template>
                            <div class="content_line  top-line">
                            </div>
                            <div class="content_line">
                                <span class="content_span1">预警类型：</span>
                                <span v-for="(item,index) in list" :key="index" @click="actives(index,item)"
                                      :class="{content_span2:act==index}" class="content_span3">{{ item }}</span>
                            </div>
                            <!--              <div class="content_line">
                                            <span class="content_span1">预警规则：</span>
                                            <span v-for="(item,index) in this.listrule" :key="index" @click="activerule(index,item)"
                                                  :class="{content_span2:actrule==index}" class="content_span3">{{ item.setName }}</span>
                                          </div>-->
                            <div class="content_line">
                                <span class="content_span1">预警级别：</span>
                                <span v-for="(item,index) in listlevel" :key="index" @click="activelevel(index,item)"
                                      :class="{content_span2:actlevel==index}" class="content_span3">{{ item }}</span>
                            </div>
                            <div class="content_line">
                                <span class="content_span1">预警处理：</span>
                                <span v-for="(item,index) in listhandle" :key="index" @click="activehandle(index,item)"
                                      :class="{content_span2:acthandle==index}" class="content_span3">{{ item }}</span>
                            </div>
                            <div class="content_line">
                                <span class="content_span1">预警时间：</span>
                                <!--                  <span v-for="(item,index) in listTime" @click="activeTime(index)" :class="{content_span2:actTime==index}" class="content_span3">{{item}}</span>-->
                                <el-date-picker
                                    class="content_picker"
                                    v-model="value2"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :picker-options="pickerOptions"
                                    @change="changetime">
                                </el-date-picker>
                            </div>
                            <div class="content_line">
                                <span class="content_span1">组织机构：</span>
                                <el-select v-model="valueTitle" :clearable="clearable" @change="handleSearch" class="custom-select" filterable remote @clear="clearHandle">
                                    <el-option>
                                        <div  @click.stop>
                                            <el-input v-model="searchText" @input="handleSearch" placeholder="请输入搜索内容"></el-input>
                                        </div>
                                        <el-tree id="tree-option"
                                                 ref="tree"
                                                 :accordion="accordion"
                                                 :data="optionz"
                                                 :props="props"
                                                 :node-key="props.value"
                                                 :default-expanded-keys="defaultExpandedKey"
                                                 :filter-node-method="filterNode"
                                                 @node-click="handleNodeClick">
                                        </el-tree>
                                    </el-option>
                                </el-select>
                                <span class="content_span1">查询账号：</span>
                                <el-input
                                    class="content_input"
                                    v-model="inputs"
                                    placeholder="请输入账号"
                                ></el-input>
                                <span class="content_span1">处理人：</span>
                                <el-input
                                    class="content_input"
                                    v-model="operatorname"
                                    placeholder="请输入处理人"
                                ></el-input>
                                <el-button type="primary" @click="searchstudent">查询</el-button>
                                <el-button :style="{ float: 'right', marginRight: '10px' }" type="primary"
                                           @click="crudexport"
                                >导出
                                </el-button
                                >
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                    <div class="content_body">
                        <avue-crud
                            ref="crud"
                            :data="datalist"
                            :option="option"
                            :page.sync="page"
                            @selection-change="selectionChange"
                            @size-change="sizeChange"
                            @current-change="currentChange"
                        >
                            <template slot="warnLevel" slot-scope="scope">
                                <span :style="{'background-color': scope.row.warnLevelColor,color: scope.row.warnLevelColor ? '#fff' : '#333'}"
                                      style="display: inline-block;border-radius:5px;width: 50px;height: 25px;line-height:25px;margin-left: 5px;text-align: center;">{{ scope.row.warnLevel }}</span>
                            </template>
                            <template slot="index" slot-scope="{row,index}">
                                <p :style="{textAlign: 'center'}">{{index+1}}</p>
                            </template>
                            <template slot-scope="{row}" slot="menuLeft">
                                <el-button type="primary"
                                           icon="el-icon-plus"
                                           size="small"
                                           @click="listAdd(row)">添加到特殊名单
                                </el-button>
                            </template>
                            <template slot-scope="{row}" slot="menu">
                                <el-button type="text"
                                           size="small"
                                           @click="isShow(row)"
                                >查看
                                </el-button>
                                <el-button type="text"
                                           size="small"
                                           @click="deleteHandle(row)"
                                >删除
                                </el-button>
                                <el-button type="text"
                                           size="small"
                                           @click="infoHandle(row)"
                                           v-if="row.status =='待处理'"
                                >处理
                                </el-button>
                            </template>
                        </avue-crud>
                    </div>
                    <el-dialog :title="caseResultTitle" :visible.sync="dialogCaseResult"
                               class="avue-dialog avue-dialog--top"
                               width="50%">
                        <el-container>
                            <el-aside width="180px">
                                <el-descriptions title="" column="1">
                                    <el-descriptions-item label="姓名">{{ warnINfoobj.humanName }}</el-descriptions-item>
                                    <el-descriptions-item label="性别">{{ warnINfoobj.sex }}</el-descriptions-item>
                                    <el-descriptions-item label="学号">{{ warnINfoobj.humanCode }}</el-descriptions-item>
                                    <el-descriptions-item label="机构">{{ warnINfoobj.organizationNames }}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="电话">{{ warnINfoobj.telmobile1 }}</el-descriptions-item>
                                </el-descriptions>
                                <p class="portraits" @click="portrait(warnINfoobj.humanCode)">查看学生画像 ></p>
                            </el-aside>
                            <el-main>
                                <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                                    <el-tab-pane class="dialogright" label="预警信息" name="first">
                                        <el-descriptions title="" column="1">
                                            <el-descriptions-item label="预警时间">{{ warnINfoobj.warnTime }}
                                            </el-descriptions-item>
                                            <el-descriptions-item label="预警级别">{{ warnINfoobj.warnLevel }} <span
                                                :style="{'background-color': warnINfoobj.warnLevelColor}"
                                                style="display: inline-block;border-radius:5px;width: 10px;height: 10px;margin-left: 5px"></span>
                                            </el-descriptions-item>
                                            <el-descriptions-item label="预警名称">{{ warnINfoobj.warnName }}
                                            </el-descriptions-item>
                                            <el-descriptions-item label="所属类型">{{ warnINfoobj.warnType }}
                                            </el-descriptions-item>
                                            <!--                      <el-descriptions-item label="数据范围">{{ warnINfoobj.telmobile1 }}</el-descriptions-item>-->
                                            <el-descriptions-item label="监控数值">{{ warnINfoobj.warnValue }}
                                            </el-descriptions-item>
                                            <el-descriptions-item label="预警原因">{{ warnINfoobj.warnReason }}
                                            </el-descriptions-item>
                                            <el-descriptions-item label="处理状态">{{ warnINfoobj.status }}
                                            </el-descriptions-item>
                                            <el-descriptions-item label="处理人">{{ warnINfoobj.operatorname }}
                                            </el-descriptions-item>
                                            <el-descriptions-item label="处理意见">{{ warnINfoobj.operatorcomment }}
                                            </el-descriptions-item>

                                        </el-descriptions>
                                    </el-tab-pane>
                                    <el-tab-pane class="dialogright" label="历史预警" name="second">
                                        <avue-crud ref="crud"
                                                   :data="datalistlsyj"
                                                   :option="optionlsyj">
                                            <template slot="index" slot-scope="{row,index}">
                                                <p :style="{textAlign: 'center'}">{{index+1}}</p>
                                            </template>
                                        </avue-crud>
                                    </el-tab-pane>
                                </el-tabs>
                            </el-main>
                        </el-container>
                        <div class="avue-dialog__footer">
                            <el-button @click="dialogCaseResult = false">取 消</el-button>
                            <el-button @click="dialogCaseResult = false" type="primary">确 定</el-button>
                        </div>
                    </el-dialog>
                    <el-dialog :title="`预警处理`" :visible.sync="handleShow" class="avue-dialog avue-dialog--top"
                               width="50%">
                        <avue-form ref="handleform" v-model="handleform" :option="handleoption">
                        </avue-form>
                        <div class="avue-dialog__footer">
                            <el-button @click="handleShow = false">取 消</el-button>
                            <el-button @click="sethandle()" type="primary">确 定</el-button>
                        </div>
                    </el-dialog>
                </div>
                <footer class="foot">
          <span>
          {{ dbxx }}
        </span>
                </footer>
            </div>
            <!--      <footer class="foot">-->
            <!--        <span>-->
            <!--          {{ dbxx }}-->
            <!--        </span>-->
            <!--      </footer>-->
        </div>
    </section>
</template>

<script>
import {GenExportData, warnInflist, warnInfodelete, warnInfoget, warnInfoqueryPage} from "@/api/dataWarningWarnInfo";
import {GetOrgJsonArray} from "@/api/basetable";
import {mapGetters} from "vuex";
import {GetSysParamNoLogin} from "@/api/sysParam";
import {warnHandlesave} from "@/api/dealrecord"
import {warnspecialListAdds} from "@/api/specialList"
import {ListDataWarningRuleList} from "@/api/dataWarningRule";
import {selectDictList} from "@/api/sysDict";
import {warninggetLevelData} from "@/api/dataWarningLevel";

export default {
    name: "GitSanythReportUiDataWarning",
    components: {
    },
    props: {
        props: {
            type: Object,
            default: () => {
                return {
                    // value:'id',             // ID字段名
                    label: 'label',         // 显示名称
                    children: 'children'    // 子级字段名
                }
            }
        },
        /* 初始值 */
        value: {
            type: Number,
            default: () => {
                return null
            }
        },
        /* 可清空选项 */
        clearable: {
            type: Boolean,
            default: () => {
                return true
            }
        },
        /* 自动收起 */
        accordion: {
            type: Boolean,
            default: () => {
                return false
            }
        },
    },
    data() {
        return {
            handleShow: false,
            searchText: null,
            handleform: {},
            listhumancode: [],
            activeNames: ['1'],
            valueId: this.value,    // 初始值
            valueTitle: '',
            defaultExpandedKey: [],
            datalistlsyj: [],
            optionz: [],
            dbxx: ``,
            activeName1: '1',
            value1: '',
            value2: '',
            warnINfoobj: {},
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            value6: '',
            queryParam: {},
            inputs: '',
            operatorname: '',
            act: `0`,
            actrule: `0`,
            actlevel: `0`,
            acthandle: `0`,
            actTime: `0`,
            list: [
                // '全部',
                // '学业预警',
                // '考勤预警',
                // '消费预警',
                // '安全预警',
            ],
            listlevel: ['全部',],
            listrule: [],
            listhandle: [],
            listhandleoption: [],
            // listhandle: ['全部', '已处理', '已知晓','已联系学生本人','待处理','忽略处理'],
            listTime: [
                '全部',
                '近一天',
                '近一周',
                '近一月',
            ],
            form: {
                name: '',
                region: '',
                date1: '',
                date2: '',
                delivery: false,
                type: [],
                resource: '',
                desc: ''
            },
            activeName: 'first',
            dialogCaseResult: false,
            dialogHandle: false,
            caseResultTitle: ``,
            caseResultContent: null,
            page: {
                currentPage: 1,
                total: 5,
                pageSize: 7,
                pageSizes: [7, 10, 15]
            },
            datalist: [],
            option: {
                indexTitle: '序号',
                menuAlign: "center",
                sizeValue: 'mini',
                align: 'left',
                indexWidth: 30,
                // menuWidth: 180,
                selection: true,
                tip: false,
                stripe: true,
                delBtn: false,
                header: true,
                editBtn: false,
                addBtn: false,
                searchBtn: false,
                emptyBtn: false,
                refreshBtn: false,
                searchShowBtn: false,
                columnBtn: false,
                column: [
                    {
                        label: '序号',
                        prop: 'index',
                        width: 50,
                        fixed: true
                    },
                    {
                        label: "执行记录id",
                        prop: "taskRecordId",
                        overHidden: true,
                        hide: true
                    },
                    {
                        label: "预警规则id",
                        prop: "ruleId",
                        overHidden: true,
                        hide: true
                    },
                    {
                        label: "登录账号",
                        prop: "humanCode",
                        overHidden: true,
                    },
                    {
                        label: "人员名称",
                        prop: "humanName",
                        overHidden: true,
                    },
                    {
                        label: "所属机构",
                        prop: "organizationNames",
                        overHidden: true,
                    },
                    {
                        label: "预警类型",
                        prop: "warnType",
                        overHidden: true,
                    },
                    {
                        label: "预警名称",
                        prop: "warnName",
                        overHidden: true,
                    },
                    {
                        label: "预警原因",
                        prop: "warnReason",
                        overHidden: true,
                        row: true,
                    },
                    {
                        label: "预警时间",
                        prop: "warnTime",
                        overHidden: true,
                        row: true,
                    },
                    {
                        label: "预警级别",
                        prop: "warnLevel",
                        slot: true,
                        overHidden: true,
                    },
                    {
                        label: "处理状态",
                        prop: "status",
                        disabled: true,
                        span: 24,
                        dicData: this.listhandleoption,
                        overHidden: true,
                    },
                    {
                        label: "处理人",
                        prop: "operatorname",
                        overHidden: true,
                    },
                    {
                        label: "处理意见",
                        prop: "operatorcomment",
                        overHidden: true,
                    },
                ],
            },
            optionlsyj: {
                menu: false,
                menuAlign: "center",
                sizeValue: 'mini',
                align: 'left',
                indexWidth: 30,
                selection: false,
                tip: false,
                stripe: true,
                delBtn: false,
                editBtn: false,
                header: false,
                searchBtn: false,
                emptyBtn: false,
                overHidden: true,
                column: [
                    {
                        label: '序号',
                        prop: 'index',
                        width: 50,
                        fixed: true
                    },
                    {
                        label: "预警类型",
                        prop: "warnType",
                        overHidden: true,
                    },
                    {
                        label: "预警名称",
                        prop: "warnName",
                        overHidden: true,
                    },
                    {
                        label: "预警原因",
                        prop: "warnReason",
                        overHidden: true,
                        row: true,
                    },
                    {
                        label: "预警时间",
                        prop: "warnTime",
                        overHidden: true,
                        row: true,
                    },
                    {
                        label: "预警级别",
                        prop: "warnLevel",
                        overHidden: true,
                    },
                    {
                        label: "处理状态",
                        prop: "status",
                        disabled: true,
                        span: 24,
                        dicData: this.listhandleoption,
                        overHidden: true,
                    },
                ],
            },
        };
    },
    computed: {
        ...mapGetters(["website", "keyCollapse"]),
        handleoption() {
            return {
                submitBtn: false,
                emptyBtn: false,
                column: [
                    {
                        label: "处理状态",
                        prop: "status",
                        type: "radio",
                        // disabled:true,
                        span: 24,
                        dicData: this.listhandleoption,
                        overHidden: true,
                    },
                    {
                        label: "处理意见",
                        type: "textarea",
                        span: 24,
                        prop: "operatorcomment",
                        overHidden: true,
                    },
                ]
            }
        }
    },
    created() {
        this.getParamDesc()
        this.initHandle()
        this.getList(this.pageParam(this.queryParam));
        this.onLoad(this.pageParam(this.queryParam));
        this.footP(this.footParam());
        this.getWarningRuleList();
        this.getWarninggetLevelData();
    },
    mounted() {
        // this.initHandle()
    },
    watch: {
        value() {
            this.valueId = this.value
            this.initHandle()
        },
    },
    methods: {
        handleSearch(value){
            this.$refs.tree.filter(value);
        },
        getParamDesc() {
            selectDictList({code: "WARNING_TYPE"}).then(res => {
                let data = res.data.info
                data.forEach(item => {
                    this.list.push(item.value)
                })
            });
            selectDictList({code: "WARNING_HANDLE"}).then(res => {
                let data = res.data.info
                data.forEach(item => {
                    this.listhandle.push(item.value)
                    this.listhandleoption.push({
                        label: item.value,
                        value: item.value
                    })
                })
            });
        },
        // 给第2列的单元格单独设置样式（从0开始）
        cellStyle({row, column, rowIndex, columnIndex}) {
            console.log(`row`, row)
            console.log(`column`, column, rowIndex, columnIndex)
            if (column.property == 'warnLevel') {
                return {
                    backgroundColor: 'red',
                };
            }

        },
        // 初始化值
        initHandle() {
            document.title = '预警信息';
            if (this.valueId) {
                this.valueTitle = this.$refs.selectTree.getNode(this.valueId).data[this.props.label]     // 初始化显示
                this.$refs.selectTree.setCurrentKey(this.valueId)       // 设置默认选中
                this.defaultExpandedKey = [this.valueId]      // 设置默认展开
            }
            // this.$nextTick(() => {
            //     let scrollWrap = document.querySelectorAll('.el-scrollbar .el-select-dropdown__wrap')[0]
            //     let scrollBar = document.querySelectorAll('.el-scrollbar .el-scrollbar__bar')
            //     scrollWrap.style.cssText = 'margin: 0px; max-height: none; overflow: hidden;'
            //     scrollBar.forEach(ele => ele.style.width = 0)
            // })

        },
        // 切换选项
        handleNodeClick(node) {
            this.valueTitle = node[this.props.label]
            this.valueId = node[this.props.value]
            this.$emit('getValue', this.valueId)
            this.defaultExpandedKey = []
            this.queryParam.orgid = node.id
            // this.pageParam(this.queryParam)
            // this.getList(this.pageParam(this.queryParam))
        },
        filterNode(value, data) {
            console.log(value,data)
            if (!value) {
                return true;
            }
            return data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1;
        },
        // 清除选中
        clearHandle() {
            this.queryParam.orgid = '';
            this.pageParam(this.queryParam)
            this.getList(this.pageParam(this.queryParam))
            this.valueTitle = ''
            this.valueId = null
            this.defaultExpandedKey = []
            this.clearSelected()
            this.$emit('getValue', null)
        },
        /* 清空选中样式 */
        clearSelected() {
            let allNode = document.querySelectorAll('#tree-option .el-tree-node')
            allNode.forEach((element) => element.classList.remove('is-current'))
        },
        footP(param) {
            GetSysParamNoLogin(param).then((res) => {
                this.dbxx = res.data.info.value;
            });
        },
        footParam() {
            return {
                idOrNameOrType: "dbxx",
            };
        },
        searchstudent() {
            this.queryParam.humanCode = this.inputs
            this.queryParam.operatorname = this.operatorname
            this.pageParam(this.queryParam)
            this.getList(this.pageParam(this.queryParam))
        },
        actives(index, item) {
            this.act = index;
            if (item == '全部') {
                this.queryParam.warnType = ''
            } else {
                this.queryParam.warnType = item
            }
            this.pageParam(this.queryParam)
            this.getList(this.pageParam(this.queryParam))
            this.getWarningRuleList(item);
        },
        activerule(index, item) {
            this.actrule = index;
            if (item == '全部') {
                this.queryParam.ruleId = ''
            } else {
                this.queryParam.ruleId = item.id
            }
            this.pageParam(this.queryParam)
            this.getList(this.pageParam(this.queryParam))
        },
        activelevel(index, item) {
            this.actlevel = index;
            if (item == '全部') {
                this.queryParam.warnLevel = ''
            } else {
                this.queryParam.warnLevel = item
            }
            this.pageParam(this.queryParam)
            this.getList(this.pageParam(this.queryParam))
        },
        activehandle(index, item) {
            this.acthandle = index;
            if (item == '全部') {
                this.queryParam.status = ''
            } else {
                this.queryParam.status = item
            }
            this.pageParam(this.queryParam)
            this.getList(this.pageParam(this.queryParam))
        },
        changetime() {
            if (this.value2 !== null) {
                let a = new Date(this.value2[0]);
                let b = new Date(this.value2[1]);
                let start = a.getFullYear() + '-' + (a.getMonth() + 1) + '-' + a.getDate();
                let end = b.getFullYear() + '-' + (b.getMonth() + 1) + '-' + b.getDate();
                this.queryParam.startTime = start;
                this.queryParam.endTime = end;
                this.pageParam(this.queryParam)
                this.getList(this.pageParam(this.queryParam))
            } else {
                this.queryParam.startTime = '';
                this.queryParam.endTime = '';
                this.pageParam(this.queryParam)
                this.getList(this.pageParam(this.queryParam))
            }

        },
        onLoad() {
            GetOrgJsonArray().then(res => {
                this.optionz = res.data;
            })
        },
        getList(param) {
            this.tableLoading = true
            warnInfoqueryPage(param).then(res => {
                this.tableLoading = false
                const data = res.data.info;
                this.page.currentPage = data.current;
                this.page.total = data.total;
                this.page.pageSize = data.size;
                for (let i = 0; i < data.records.length; i++) {
                    data.records[i].warnTime = data.records[i].warnTime.replace("T", "-")
                    data.records[i].warnTime = data.records[i].warnTime.replace("+0000", "")
                }
                this.datalist = data.records;
            })
        },
        getWarningRuleList(ruleType) {
            ListDataWarningRuleList({ruleType: ruleType}).then(res => {
                let list = res.data;
                let arr = [{"setName": "全部"}];
                for (let i = 0; i < list.length; i++) {
                    arr.push(list[i])
                }
                this.listrule = arr;
            })
        },
        getWarninggetLevelData() {
            warninggetLevelData().then(res => {
                let list = res.data.info;
                for (let i = 0; i < list.length; i++) {
                    this.listlevel.push(list[i].LEVELNAME)
                }
            })
        },
        //处理
        infoHandle(row) {
            this.handleShow = true;
            this.handleform = row;
        },
        sethandle() {
            this.handleShow = false;
            let param = {
                infoid: this.handleform.id,
                status: this.handleform.status,
                operatorcomment: this.handleform.operatorcomment
            }
            warnHandlesave(param).then(res => {
                if (res.data.code === '00000') {
                    this.$message({type: "success", message: "处理成功"});
                    this.getList(this.pageParam(this.queryParam))
                }
            })
        },
        //删除
        deleteHandle(row) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                warnInfodelete({"id": row.id}).then(res => {
                    if (res.data.code === '00000') {
                        this.$message({type: "success", message: "删除成功"});
                        this.getList(this.pageParam())
                    }
                })
            }).catch(() => {
                console.log('已取消删除操作')
            });
        },
        //导出
        crudexport() {
            // this.$confirm("此操作将导出列表, 是否继续?", "提示", {
            //   confirmButtonText: "确定",
            //   cancelButtonText: "取消",
            //   type: ""
            // }).then(() => {
            // warnInfoexport({"param": this.queryParam}).then(res => {
            //   console.log(`res`,res)
            //   window.open(`/dataWarning/warnInfo/export`);
            // })
            // }).catch(() => {
            //   console.log('已取消删除操作')
            // });
            GenExportData(this.pageParam(this.queryParam)).then((res) => {
                if (res.data.code == "00000") {
                    window.open(`/dataWarning/warnInfo/export`);
                }
            });
        },
        sizeChange(val) {
            this.page.currentPage = 1;
            this.page.pageSize = val;
            this.getList(this.pageParam(this.queryParam));
        },
        currentChange(val) {
            this.page.currentPage = val;
            this.getList(this.pageParam(this.queryParam));
        },
        pageParam(queryParam) {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize,
                queryParam: queryParam
            }
        },
        selectionChange(list) {
            this.listhumancode = list;
        },
        listAdd(row) {
            console.log(row)
            if (this.listhumancode.length > 0) {
                this.$confirm("确定添加到特殊名单, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    let list = {
                        warnInfoJson: JSON.stringify(this.listhumancode)
                    };
                    warnspecialListAdds(list).then(res => {
                        if (res.data.code === '00000') {
                            this.$message({type: "success", message: "新增成功"});
                        }
                    });
                }).catch(() => {
                    console.log('已取消操作')
                });


            } else {
                this.$message({type: "warning", message: "请选择数据"});
            }

        },
        //查看
        isShow(row) {
            this.dialogCaseResult = true
            // this.caseResultTitle = item.setName
            this.caseResultTitle = `学生预警详情`
            warnInfoget({id: row.id}).then((res) => {
                if (res.data.info.sex == "male") {
                    res.data.info.sex = "男"
                }
                if (res.data.info.sex == "female") {
                    res.data.info.sex = "女"
                }
                this.warnINfoobj = res.data.info;
            })
            warnInflist({humanCode: row.humanCode}).then(res => {
                this.datalistlsyj = res.data.info;
            })
        },
        isHandle() {
            this.dialogHandle = true;
            this.caseResultTitle = `处理设置`;
        },
        handleClick() {
        },
        portrait(val) {
            GetSysParamNoLogin({idOrNameOrType: "reportId"}).then(res => {
                let param = {
                    id: res.data.info.value,
                    humancode: val
                }
                let {href} = this.$router.resolve({
                    name: '移动画像',
                    query: {param: JSON.stringify(param)}
                })
                window.open(href, '_blank')
            })
        }
    },
};
</script>
<style scoped>
@import url(../../styles/assets/css/reset.css);
@import url(../../styles/assets/css/head.css);
@import url(../../styles/assets/css/index.css);
</style>
<style lang="scss" scoped>
//::v-deep .avue-crud__menu {
//  display: none;
//}
//::v-deep .avue-crud__card {
//  margin: 0 10px;
//}
.wrapAlls {
    // height: calc(100% - 60px);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    //   bottom: -60px;
    // height: 100vh;
    background: #F0F2F5;
    height: calc(100vh - 60px);

    overflow: hidden;
}

.boxs {
    height: calc(100vh - 60px);
    overflow-y: auto;
}

.foot {
    background: rgba(240, 239, 239, 0.788);
    font-size: 12px;
    line-height: 30px;
    color: rgb(147, 150, 149);
    text-align: center;
}

.nav {
    height: 50px;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #E9E9E9;
}

.nav_h1 {
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    margin-left: 50px;
}

.nav_h2 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 50px;
    margin-right: 15px;
    color: rgb(147, 150, 149);
}

.nav_h3 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
    color: rgb(147, 150, 149);
}

.nav_h4 {
    line-height: 50px;
    font-size: 14px;
    margin-left: 15px;
    margin-right: 15px;
}

.nav_h5 {
    font-size: 14px;
    color: rgb(147, 150, 149);
}

.content {
    width: 100%;
    // height: 100%;
    min-height: calc(100vh - 140px);
    padding: 5px 15px 0;
    background: rgba(240, 239, 239, 0.7);
}

.wrapAll {
    padding: 0 !important;
}

//.content_title {
//    height: 60px;
//    // border-bottom: 1px solid rgb(228, 225, 225);
//    border-radius: 5px;
//    margin: 0 30px 0 30px;
//    line-height: 60px;
//    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
//}

.content_titleLeft {
    font-size: 20px;
    font-weight: bold;
    position: absolute;
    left: 15px;
}

.content_titleRigth {
    // float: right;
    font-size: 14px;
    position: absolute;
    right: 25px;
}

.content_body {
    padding: 10px 10px 0 10px;
    background: #fff;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.content_span1 {
    font-size: 14px;
    margin-left: 15px;
}

.content_span2 {
    display: inline-block;
    min-width: 90px;
    padding: 0 20px 0 20px;
    line-height: 30px;
    background-color: #1890FF !important;
    border-radius: 2px;
    color: #fff !important;
    text-align: center;
    margin-right: 5px;
}

.content_span3 {
    font-size: 12px;
    display: inline-block;
    min-width: 90px;
    padding: 0 20px 0 20px;
    line-height: 30px;
    background-color: #fff;
    border-radius: 2px;
    color: #333;
    text-align: center;
    margin-right: 5px;
    cursor: pointer;
}

.content_span3:hover {

    background-color: #D7D7D7;
    color: #fff;
}

.content_line {
    margin-bottom: 10px;
}

.top-line {
    margin: 0 15px 10px 15px;
    border-bottom: 1px solid rgb(235, 238, 245);
}

.content_select {
    width: 185px;
    margin-right: 5px;
}

.content_picker {
    height: 30px;
    // margin-top: 10px;
    vertical-align: top;
}

::v-deep .avue-crud .el-table th {
    font-size: 14px;
}

::v-deep .avue-crud .el-table td {
    font-size: 12px;
}

::v-deep .el-input__icon {
    line-height: 22px;
}

::v-deep .el-range-separator {
    line-height: 22px;
    width: 20px;
}

.content_input {
    display: inline-block;
    width: 185px;
    margin-right: 5px;
}

.el-collapse {
}

::v-deep .el-collapse-item__content {
    padding-bottom: 0;
}

::v-deep .el-collapse-item__header {
    //position: relative;
    height: 60px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.imgs {
    width: 100px;
    height: 100px;
    margin-bottom: 10px;
    background-color: #eeeeee;
}

::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item {
    border-left: none;
}

::v-deep .el-tabs__nav-scroll {
    height: 40px;
    margin-left: 30px;
}

::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    border-top: 1px solid #E4E7ED;
    border-right: 1px solid #E4E7ED;
    border-left: 1px solid #E4E7ED;
    border-radius: 4px;
    font-size: 16px;
}

::v-deep .el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: none;
}

::v-deep .el-aside {
    overflow: hidden;
    font-size: 10px;
    // border-right: 1px solid #E4E7ED;
}

::v-deep .el-descriptions .el-descriptions-item__cell {
    padding-bottom: 5px;
}

.dialobottom {
    // margin-left: 100px;
    position: absolute;
    left: 100px;
    bottom: 20px;
}

.dialogright {
    margin-left: 50px;
}

::v-deep .el-collapse-item__wrap {
    padding-bottom: 20px;
}

::v-deep .el-dialog {
    // height: 600px;

    max-height: 70vh;

    overflow: hidden;
}

::v-deep .el-main {
    overflow: hidden;
    border-left: 1px solid #E4E7ED;
    position: relative;
    //min-height: 390px;
}

::v-deep .el-dialog__body {
    padding-top: 20px;
    max-height: 65vh;
}

.dialog-footer {
    text-align: center;
}

::v-deep .el-card__body .el-collapse-item__wrap {
    display: none !important;
}

::v-deep .el-dialog {
    max-height: 70vh;
    overflow: hidden;
}

::v-deep .el-dialog .el-dialog__header {
    background-color: #ffffff;
    color: #333333;
}

::v-deep .el-dialog .el-dialog__header .el-dialog__title {
    color: #333333;
}

::v-deep .el-dialog .el-dialog__header .el-dialog__headerbtn i {
    color: #666;
}

::v-deep .el-descriptions-item__label {
    white-space: nowrap
}

.dialogsecondtop {
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #E4E7ED;
}

::v-deep .el-dialog {
    min-height: 200px;
}

.portraits {
    font-size: 14px;
    color: #00a5ec;
    cursor: pointer;
}
.custom-select .el-select-dropdown {
    max-height: 300px; /* 设置最大高度 */
    overflow-y: auto; /* 添加垂直滚动条 */
}
.content_title {
    height: 80px;
    margin: 15px;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 0.3);
}
.head_style img {position: relative;top:18px;left:20px;}
.title_left{
    float: left;
    height: 80px;
}
.title_left_p1{
    font-size: 18px;
    font-weight: bold;
    margin-left: 35px;
    line-height: 75px;
}
.title_left_p2{
    font-size: 14px;
    color: #b4b2b2;
    margin-left: 5px;
    margin-top: 8px;
}
.title_right{
    float: right;
    margin-top: 25px;
    margin-right: 15px;
}
.title_right>button{
    margin: 0 10px 0 10px;
}
</style>
