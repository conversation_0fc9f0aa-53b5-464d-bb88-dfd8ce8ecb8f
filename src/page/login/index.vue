<template>
  <div class="login-container" @keyup.enter.native="handleLogin">
    <top-color v-show="false"></top-color>
    <div class="loginTop">
      <img :src="schoolLogo" alt/>
      <span class="loginToptitle">{{ title }}</span>
    </div>

    <div class="loginCenter">
      <img class="img1" :src="loginBackground1" alt/>
      <!-- <img class="img2" src="../../styles/image/script.png" alt=""> -->
    </div>
    <div class="login-weaper animated bounceInDown" v-if="!show">
      <div class="login-border">
        <div class="login-main">
          <el-tabs v-model="activeName">
            <el-tab-pane label="账号登录" name="first">
              <userLogin @func="getMsgFormSon"></userLogin>
            </el-tab-pane>
            <el-tab-pane label="扫码登录" name="second">
              <codeLogin></codeLogin>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="show" width="700px" height="450px">
      <el-row class="contentWrap">
        <el-col :span="8" class="dialogLeft">
          <i class="el-icon-lock"></i>
          <p>找回密码</p>
        </el-col>
        <el-col :span="16" class="dialogRight">
          <el-steps :active="active" simple>
            <el-step title="账号信息"></el-step>
            <el-step title="重置密码"></el-step>
            <el-step title="完成"></el-step>
          </el-steps>

          <div class="findContent" v-if="active == 1">
            <el-form label-position="top"
                     style="margin-top:40px"
                     label-width="80px"
                     :model="formLabelAlign">
              <el-form-item>
                <el-input placeholder="请输入学号或工号" v-model="formLabelAlign.name"></el-input>
              </el-form-item>
              <el-form-item>
                <el-input placeholder="请输入姓名" v-model="formLabelAlign.name"></el-input>
              </el-form-item>
            </el-form>
          </div>

          <div class="findContent" v-if="active == 2">
            <el-form label-position="top" label-width="80px" :model="formLabelAlign">
              <el-form-item label="验证码已发送到邮箱">
                <div style="display: flex;">
                  <el-input placeholder="请输入验证码" v-model="formLabelAlign.name"></el-input>
                  <el-button style="margin-left:10px">重新发送</el-button>
                </div>
              </el-form-item>
              <el-form-item>
                <div style="display: flex;">
                  <el-input placeholder="请输入新密码" v-model="formLabelAlign.name"></el-input>
                </div>
              </el-form-item>
              <el-form-item>
                <div style="display: flex;">
                  <el-input placeholder="请再次输入新密码" v-model="formLabelAlign.name"></el-input>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <div class="findContent findContentSuccess" v-if="active == 3">
            <i class="el-icon-success"></i>
            <p>重置成功</p>
          </div>
        </el-col>
        <div class="buttonWrap">
          <el-button type="primary" class="nextButton" @click="next">下一步</el-button>
          <el-button class="nextButton" @click="prev">上一步</el-button>
        </div>
      </el-row>
    </el-dialog>

    <div class="footer">
      <p>{{dbxx}}</p>
    </div>
  </div>
</template>
<script>
import userLogin from "./userlogin";
import codeLogin from "./codelogin";
import {mapGetters} from "vuex";
import {dateFormat} from "@/util/date";
import {validatenull} from "@/util/validate";
import topLang from "@/page/index/top/top-lang";
import topColor from "@/page/index/top/top-color";
import Logo from "@/page/index/logo";
import {GetSysParamListNoLogin} from "@/api/sysParam";

export default {
  name: "login",
  components: {
    Logo,
    userLogin,
    codeLogin,
    topLang,
    topColor
  },
  data() {
    return {
      time: "",
      activeName: "first",
      dialogVisible: false,
      formLabelAlign: {
        name: "",
        region: "",
        type: ""
      },
      show: false,
      active: 1,
      schoolLogo: '',
      loginBackground1: '../../styles/image/picture.png',
      loginBackground2: '',
      title: '招生系统',
      dbxx: '',

    };
  },
  watch: {
    $route() {
      const params = this.$route.query;
      this.socialForm.state = params.state;
      this.socialForm.code = params.code;
      if (!validatenull(this.socialForm.state)) {
        const loading = this.$loading({
          lock: true,
          text: `${
              this.socialForm.state === "WX" ? "微信" : "QQ"
          }登录中,请稍后。。。`,
          spinner: "el-icon-loading"
        });
        setTimeout(() => {
          loading.close();
        }, 2000);
      }
    }
  },
  created() {
    // this.getTime();
    // setInterval(() => {
    //     this.getTime();
    // }, 1000);
    this.onLoad(this.pageParam());
  },
  mounted() {
  },
  computed: {
    ...mapGetters(["website", "keyCollapse"])
  },
  props: [],
  methods: {
    onLoad(param) {
      GetSysParamListNoLogin(param).then(res => {
        const data = res.data.info;
        data.forEach(item => {
          switch (item.type) {
            case 'loginLogo':
              this.schoolLogo = item.img !== '' ? JSON.parse(item.img)[0].url : '';
              break;
            case 'loginBackground1':
              this.loginBackground1 = item.img !== '' ? JSON.parse(item.img)[0].url : '';
              break;
            case 'loginBackground2':
              this.loginBackground2 = item.img !== '' ? JSON.parse(item.img)[0].url : '';
              break;
            case 'dbxx':
              this.dbxx = item.value;
              break;
            default:
              break;
          }
        })
      })
    },
    pageParam() {
      return {
        queryParam: {
          type: "loginLogo,loginBackground1,loginBackground2,dbxx"
        }
      }
    },
    getTime() {
      this.time = dateFormat(new Date());
    },
    getMsgFormSon(data) {
      this.number = data;
      if (this.number == 1) {
        this.show = true;
      }
    },
    next() {
      if (this.active++ > 2) this.active = 1;
    },
    prev() {
      if (this.active-- < 2) this.active = 3;
    }
  }
};
</script>

<style lang="scss">
@import "@/styles/login.scss";
</style>
<style scoped>
.login-container >>> .el-tabs__nav-scroll {
  padding-left: 70px !important;
}

.login-container >>> .el-tabs__item {
  font-size: 16px;
}

.loginTop img {
  height: 60px;
  margin-top: calc(7.5vh - 30px);
}

.loginToptitle {
  line-height: 15vh;
}

.loginTop {
  height: 15vh;
  padding-top: 0;
}
</style>

