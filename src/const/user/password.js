const RE_PASS = new RegExp(/^(?=.*[0-9].*)((?=.*[A-Z].*)|(?=.*[a-z].*)).{10,16}$/);
var validatePass = (rule, value, callback) => {
    if (value !== null && value !== '' && !RE_PASS.test(value)) {
        callback(new Error('密码强度不符,请使用包含数字大小写字母或特殊字符,10到16位作为密码'));
    } else {
        callback();
    }
}
export default {
    emptyBtn: false,
    submitBtn: false,
    column: [{
        label: '原密码',
        span: 12,
        row: true,
        type: 'password',
        prop: 'oldpassword',
        rules: [{required: true, message: "原密码不能为空"}],
    }, {
        label: '新密码',
        span: 12,
        row: true,
        type: 'password',
        prop: 'newpassword',
        placeholder: '请输入新密码，请使用包含数字大小写字母或特殊字符,10到16位作为密码',
        rules: [{required: true, message: "新密码不能为空"}],
    }, {
        label: '确认密码',
        span: 12,
        row: true,
        type: 'password',
        prop: 'newpasswords',
        placeholder: '请输入确认密码，请使用包含数字大小写字母或特殊字符,10到16位作为密码',
        rules: [{required: true, message: "确认密码不能为空"}],
    }],
}
