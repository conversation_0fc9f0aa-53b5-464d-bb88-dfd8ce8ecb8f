export default {

    emptyBtn: false,
    submitBtn: false,
    column: [
        // {
        //     label: '头像',
        //     type: 'upload',
        //     listType: 'picture-img',
        //     propsHttp: {
        //         res: 'data.0'
        //     },
        //     canvasOption: {
        //         text: 'avue',
        //         ratio: 0.1
        //     },
        //     action: 'https://avueupload.91eic.com/upload/list',
        //     tip: '只能上传jpg/png用户头像，且不超过500kb',
        //     span: 24,
        //     prop: 'img'
        // },
        {
            label: "学号/工号",
            prop: "humancode",
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            rules: [{required: true, message: "学号或工号不能为空"}],
            span: 12,
            row: true,
        },
        {
            label: "姓名",
            prop: "humanname",
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            rules: [{required: true, message: "姓名不能为空"}],
            span: 12,
            row: true,
        },
        {
            label: "性别",
            prop: "sex",
            type: "select",
            width: 60,
            align: "center",
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            dicData: [
                {label: "男", value: "male"},
                {label: "女", value: "female"},
                {label: "未知", value: ""},
            ],
            span: 12,
            row: true,
        },
        // {
        //   label: "用户类型",
        //   prop: "rolename",
        //   addDisplay: false,
        //   editDisplay: false,
        //   slot: true,
        //   align: "left",
        //   span: 8,
        // },
        {
            label: "所属机构",
            prop: "organizationnames",
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            slot: true,
            align: "left",
            span: 12,
            row: true,
        },
        {
            label: "角色",
            prop: "roleNames",
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            slot: true,
            align: "left",
            span: 12,
            row: true,
        },
        {
            label: "手机号码",
            prop: "telmobile1",
            addDisplay: false,
            editDisplay: false,
            span: 12,
            row: true,
        },
        // {
        //   label: "角色设置",
        //   prop: "role",
        //   type: "select",
        //   span: 12,
        //   placeholder: "请选择 角色设置-可多选",
        //   drag: true,
        //   multiple: true,
        //   dicData: [
        //     { label: "管理员", value: "1" },
        //     { label: "教师", value: "2" },
        //     { label: "学生", value: "3" },
        //   ],
        //   rules: [
        //     {
        //       required: true,
        //       message: "角色设置不能为空",
        //     },
        //   ],
        //   props: {
        //     label: "rolename",
        //     value: "id",
        //   },
        //   span: 8,
        // },
        // {
        //   label: "组织机构",
        //   prop: "organization",
        //   type: "tree",
        //   multiple: true,
        //   dicData: [
        //     { label: "管理员", value: "1" },
        //     { label: "教师", value: "2" },
        //     { label: "学生", value: "3" },
        //   ],
        //   props: {
        //     value: "id",
        //   },
        //   span: 8,
        //   rules: [{ required: true, message: "组织机构不能为空" }],
        // },
        // {
        //   label: "所属职务",
        //   prop: "dutyid",
        //   span: 8,
        // },
        // {
        //   label: "出生日期",
        //   prop: "birthday",
        //   type: "date",
        //   format: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   span: 8,
        // },
        // {
        //   label: "微信号",
        //   prop: "telmobile2",
        //   span: 8,
        // },
        {
            label: "电子邮箱",
            prop: "email",
            span: 12,
            row: true,
        },
        // {
        //   label: "人员状态",
        //   prop: "validflag",
        //   type: "select",
        //   dicData: [
        //     { value: 0, label: "在校" },
        //     { value: 1, label: "离校" },
        //   ],
        //   span: 8,
        // },
        {
            label: "证件类型",
            prop: "idtype",
            type: "select",
            dicData: [
                {value: "居民身份证", label: "居民身份证"},
                {value: "士兵证", label: "士兵证"},
                {value: "军官证", label: "军官证"},
                {value: "警官证", label: "警官证"},
                {value: "护照", label: "护照"},
                {value: "其他", label: "其他"},
            ],
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            span: 12,
            row: true,
        },
        {
            label: "证件号码",
            prop: "idcode",
            span: 12,
            addDisplay: false,
            editDisplay: false,
            disabled: true,
            row: true,
        },
        // {
        //   label: "办公电话",
        //   prop: "teloffice",
        //   span: 8,
        // },
        // {
        //   label: "家庭电话",
        //   prop: "telhome",
        //   span: 8,
        // },
        // {
        //   label: "邮政编码",
        //   prop: "postalcode",
        //   span: 8,
        // },
        // {
        //     label: "联系地址",
        //     prop: "address",
        //     // type: "textarea",
        //     span: 12,
        //     row: true,
        // },
    ]
}
