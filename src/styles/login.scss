.login-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  // background: url("http://www.17sucai.com/preview/242158/2015-01-10/%E7%99%BB%E5%BD%95/images/cloud.jpg")
  // 0 bottom repeat-x #049ec4;
  // animation: animate-cloud 20s linear infinite;
}
.login-weaper {
  margin: 0 auto;
  margin-right: 13%;
  width: 400px;
  // box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);
  .el-input-group__append {
    border: none;
  }
}

.login-left,
.login-border {
  position: relative;
  // min-height: 500px;
  min-height: 400px;
  align-items: center;
  display: flex;
}
.login-left {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  justify-content: center;
  flex-direction: column;
  background-color: #409eff;
  color: #fff;
  float: left;
  width: 50%;
  position: relative;
}
.login-left .img {
  width: 140px;
}
.login-time {
  position: absolute;
  left: 25px;
  top: 25px;
  width: 100%;
  color: #fff;
  font-weight: 200;
  opacity: 0.9;
  font-size: 18px;
  overflow: hidden;
}
.login-left .title {
  margin-top: 60px;
  text-align: center;
  color: #fff;
  font-weight: 300;
  letter-spacing: 2px;
  font-size: 25px;
}

.login-border {
  border-left: none;
  // border-top-right-radius: 5px;
  // border-bottom-right-radius: 5px;
  border-radius: 5px;
  color: #fff;
  // background-color: #fff;
  width: 100%;
  float: left;
  box-sizing: border-box;
}
.login-main {
  background-color: #fff;
  margin: 0 auto;
  width: 360px;
  // margin-top: 55px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px 15px;
  border-radius: 5px;
}
.login-main > h3 {
  margin-bottom: 20px;
}
.login-main > p {
  color: #76838f;
}
.login-title {
  color: #333;
  margin-bottom: 40px;
  font-weight: 500;
  font-size: 22px;
  text-align: center;
  letter-spacing: 4px;
}
.login-menu {
  margin-bottom: 40px;
  width: 100%;
  text-align: center;
  height: 50px;
  border-bottom: 1px solid #eee;
  a {
    color: #666;
    font-size: 18px;
    margin: 0px 20px;
    height: 50px;
    line-height: 50px;
    display: inline-block;
    padding: 0 10px;
    border-bottom: 2px solid #1890ff;
    outline: none;
  }
}
.login-submit {
  display: block !important;
  margin: 10px auto 10px auto !important;
  width: 200px;
  height: 48px;
  font-size: 14px !important;
  text-align: center;
  border-radius: 50px !important;
  border: 0px;
  -webkit-box-shadow: rgba(152, 22, 244, 0.19) 0px 5px 10px 2px;
  box-shadow: rgba(152, 22, 244, 0.19) 0px 5px 10px 2px;
}
.login-form {
  margin: 10px 0;
  i {
    color: #333;
  }
  .el-form-item__content {
    width: 100%;
  }
  .el-form-item {
    margin-bottom: 12px;
  }
  .el-input {
    input {
      padding-bottom: 10px;
      text-indent: 5px;
      background: transparent;
      border: none;
      border-radius: 0;
      color: #333;
      border-bottom: 1px solid rgb(235, 237, 242);
    }
    .el-input__prefix {
      i {
        padding: 0 5px;
        font-size: 16px !important;
      }
    }
  }
}
.login-code {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 0 0 0 10px;
}
.login-code-img {
  margin-top: 2px;
  width: 100px;
  height: 38px;
  background-color: #fdfdfd;
  border: 1px solid #f0f0f0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 5px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;
}
.loginTop {
  width: 100%;
  height: 90px;
  padding-top: 20px;
  padding-left: 20px;
  text-align: left;
  position: fixed;
  left: 0;
  top: 0;
}
.top img {
  height: 69px;
}
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  line-height: 30px;
  font-size: 12px;
  color: #999;
  width: 100%;
  text-align: center;
}
.footer p {
  margin: 0;
}
.centertitle {
  margin-top: -60px;
  margin-left: 15%;
  color: #fff;
  font-size: 50px;
  font-weight: bold;
}

.qrcard {
  width: 150px;
  height: 150px;
  background: #ccc;
  display: inline-block;
}
.qrcardTip {
  color: #999;
  font-size: 12px;
  text-align: center;
  margin-top: 30px;
}

.dialogLeft {
  text-align: center;
}
.dialogLeft i {
  font-size: 100px;
  color: #1890ff;
  margin-top: 70px;
}
.dialogLeft p {
  color: #1890ff;
  font-size: 30px;
}
.wrap  .el-dialog {
  height: 420px;
}

.dialogRight {
  text-align: left;
}
.contentWrap {
  position: relative;
  height: 330px;
}
.findContent {
  width: 370px;
  margin: 0 auto;
}
.buttonWrap {
  position: absolute;
  width: 370px;
  bottom: 0;
  right: 35px;
}
.buttonWrap .nextButton {
  width: 100px;
  float: right;
  margin-left: 20px;
}
.el-step__title.is-process {
  font-weight: normal;
  color: #666;
}
.wrap  .el-step__head.is-success,
.wrap  .el-step__title.is-success {
  color: #1890ff;
  border-color: #1890ff;
}
.findContentSuccess {
  font-size: 30px;
  text-align: center;
  color: #1890ff;
}
// .el-icon-success {
//   // font-size: 100px;
//   // margin-top: 30px;
// }
.el-steps {
  background: none;
}

.loginCenter {
  width: 100%;
  height: 70vh;
  position: fixed;
  left: 0;
  top: 15vh;
  background: url(../../styles/image/bj.png);
  background-size: contain;
  background-repeat: repeat-x;
}
.img1 {
  position: absolute;
  left: 10%;
  top: 0;
  height: 100%;
}
.img2 {
  position: absolute;
  top: 15vh;
  left: 20%;
}
.login-form .el-form-item {
  margin-bottom: 8px;
}
.loginTop>img{
  float: left;
}
.loginToptitle {
  font-size: 24px;
  color: #00a5ec;
  font-weight: bold;
  display: inline-block;
  float: left;
  margin-left: 10px;
  line-height: 90px;
}
