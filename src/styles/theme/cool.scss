.theme-cool {
  .el-menu--popup{
      .el-menu-item{
          background-color: #fff;
          i,span{
              color:#666;
          }
          &:hover{
              i,span{
                  color:#333;
              }
          }
          &.is-active {
              background-color: #409EFF;
              &:before {
                  content: '';
                  top: 0;
                  left: 0;
                  bottom: 0;
                  width: 4px;
                  background: #409eff;
                  position: absolute;
              }
              i,span{
                  color:#fff;
              }
          }
      }
  }
.avue-header{
  background: linear-gradient(120deg,#25aff3,#008ad3);
}
.avue-tags {
  padding: 0 3px;
  margin: 8px 0;
  box-shadow: none;
  background-color: transparent;
  .el-tabs__header .el-tabs__item{
    padding: 0 10px !important;
    background-color: #fff;
    margin-right: 5px;
    color: #909399;
    border-radius: 3px;
    height:30px;
    font-size: 12px;
    line-height: 30px;
     &.is-active {
      border: none;
      color:#008ad3;
     }
  }
}
.avue-logo{
  background: #fff;
  box-shadow: none;
}
.avue-sidebar--tip{
  background-color:transparent;
  color:#333;
}
.el-dropdown{
  color:#fff;
}
.avue-logo_title{
  font-weight: 400;
  color:#008ad3;
}
.logo_title,
.avue-breadcrumb
{
    color: #fff ;
    i {
        color: #fff;
    }
}
.avue-top{
  .el-menu-item {
    i,
    span {
        color: #fff ;
    }
    &:hover {
        i,
        span {
            color: #fff ;
        }
    }
  }
}
.avue-sidebar{
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.15);
  background-color: #fff;
  padding-top: 70px;
  .el-menu-item,.el-submenu__title{
    font-size: 13px;
    i,span{
        color:#000;
    }
    &:hover{
        background: transparent;
        i,span{
           color:#000;
        }
    }
    &.is-active {
      background-color: #e5f1fb;
      &::before{
        width: 0;
      }
        i,span{
          color: #25aff3;
        }
    }
  }
}
.top-search {
    .el-input__inner{
      color: #333;
    }
    input::-webkit-input-placeholder,
    textarea::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #fff;
    }
    input:-moz-placeholder,
    textarea:-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #fff;
    }
    input::-moz-placeholder,
    textarea::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #fff;
    }
    input:-ms-input-placeholder,
    textarea:-ms-input-placeholder {
        /* Internet Explorer 10+ */
        color: #fff;
    }
}
.top-bar__item {
    i {
        color: #fff;
    }
}
}