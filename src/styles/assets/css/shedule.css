@charset "utf-8";
.wrap{
    width: 100%;
    height: auto !important;
    overflow-y: inherit !important;
    padding: 20px;
    padding-top: 20px;
    padding-left: 0;
    padding-right: 0;
    background:#F0F2F5;
    font-size: 14px;
    position: relative;
    margin-top: -20px;
}
.center{
    float: left;
    width: 1150px;
    position: absolute;
    left: 50%;
    margin-left: -575px;
}
.centerAuto{
    float: left;
    width: calc(100% - 200px);
    margin-left: 15px;
}
.sheduleCenter{
    padding: 30px;
    padding-top: 5px;
    background: #fff;
    margin-bottom: 60px;
}
.top{
    width: 100%;
    height: 60px;
    line-height: 60px;
}
.topTitle{
    font-size: 20px;
    color: #424242;
    font-weight: bold;
    float: left;
}
.wrap  .events {
  list-style: none;
  margin: 0;
  padding: 0;
}
.wrap  .events .ant-badge-status {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
  font-size: 12px;
}
.wrap  .notes-month {
  text-align: center;
  font-size: 28px;
}
.wrap  .notes-month section {
  font-size: 28px;
}
