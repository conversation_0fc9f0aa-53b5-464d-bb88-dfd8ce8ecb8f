.wrap{
    width: 100%;
    height: auto !important;
    overflow-y: inherit !important;
    padding: 20px;
    padding-top: 15px;
    padding-left: 0;
    padding-right: 0;
    background:#F0F2F5;
    font-size: 14px;
    position: relative;
    margin-top: -20px;
}
.center{
    /*float: left;*/
    width: 1150px;
    position: absolute;
    /*left: 50%;*/
    /*margin-left: -575px;*/
}
.desktop-center{
    z-index: 2;
    min-height: calc(100vh - 100px);
    position: relative;
    padding-bottom: 20px !important;
}
.centerAuto{
    float: left;
    width: calc(100% - 200px);
    margin-left: 15px;
}
.banner{
    width: 100%;
    height: 240px;
    background: #fff;
    border-radius: 3px;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.bannerDiv{
  width: 100%;
  height: 240px;
  border-radius: 3px;
}
.carousel-item{
    border-radius: 3px;
}
.serviceWrap{
    width: 100%;
    height: 240px;
    background: #fff;
    margin-top: 20px;
    border-radius: 3px;
    position: relative;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.serviceTop{
    width: 100%;
    height: 44px;
    line-height: 40px;
    font-size: 18px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
}
.serviceTopTitle{
    position: absolute;
    left: 0;
    top: 7px;
    font-size: 16px;
    line-height: 30px;
    margin-left: 20px;
    font-weight: bold;
}
.small-serviceTopTitle{
    margin-top: 7px;
    font-size: 16px;
    line-height: 30px;
    margin-left: 20px;
    font-weight: bold;
    float: left;
    color: rgba(0,0,0,0.65);
}
.tabChange{
    position: absolute;
    width: 350px;
    left: 50%;
    top: 0;
    margin-left: -175px;
}
.tabChange div{
    float: left;
    height: 48px;
    padding: 0 5px;
    font-size: 16px;
    margin-left: 30px;
    cursor: pointer;
}
.tabChangeClick{
    font-weight: bold;
    color:#1890FF;
    border-bottom: 3px solid #1890FF;
}
.serviceMore{
    position: absolute;
    right: 0px;
    top: 12px;
    margin-right: 10px;
    color: #999;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
}
.small-serviceMore{
    margin-top: 12px;
    margin-right: 10px;
    color: #999;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    float: right;
}
.serviceMore i,.small-serviceMore i{
    font-size: 35px !important;
}
.serviceContent{
    width: 100%;
    /* height: 200px; */
    height: 190px;
    padding: 15px 16px;
    padding-top: 20px;
    /* padding-top: 30px; */
    overflow: hidden;
}
.service{
    float: left;
    width: 157px;
    height: 75px;
    line-height: 75px;
    padding-right: 2px;
    padding-top: 10px;
    padding-left: 5px;
    line-height: 0px;
    margin-right: 30px;
    color: #777;
    cursor: pointer;
    position: relative;
    text-align: left;
}
.service:nth-of-type(6n+6){
    margin-right: 0;
}
.serviceDiv{
    width: 100%;
    height: 100%;
}
.service>div span{
    display: inline-block;
    width: 90px;
    height: 45px;
    line-height: 45px;
    vertical-align: middle;
    /* margin-top: 15px; */
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.service img{
    width: 45px;
    height: 45px;
    margin-right: 10px;
    vertical-align: middle;
    border-radius: 50%;
}
.serviceHover{
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 3px;
    width: 100%;
    height: 100%;
    background: #fff;
    border: 1px solid #eee;
    box-shadow: 2px 2px 5px rgba(30, 48, 76, 0.2);
}
/* .service:hover .serviceHover{
  display: block;
} */
.hoverTitle {
  width: 27px;
  height: 27px;
  line-height: 27px;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  padding: 0px;
  border-bottom: 1px solid #eee;
  background: url(../img/xx.png) no-repeat center;
  background-size: 15px;
  float: right;
  margin-top: -27px;
  z-index: 100000;
}
.hoverTitleClick {
  background: url(../img/xxclick.png) no-repeat center;
  background-size: 15px;
}
.hoverTitle1{
    width: 130px;
    height: 27px;
    line-height: 27px;
    font-size: 12px;
    font-weight: bold;
    color: #666;
    padding: 0 10px;
    border-bottom: 1px solid #eee;
}
.hoverWrap{
    width: 100%;
    height: 45px;
    padding: 0 10px;
}
.hoverWrap img{
    width: 30px;
    height: 30px;
    margin-top: 8px;
    float: left;
    border-radius: 15px;
}
.hoverWrap p{
    float: left;
    color: #999;
    font-size: 12px;
    height: 15px;
    line-height: 15px;
    margin-top: 3px;
    margin-left: 5px;
}
.hoverWrap p:nth-of-type(1){
    margin-top: 6px;
}
.systemWrap{
    width: 100%;
    height: 200px;
    background: #fff;
    border-radius: 3px;
    margin-top: 20px;
    padding: 0 20px;
    overflow: hidden;
}
.system{
    width: 31%;
    height: 40px;
    float: left;
    margin-right: 3%;
    line-height: 40px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    margin-top: 15px;
    background: #4F97D4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    padding: 0 5px !important;
}
.noneServiceWrap{
    background: #F0F2F5;
    box-shadow: none;
}
.newWrap{
    width: calc(50% - 10px);
    height: 100%;
    float: left;
    background: #fff;
    border-radius: 3px;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.noneServiceWrap .newWrap:nth-of-type(1){
    margin-right: 20px;
}
.newContent{
    width: 100%;
    height: 200px;
    padding: 5px 20px;
    text-align: left;
    overflow: hidden;
}
.newContent1{
    padding: 5px 10px;
}
.newUl{
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.newUl li{
    width: 100%;
    height: 29x;
    line-height: 29px;
    padding: 0 10px;
}
.newUl li a{
    color: #666;
}
.newLiTitle{
    float: left;
    width: calc(100% - 100px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.newLiTime{
    float: right;
    width: 100px;
    text-align: right;
    font-size: 12px;
    color: #999;
}
.daiban{
    display: inline-block;
    border: 1px solid #F7B35A;
    height: 22px;
    line-height: 20px;
    border-radius: 3px;
    color: #F7B35A;
    margin-right: 5px;
    padding: 0 3px;
}
.shenqing{
    display: inline-block;
    border: 1px solid #B1D155;
    height: 22px;
    line-height: 20px;
    border-radius: 3px;
    color: #B1D155;
    margin-right: 5px;
    padding: 0 3px;
}
.xiaoxi{
    display: inline-block;
    border: 1px solid #80C2DF;
    height: 22px;
    line-height: 20px;
    border-radius: 3px;
    color: #80C2DF;
    margin-right: 5px;
    padding: 0 3px;
}
.mineDesktopDataWrap{
    float: left;
    width: 33%;
    margin-top: 10px;
    border-right: 1px solid #eee;
}
.mineDesktopData{
    margin-left: 5%;
    margin-right: 5%;
    padding: 10px 0;
    cursor: pointer;
}
.mineDesktopData:hover{
    background: #F5F6F8;
    border-radius: 3px;
}
.mineDesktopData:hover .mineDesktopDataNumber{
    font-size: 28px;
}
.mineDesktopDataWrap:nth-of-type(3n+3){
    border-right: 0;
}
/* .mineDesktopData img{
    float: left;
    width: 60px;
    height: 60px;
} */
.mineDesktopData .mineDesktopDataNumber{
    font-size: 24px;
    /* padding-left: 80px; */
    line-height: 35px;
    text-align: center;
    font-weight: bold;
}
.mineDesktopData .mineDesktopDataTitle{
    font-size: 14px;
    color: #666;
    /* padding-left: 70px; */
    line-height: 20px;
    text-align: center;
}
.margin40{
  margin-bottom: 40px;
}
.wrap  .ant-tabs-bar{
  margin: 0;
}
.wrap  .ant-tabs-tab{
  font-size: 16px;
}
.mineServiceWrap{
    background: #fff;
    border-radius: 3px;
}
.ant-tabs-nav .ant-tabs-tab{
    padding: 12px 0;
}








/* 集成系统颜色 */
.systemHover:nth-of-type(1) .system{
    background: #4F97D4;
}
.systemHover:nth-of-type(2) .system{
    background: #EE756C;
}
.systemHover:nth-of-type(3) .system{
    background: #DDB860;
}
.systemHover:nth-of-type(4) .system{
    background: #76AA7A;
}
.systemHover:nth-of-type(5) .system{
    background: #5AA2A7;
}
.systemHover:nth-of-type(6) .system{
    background: #C6C3C3;
}
.systemHover:nth-of-type(7) .system{
    background: #5DBDC4;
}
.systemHover:nth-of-type(8) .system{
    background: #FFA643;
}
.systemHover:nth-of-type(9) .system{
    background: #85C5D5;
}
.systemHover:nth-of-type(10) .system{
    background: #9C76A9;
}
.systemHover:nth-of-type(11) .system{
    background: #6DCAAA;
}
.systemHover:nth-of-type(12) .system{
    background: #58B2DC;
}
.systemHover:nth-of-type(13) .system{
    background: #E79460;
}
.systemHover:nth-of-type(14) .system{
    background: #86C166;
}
.systemHover:nth-of-type(15) .system{
    background: #3A8FB7;
}
.systemHover:nth-of-type(16) .system{
    background: #6E75A4;
}
.systemHover:nth-of-type(17) .system{
    background: #6D2E5B;
}
.systemHover:nth-of-type(18) .system{
    background: #434343;
}


.newContent .mineDesktopDataWrap:nth-of-type(1) .mineDesktopDataNumber{
    color: #85c5d5;
}
.newContent .mineDesktopDataWrap:nth-of-type(2) .mineDesktopDataNumber{
    color: #EE756C;
}
.newContent .mineDesktopDataWrap:nth-of-type(3) .mineDesktopDataNumber{
    color: #6DCAAA;
}
.newContent .mineDesktopDataWrap:nth-of-type(4) .mineDesktopDataNumber{
    color: #DDB860;
}
.newContent .mineDesktopDataWrap:nth-of-type(5) .mineDesktopDataNumber{
    color: #C56EF3;
}
.newContent .mineDesktopDataWrap:nth-of-type(6) .mineDesktopDataNumber{
    color: #FFB11B;
}
