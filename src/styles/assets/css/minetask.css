@charset "utf-8";
.wrap{
    width: 100%;
    height: auto !important;
    overflow-y: inherit !important;
    padding: 20px;
    padding-top: 20px;
    padding-left: 0;
    padding-right: 0;
    background:#F0F2F5;
    font-size: 14px;
    position: relative;
    margin-top: -20px;
    padding-bottom: 0;
}
.center{
    float: left;
    width: 1150px;
    position: absolute;
    left: 50%;
    margin-left: -575px;
}
.centerBox{
    -moz-box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
    -webkit-box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
    border-radius: 3px;
}
.centerPadding{
    padding: 0 30px;
    background-color: #fff;
    /* padding-bottom: 60px; */
}
.centerWrap{
    position: relative;
    min-height: calc(100vh - 140px);
}
.centerAuto{
    float: left;
    width: calc(100% - 200px);
    margin-left: 15px;
    background-color: #fff;
    border-radius: 3px;
    padding: 0 20px;
}
.top{
    width: 100%;
    height: 60px;
    line-height: 60px;
}
.topTitle{
    font-size: 20px;
    color: #424242;
    font-weight: bold;
    float: left;
}
.topNav{
    float: right;
    font-size: 14px;
    color: #999;
    cursor: pointer;
}
.topNav span:hover{
    color: #1890FF;
}
.topSelect{
    color: #666;
}
.middle{
    width: 100%;
    height: 40px;
    line-height: 40px;
    margin-top: 5px;
}
.middle select,.middle input{
    width: 120px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ccc;
    border-radius: 3px;
    float: left;
}
.middle select{
    appearance:none;
    -moz-appearance:none;
    -webkit-appearance:none;
    background: url(../image/down.png) no-repeat right 3px center ;
    background-size: 15px;
    padding-right: 0px;
}
.middle select::-ms-expand { display: none; }
.middle input{
    width: 180px;
    padding-left: 5px;
    padding-right: 10px;
    margin-left: 10px;
    float: left;
}
.middle button{
    width: 80px;
    height: 30px;
    line-height: 30px;
    border: none;
    border-radius: 3px;
    color: #fff;
    background: #1890FF;
    cursor: pointer;
    float: left;
}
.middle button:nth-last-of-type(1){
    margin-left: -5px;
    width: 60px;
    border-radius: 0 3px 3px 0;
}
.middle .messageDelete{
    background: #fff;
    color: #1890FF;
    border: 1px solid #1890FF;
    margin-right: 10px;
}



.wrap  .ant-tabs-nav-scroll{
  text-align: left !important;
}
.modal >>> .ant-form-item-label{
  width: 100px;
}
.modal >>> .ant-form-item-control-wrapper{
  width: calc(100% - 120px);
  display: inline-block;
}
.wrap  .ant-table-thead > tr > th, .wrap  .ant-table-tbody > tr > td{
  padding: 5px 16px;
}
.wrap  .ant-tabs-tab-active{
    background: none;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr > td{
    padding: 10px 16px;
}
.ant-table-pagination.ant-pagination{
    margin-bottom: 30px;
}
.ant-tabs-nav .ant-tabs-tab{
    padding: 12px 0;
}
.ant-table{
    border-top: 1px solid #e8e8e8;
    margin-top: 10px;
}
.desktopBianji,.DesktopSetBlock{
    color: #00a5ec;
    margin-right: 10px;
}
.DesktopSetBlock{
    margin-right: 0;
}
