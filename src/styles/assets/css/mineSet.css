@charset "utf-8";
.wrap {
  width: 100%;
  height: auto !important;
  overflow-y: inherit !important;
  padding: 20px;
  padding-top: 20px;
  padding-left: 0;
  padding-right: 0;
  background: #f0f2f5;
  font-size: 14px;
  position: relative;
  margin-top: -20px;
}
.center {
  float: left;
  width: 1150px;
  position: absolute;
  left: 50%;
  margin-left: -575px;
}
.mineSetWrap {
  background: #fff;
  padding: 0 30px;
  border-radius: 3px;
  padding-bottom: 50px;
}
.centerAuto {
  float: left;
  width: calc(100% - 200px);
  margin-left: 15px;
}

.setTitle {
  font-size: 20px;
  font-weight: bold;
  color: #424242;
  line-height: 60px;
  margin-bottom: 10px;
}
.setTitle span {
  font-size: 14px;
  margin-left: 14px;
  font-weight: normal;
  color: #999;
}
.mineSetInfoTitle {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: bold;
  color: #000;
}

.el-tabs__item {
  font-size: 14px;
}
/* .jianjie .ant-col-6{
    width: 90px !important;
} */
.jianjie .ant-col-18 {
  width: 95% !important;
}
.changeHeadImg {
  text-align: center;
  padding-top: 40px;
}
.changeHeadImg button {
  margin-top: 20px;
}
.ant-list-item-action a {
  color: #1890ff;
}

/* .ant-tabs-tab-active{
    background-color: #E6F7FF;
} */
