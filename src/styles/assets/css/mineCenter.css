@charset "utf-8";
.wrap {
  width: 100%;
  height: auto !important;
  overflow-y: inherit !important;
  padding: 20px;
  padding-top: 20px;
  padding-left: 0;
  padding-right: 0;
  background: #f0f2f5;
  font-size: 14px;
  position: relative;
  margin-top: -20px;
}
.center {
  float: left;
  width: 1150px;
  position: absolute;
  left: 50%;
  margin-left: -575px;
}
.mineCenterWrap {
  background: #fff;
  padding-bottom: 0.1px;
}
.centerAuto {
  float: left;
  width: calc(100% - 200px);
  margin-left: 15px;
}
.nameWrap {
  width: 100%;
  height: 120px;
  text-align: left;
  padding: 20px;
}
.imgNameWrap {
  width: 80px;
  height: 80px;
  float: left;
  overflow: hidden;
  border-radius: 50%;
}
.imgNameWrap img {
  width: 100%;
}
.mineCenterName {
  line-height: 40px;
  float: left;
  margin-left: 20px;
  width: calc(100% - 100px);
}
.mineCenterName span {
  font-size: 18px;
  font-weight: bold;
  margin-right: 10px;
}
.mineCenterNumber {
  line-height: 40px;
  float: left;
  margin-left: 20px;
}
.infoWrap {
  width: 100%;
  padding: 0 20px;
}
.infoWrap .info {
  width: 100%;
  min-height: 150px;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 0 10px;
  text-align: left;
  margin-bottom: 20px;
}
.infoTitle {
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}
.grid-content > span {
  display: inline-block;
  line-height: 30px;
  font-size: 14px;
}
.infoName {
  width: 100px;
  text-align: right;
}
.infoName1 {
  width: calc(100% - 100px);
  text-align: left;
}
.zanwu {
  font-size: 12px;
  margin-top: 50px;
  color: #666;
  text-align: center;
}
