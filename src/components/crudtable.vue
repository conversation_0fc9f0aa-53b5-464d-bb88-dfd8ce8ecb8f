<template>
  <div style="width: 100%;">
    <el-table
        v-if="tableShow"
        :data="datalist"
        border
        style="width: 100%">
      <el-table-column
          v-for="field in dictFieldList"
          :key="field.id"
          :prop="field.fielden"
          :label="field.fieldzh">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {geturldata} from "@/api/ksgl/tdd";

export default {
  name: "crudtable",
  props:{
    dictFieldList: Array,
    url: String,
    param: Object,
  },
  data(){
    return{
      datalist: [],
      tableShow: false,
    }
  },
  created() {
    console.log(this.dictFieldList,this.url,this.param)
    this.getdata();
  },
  methods:{
    getdata(){
      if( this.url && this.param.ksh){
        geturldata({ksh:this.param.ksh},this.url).then(res=>{
          console.log(`geturldata`,res);
          let data = res.data.data;
          this.datalist = data;
          this.tableShow = true;

        })
      }
    },
  },
}
</script>

<style scoped>
</style>