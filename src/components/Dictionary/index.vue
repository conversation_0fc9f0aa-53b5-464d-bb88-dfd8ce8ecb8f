<template>
  <el-select
    v-model="dictionary"
    placeholder="请选择"
    clearable
    @change="selectChange"
    size="mini"
    :multiple="drill.drill"
  >
    <el-option
      v-for="item in dictionaryOptions"
      :key="item.id"
      :label="item.name"
      :value="item.value"
    />
  </el-select>
</template>

<script>
import {selectDictList} from "@/api/sysDict"; // 获取数据字典
export default {
  name: "GetDictionary",
  props: {
    dictData:Array,
    dictKey: String, // 字典code
    updataDict: String, // 回显绑定的值
    drill: Boolean
  },
  data() {
    return {
      dictionary: "", // 绑定选中的值
      dictionaryOptions: [] // 拉下字典选项
    };
  },
  watch: {
    dictData: {
      immediate: true,
      handler() {
        this.getSystem();
      }
    },dictKey: {
      immediate: true,
      handler() {
        this.getSystem();
      }
    },
    updataDict: {
      immediate: true,
      handler() {
        this.dictionary = this.updataDict;
      }
    }
  },
  created() {
    this.dictionary = this.updataDict;
    this.getSystem();
  },
  mounted() {
    // this.dictionary = this.updataDict;
  },
  methods: {
    // 获取数据字典
    async getSystem() {
      if (this.dictData && this.dictData.length > 0) {
        this.dictionaryOptions = this.dictData;
      } else {
        const data = await selectDictList({code: this.dictKey});
        if (data.data.code != "00000") return;
        this.dictionaryOptions = data.data.info;
      }
    },
    selectChange(val) {
      this.$emit("input", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
