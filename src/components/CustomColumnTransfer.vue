<template>
  <div class="selector-layout">
    <el-transfer
        filterable
        filter-placeholder="请输入搜索"
        :titles="['自定义列 [可选]', '自定义列 [已选]']"
        target-order="push"
        v-model="value"
        :data="data"
        @right-check-change="handleRightchange"
    >
      <div slot-scope="{ option }">
        <div class="default-tranfer-item" @mouseenter="indexKey = option.key"
             @mouseleave="indexKey = null">
          <span>{{ option.label }}</span>
          <div v-show="value.includes(option.key) && indexKey === option.key" class="button-group">
            <!--  阻止事件冒泡  -->
            <!-- 自定义数据项，将上、下、底部、顶部的排序功能放在数据项里面 -->
            <el-tooltip content="上移" placement="top" :effect="tooltipEffect" :hide-after="tooltipHideAfter">
              <em class="el-icon-top" @click.stop.prevent="publicMobileMethod('handleUp', option.key)"></em>
            </el-tooltip>
            <el-tooltip content="置顶" placement="top" :effect="tooltipEffect" :hide-after="tooltipHideAfter">
              <em class="el-icon-upload2"
                  @click.stop.prevent="publicMobileMethod('handleTop', option.key)"></em>
            </el-tooltip>
            <el-tooltip content="下移" placement="top" :effect="tooltipEffect" :hide-after="tooltipHideAfter">
              <em class="el-icon-bottom" @click.stop.prevent="publicMobileMethod('handleDown', option.key)"></em>
            </el-tooltip>
            <el-tooltip content="置底" placement="top" :effect="tooltipEffect" :hide-after="tooltipHideAfter">
              <em class="el-icon-download" @click.stop.prevent="publicMobileMethod('handleBottom', option.key)"></em>
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-transfer>
    <div slot="footer" class="dialog-footer">
      <div style="width:30%;float:right;text-align: right;padding-top:20px;padding-right:20px;">
        <el-button size="small" class="el-button" type="primary" @click="handleSaveColumn">确 定</el-button>
        <el-button size="small" @click="close">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script>

import {getAllCustomColData, getAllDictField, saveCustomColData} from "@/api/customColumn";

export default {
  name: "CustomColumnTransfer",
  props: {
    selectedColumnParam: {
      type: Object,
      require: true
    },
  },
  data() {
    return {
      data: [],
      value: [],
      filterMethod(query, item) {
        return item.label.indexOf(query) > -1;
      },
      queryParams: {
        type: this.selectedColumnParam.type,  // 类型
      },
      selectedData: [], // 已选的数据
      formFieldsData:[], // 表单字段数据
      item: [], // 右侧勾选数据
      indexKey: null, // 高亮显示
      tooltipEffect: "light",	// 两个不同的主题：dark和light
      tooltipHideAfter: "1000",	// Tooltip 出现后自动隐藏延时，单位毫秒，为 0 则不会自动隐藏
    };
  },
  watch: {
    'selectedColumnParam.type': function (val) {
      this.queryParams.type = val;
      console.log(this.queryParams);
      this.loadData();
    },
  },
  created() {
    this.loadData();
  },
  methods: {
    loadData() {
      this.data = [];
      this.selectedData = [];
      let query = new Promise((resolve, reject)=>{resolve(this.queryParams)});
      let getDataByProjectIdPromise = new Promise((resolve, reject)=>{
        query.then(res=>{
          let queryData = {
            type: this.queryParams.type
          };
          getAllCustomColData(queryData).then(r =>{
            if(r.data.code === 200){
              let dataTmp = r.data.data;
              let selectedData = [];
              if(dataTmp && dataTmp.length > 0){
                dataTmp.forEach(obj => {
                  selectedData.push(obj.fielden);
                });
              }
              resolve(selectedData);
              this.value = selectedData;
              this.selectedData = dataTmp;
            }
          })
        })
      });
      let queryFormFieldListPromise = new Promise((resolve, reject)=>{
        query.then(resolve=>{
          let queryData = {
            type: this.queryParams.type
          };
          getAllDictField(queryData).then(res =>{
            if(res.data.code === 200){
              this.formFieldsData = res.data.data;
              let selectedData = resolve;
              let data = [];
              this.formFieldsData.forEach(tmpObj => {
                data.push({
                  key: tmpObj.fielden,
                  label: tmpObj.fieldzh,
                });
              });
              this.data = data;
            }
          })
        })
      });
    },

    /**
     * 保存数据
     */
    handleSaveColumn() {
      if((!this.selectedData || this.selectedData.length==0) && (!this.value || this.value.length == 0)){
        this.$modal.msgError('请选择列');
        return;
      }
      // 保存选择的列数据
      this.selectedData = [];
      let sort = 0;
      let formFieldsTmp = {};
      this.formFieldsData.forEach(obj =>{
        formFieldsTmp[obj.fielden] = obj;
      });
      this.value.forEach(tmp=>{
        let obj = formFieldsTmp[tmp];
        if(!!obj){
          this.selectedData.push({
            fielden: obj.fielden,
            fieldzh: obj.fieldzh,
            type: obj.scope,
            sort: sort++,
            sfss: obj.sfss,
            fieldType: obj.fieldType,
            fieldAttribute: obj.fieldAttribute,
            fieldData: obj.fieldData,
          });
        }
      });
      let objData = {
        type: this.queryParams.type,
        columnData: this.selectedData
      };
      saveCustomColData(objData).then(res=>{
        if (res.data.code == "00000") {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        } else {
          this.$message({
            type: "error",
            message: "操作失败!"
          });
        }
        this.close();
      })
    },
    /**
     * 取消
     */
    close(){
      this.$emit('close', {
        customColumnOpen: false,
      });
      this.loadData();
    },

    /**
     * 监听右侧选中
     */
    handleRightchange (value) {
      this.item = value;
    },
    /**
     * 右侧数据点击排序
     */
    publicMobileMethod (direction, key) {
      const self = this;
      let index;
      // 判断是否超出一条
      const item = self.item;
      if (item.length === 1 || item.length === 0) {
        self.value.forEach((val, indexs) => {
          // 获取需移动数据的下标
          if (val === key) {
            index = indexs;
          }
        });
        if (index === 0 && direction !== 'handleBottom' && direction !== 'handleDown') {
          return self.$message('没有上移的空间了');
        }
        if (index === self.value.length - 1 && direction !== 'handleUp' && direction !== 'handleTop') {
          return self.$message('没有下移的空间了');
        }
        // 改变的数组
        const changeItem = self.value[index];
        // 根据下标在数组中删除该数据
        self.value.splice(index, 1);
        // 判断加入数组位置
        if (direction) {
          // 置顶
          direction === 'handleTop' && self.value.unshift(changeItem);
          // 置底
          direction === 'handleBottom' && self.value.push(changeItem);
          // 上移
          direction === 'handleUp' && self.value.splice(index - 1, 0, changeItem);
          // 下移
          direction === 'handleDown' && self.value.splice(index + 1, 0, changeItem);
        }
      } else {
        self.$message.error('只能选择一条数据进行上下移动');
      }
    },

  }
};
</script>

<style lang="scss">

.el-transfer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-transfer-panel__item {
    margin-right: 0;
  }

  .default-tranfer-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .button-group {
      em {
        margin-right: 8px;
      }
    }
  }

  .el-input.el-input--small {
    .el-input__inner {
      border-radius: 4px;
    }
  }

  .el-transfer__buttons {
    padding: 0;
    margin: 0 17px;

    .el-transfer__button {
      display: block;
      margin: 0 0 5px 0;
      padding: 4px 8px;
    }

  }

  .el-transfer-panel {
    width: 49%;
    border-radius: 0;
  }
}

</style>
