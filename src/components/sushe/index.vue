<template>
    <div class="carousel-wrapper">
      <div class="main-wrapper" :style="{width: rightShow ? '77%' : '98%'}">
        <div class="wrapper-right" >
          <div v-for="(e,i) in list" :key="i" class="wrapper_box">
            <div style="margin: auto">
              <div style="height: 40px;text-align: center">
                <div style="font-size: 20px;font-weight: bold;line-height: 40px;float: left;">{{e.name}}</div>
                <div style="line-height: 40px;color: #808383;font-size: 12px">
                  房间数: {{e.fjs}}间 | 入住人数: {{lcrzslist[i]}}
                </div>
              </div>
              <div v-for="(item,index) in e.data[e.fjtd[0]]" :key="index" class="wrapper_box_item" @click="getsushedetail(item)" :style="{'border': '3px solid' + (item.RZRS == item.CWS ? '#00a5ec' : (item.RZRS == 0 ? '#E06655' : '#54BFA0'))}">
                <div v-if="item.CWS">
                  <div style="color: #fff;background-color: #00a5ec;height: 30px;line-height: 30px" :style="{'background-color': item.RZRS == item.CWS ? '#00a5ec' : (item.RZRS == 0 ? '#E06655' : '#54BFA0')}">{{item.SSMC}}</div>
                  <div style="height: 30px;line-height: 30px;">
                    <span style="color: #333;line-height: 30px;font-size: 12px;float: left;margin-left: 10px;">{{item.RZRS}}/{{item.CWS}}</span>
                    <span style="color: #333;line-height: 30px;font-size: 12px;float: right;margin-right: 10px;" v-if="item.DWMC">{{ item.DWMC.indexOf(',') !=-1 ? (item.DWMC.slice(0,1) + item.DWMC.slice(item.DWMC.indexOf(',')+1,item.DWMC.indexOf(',')+2)) : item.DWMC.slice(0,1)}}</span>
                  </div>
                </div>
                <div v-else>
                  <div style="color: #333;height: 56px;line-height: 60px;opacity: 0.5;background-color: rgb(251 251 251);font-weight: bold">{{item.SSMC}}</div>
                </div>
              </div>
              <div>
                <div v-for="(item,index) in e.data[e.fjtd[0]]" :key="index" style="display: inline-block;height: 40px;width: 85px !important;background-color: #fff;text-align: center;line-height: 40px;color: #C0C0C0AF;"></div>

              </div>
              <div v-for="(item,index) in e.data[e.fjtd[1]]" :key="index" class="wrapper_box_item" @click="getsushedetail(item)" :style="{'border': '3px solid' + (item.RZRS == item.CWS ? '#00a5ec' : (item.RZRS == 0 ? '#E06655' : '#54BFA0'))}">
                <div v-if="item.CWS">
                  <div style="color: #fff;background-color: #00a5ec;height: 30px;line-height: 30px" :style="{'background-color': item.RZRS == item.CWS ? '#00a5ec' : (item.RZRS == 0 ? '#E06655' : '#54BFA0')}">{{item.SSMC}}</div>
                  <div style="height: 30px;line-height: 30px;">
                    <span style="color: #333;line-height: 30px;font-size: 12px;float: left;margin-left: 10px;">{{item.RZRS}}/{{item.CWS}}</span>
                    <span style="color: #333;line-height: 30px;font-size: 12px;float: right;margin-right: 10px;" v-if="item.DWMC">{{ item.DWMC.indexOf(',') !=-1 ? (item.DWMC.slice(0,1) + item.DWMC.slice(item.DWMC.indexOf(',')+1,item.DWMC.indexOf(',')+2)) : item.DWMC.slice(0,1)}}</span>
                  </div>
                </div>
                <div v-else>
                  <div style="color: #333;height: 56px;line-height: 60px;opacity: 0.5;background-color: rgb(251 251 251);font-weight: bold">{{item.SSMC}}</div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div @click="isright" style="width: 20px;float: left;height:80vh;line-height: 80vh;background-color: #fff">
        <i v-if="rightShow" class="el-icon-arrow-right"></i>
        <i v-if="!rightShow" class="el-icon-arrow-left"></i>
      </div>

      <div v-if="rightShow" class="main-wrapper-r" >
        <div class="main-wrapper-r-content">
          <div style="background-color: rgba(218,215,215,0.49);cursor: pointer;height: 30px;line-height: 30px;color: #1e9fff">
            公寓基本信息
          </div>
          <div>
            <div v-if="crudParam.contextData" style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">楼栋名称:</span>
              <span style="float: right;">{{crudParam.contextData.sslmc}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">楼层数:</span>
              <span style="float: right;">{{list.length}}层</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">总床位数:</span>
              <span style="float: right;">{{zcws}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">总房间数:</span>
              <span style="float: right;">{{paramlist.length}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">空床位数:</span>
              <span style="float: right;">{{kcws}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">空房间数:</span>
              <span style="float: right;">{{kfjs}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">入住人数:</span>
              <span style="float: right;">{{rzrs}}</span>
            </div>
          </div>



        </div>
        <div class="main-wrapper-r-content" style="text-align: left">
          <div style="background-color: rgba(218,215,215,0.49);cursor: pointer;height: 30px;line-height: 30px;color: #1e9fff;margin-bottom: 10px;text-align: center">
            当前宿舍信息
          </div>
          <div style="color: rgba(51, 51, 51, 0.88);">
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">宿舍名称:</span>
              <span style="float: right;">{{ssxx.SSMC}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">房间位置:</span>
              <span style="float: right;">{{ssxx.DYMC}}{{ssxx.FLMC}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">房间人数:</span>
              <span style="float: right;">{{ssxx.RZRS}}</span>
            </div>
            <div style="font-size: 14px; line-height: 20px;height: 20px;">
              <span style="float: left;">房间特点:</span>
              <span style="float: right;">{{ssxx.FJTD}}</span>
            </div>
          </div>
          <div style="background-color: rgba(218,215,215,0.49);height: 30px;cursor: pointer;line-height: 30px;color: #1e9fff;margin-bottom: 10px;text-align: center">
            宿舍入住信息
          </div>
            <div v-for="(item,index) in itemDetail" :key="index" class="content_box">
                <div style="font-size: 12px; line-height: 20px;height: 20px;">
                    <span style="float: left;color: #00a5ec;margin-left: 10px;font-size: 14px;font-weight: bold">{{item.BEBH}}</span>
                    <span style="float: right;margin-right: 10px;color: #808383;">{{item.DWMC ? item.DWMC.slice(0,2) : ""}}</span>
                </div>
                <div style="line-height: 30px;height: 30px;font-size: 12px;color:#808383;">{{item.XM ? item.XM : '空'}}</div>
            </div>

        </div>
      </div>
    </div>
</template>

<script>

import {getDirllData} from "@/api/bigscreen";

export default {
    props: ['paramlist','crudParam'],
    data() {
        return {
          list:[],
          itemDetail: null,
          zcws: null,
          kcws: null,
          rightShow: false,
          kfjs: 0,
          rzrs: 0,
          DWMC: '传媒学院,医学院',
          ssxx: {

          },
          lcrzslist: [],
        }
    },
    watch: {
      'paramlist':{
        handler(val){
          this.zcws = 0;
          this.kcws = 0;
          val.forEach(item=>{
            if(item.CWS){
              this.zcws += item.CWS;
              this.kcws += (item.CWS - item.RZRS)
            }
            if(item.RZRS == 0){
              this.kfjs = this.kfjs + 1
            }
            if(item.RZRS){
              this.rzrs += item.RZRS
            }
          })
          let FLMC = val.reduce((pre, cur) => {
            (pre[cur.FLMC] || (pre[cur.FLMC] = [])).push(cur);
            return pre
          }, [])
          let list = Object.keys(FLMC)
          console.log(`FLMC`,FLMC)
          list.forEach(item=>{
            let FJTD = FLMC[item].reduce((pre, cur) => {
              (  pre[cur.FJTD] || (pre[cur.FJTD] = [])).push(cur);
              return pre
            }, [])
            let rzrs = 0
            FLMC[item].forEach(e=>{
              if(e.RZRS){
                rzrs += e.RZRS
              }
            })
            console.log(`FJTD`,FJTD)

            console.log(`rzrs`,rzrs)
            this.lcrzslist.push(rzrs)
            this.list.push({
              name: item,
              data: FJTD,
              fjtd: ["北","南"],
              fjs: FLMC[item].length
            })
          })
          console.log(`this.list`,this.list)
        }
      },
      'crudParam':{
        handler(val) {
          val.setCode = val.drillReportSSId
        }
      }
    },
    computed: {
    },
    created() {
    },
    beforeUpdate() {
    },
    methods: {
      isright(){
        this.rightShow = !this.rightShow
      },
      getsushedetail(item){
        this.ssxx = item
        // this.crudParam.contextData ={}
        // this.itemDetail = new Array(item.CWS).fill({
        //     BEBH: 0,
        //     DWMC: '',
        //     XH: '',
        //     XM: '空'
        // });
        // for(let i = 0; i < item.CWS; i++){
        //   this.itemDetail.push({
        //     'BEBH': '0' + (i+1).toString()
        //   })
        // }
          this.itemDetail = new Array
        this.crudParam.contextData.ssid = item.SSID
        getDirllData({
          page: 1,
          pageSize: 999999,
          queryParam: this.crudParam
        }).then(res => {
          const data = res.data.info;
          // this.itemDetail = data.records;
            console.log(`data.records`,data.records)
            for(let i = 0; i < item.CWS; i++){
                if(data.records[i]){
                    this.itemDetail[i] = data.records[i]
                }else {
                    this.itemDetail[i] = {
                        BEBH: 0,
                        DWMC: '',
                        XH: '',
                        XM: '空'
                    }
                }

            }
            // for(let i = 0; i < data.records.length; i++){
            //     this.itemDetail[i].BEBH = data.records[i].BEBH;
            //     this.itemDetail[i].DWMC = data.records[i].DWMC;
            //     this.itemDetail[i].XH = data.records[i].XH;
            //     this.itemDetail[i].XM = data.records[i].XM;
            // }
            console.log(`this.itemDetail`,this.itemDetail)
          // this.itemDetail.forEach(item=>{
          //   data.records.forEach(e=>{
          //     // if(item.BEBH == e.BEBH){
          //     //   item.DWMC = e.DWMC
          //     //   item.XH = e.XH
          //     //   item.XM = e.XM
          //     // }
          //       if(e && e.BEBH){
          //           item.BEBH = e.BEBH
          //           item.DWMC = e.DWMC
          //           item.XH = e.XH
          //           item.XM = e.XM
          //       }
          //   })
          // })
          this.$forceUpdate()
          this.$nextTick(()=>{
            this.rightShow = true
          })

        })
      }
    }
}
</script>

<style scoped lang="scss">
.carousel-wrapper{
  width: 100%;
  height: 100%;
  background-color: #eee;
  background-repeat: no-repeat;
  background-size: cover;
  margin-top: 40px;
}
.main-wrapper{
  width: 77%;
  height: 80vh;
  background-color: #fff;
  border-radius: 5px;
  padding:20px 0 20px 20px;
  float: left;
  overflow-y: auto;
}
.main-wrapper-r{
  width: 20%;
  //background-color: #fff;
  //border-radius: 5px;
  //padding:20px;
  //position: relative;
  float: right;
}
.main-wrapper-r-content{
  color: rgba(51, 51, 51, 0.88);
  width: 100%;
  //height: 100px;
  background-color: #fff;
  border-radius: 5px;
  padding:20px;
  margin-bottom: 20px;
  position: relative;
}
.wrapper-right{
  width: 100%;
  //height: 100%;
  //overflow: scroll;
  white-space: nowrap;
  //border: 1px solid #1e9fff;
  //overflow: hidden;
}
.wrapper_box{
  width: 100%;
  height: 100%;
  overflow: auto;
  margin-bottom: 5px;
  //border: 2px solid #ffd400;
  background-color: #EAF2F8;
  text-align: left;
  padding: 5px 20px 20px ;
  display: flex;
}
.wrapper_box_item{
  vertical-align: middle;
  //float: left;
  text-align: center;
  display: inline-block;
  margin-right: 5px;
  width: 80px !important;
  height: 60px;
  border: 3px solid #00a5ec;
  border-radius: 5px;
  position: relative;
}
.wrapper_box_br{
  width: 100%;
  height: 20px;
  margin-top: 90px;
  border: 3px solid #00ff00;
}
.content_box{
  text-align: center;
  height: 50px;
  width: 32%;
  border: 2px solid #00a5ec;
  border-radius: 3px;
  margin-right: 1%;
  margin-bottom: 1%;
  //float: left;
  display: inline-block;
}
::-webkit-scrollbar {
  width: 0px;
  height: 8px !important;
}

::-webkit-scrollbar-button {
  background-color: #EAF2F8;
  color: #00bfeb;
}

::-webkit-scrollbar-track-piece {
  //background: yellow;
}

::-webkit-scrollbar-thumb {
  background: #9c9c9c !important;
  border-radius: 4px;
}
</style>

