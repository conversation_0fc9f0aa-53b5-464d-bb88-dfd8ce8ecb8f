<template>
    <!-- <div class="wrap"> -->
    <div class="center desktop-center clearfix" style="margin-top: -10px;">
        <template v-for="Ditem in data">
            <div :key="Ditem.id" class="list-item" :class="{bl: Ditem.style=='半栏', tl: Ditem.style=='通栏'}">
                <!-- 轮播图 -->
                <template v-if="Ditem.model == 'lunbotu'">
                    <div class="banner layui-carousel" id="test">
                        <a-carousel autoplay height="240px">
                            <a v-for="item in obj.carouselData" :key="item.url" :href="item.detail">
                                <img class="bannerDiv" :src="item.url" :alt="item.title"/>
                            </a>
                        </a-carousel>
                    </div>

                </template>

                <!-- 新闻 -->
                <template v-if="Ditem.model == 'xinwen'">
                    <div class="newWrap">
                        <div class="small-newWrapTitle clearfix">
                            <p class="small-serviceTopTitle">{{Ditem.name}}</p>
                            <div class="small-serviceMore">
                                <i class="icon-font icon-ellipsis"></i>
                            </div>
                        </div>
                        <p class="serviceTopTitle">{{Ditem.name}}</p>
                        <a-tabs :default-active-key="1" @tabClick="newsTabs($event, Ditem)">
                            <a-tab-pane v-for="item in Ditem.tabs" :key="item.index" :tab="item.name">
                                <div class="loading" v-show="isPortalLoading.news">
                                    <img src="../../styles/assets/image/loadinggif.gif" width="50px"/>
                                </div>
                                <div class="newContent newContent1 newContentLeft" style="display: block;">
                                    <ul class="newUl">
                                        <li v-for="news in obj.news" :key="news.id">
                                            <div @click="showNewsDetail(news)">
                                                <a href="javascript:;">
                                                    <span class="newLiTitle">
                                                        <span v-if="news.orgName">[{{news.orgName}}]</span>
                                                        {{news.title}}
                                                    </span>
                                                    <span class="newLiTime">{{news.createDate}}</span>
                                                </a>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </a-tab-pane>
                        </a-tabs>
                        <div class="serviceMore">
                            <i class="icon-font icon-ellipsis"></i>
                        </div>
                    </div>
                </template>

                <!-- 消息 -->
                <template v-else-if="Ditem.model == 'xiaoxirenwu'">
                    <div class="newWrap">
                        <div class="small-newWrapTitle clearfix">
                            <p class="small-serviceTopTitle">{{Ditem.name}}</p>
                            <div class="small-serviceMore">
                                <i class="icon-font icon-ellipsis"></i>
                            </div>
                        </div>
                        <p class="serviceTopTitle">{{Ditem.name}}</p>
                        <a-tabs :default-active-key="1" @tabClick="messageTabs($event, Ditem)">
                            <a-tab-pane v-for="item in Ditem.tabs" :key="item.index" :tab="item.name">
                                <div class="loading" v-show="isPortalLoading.message">
                                    <img src="../../styles/assets/image/loadinggif.gif" width="50px"/>
                                </div>
                                <div class="newContent newContent1 newContentRight" style="display: block;">
                                    <ul class="newUl">
                                        <li v-for="msg in obj.msgData" :key="msg.id">
                                            <a :href="msg.detailLinks">
                                                <span class="newLiTitle">
                                                    <!-- <span v-if="msg.msgType == '申请'" class="shenqing">[{{msg.msgType}}]</span>
                          <span v-else-if="msg.msgType == '待办'" class="daiban">[{{msg.msgType}}]</span>
                          <span v-else-if="msg.msgType == '消息'" class="xiaoxi">[{{msg.msgType}}]</span>-->
                                                    [{{msg.type}}] {{msg.content}}
                                                </span>
                                                <span class="newLiTime">{{msg.createDate}}</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </a-tab-pane>
                        </a-tabs>
                        <div class="serviceMore">
                            <i class="icon-font icon-ellipsis"></i>
                        </div>
                    </div>
                </template>

                <!-- 我的数据 -->
                <template v-else-if="Ditem.model == 'wodeshuju'">
                    <div class="newWrap">
                        <div class="small-newWrapTitle clearfix">
                            <p class="small-serviceTopTitle">{{Ditem.name}}</p>
                            <div class="small-serviceMore">
                                <i class="icon-font icon-ellipsis"></i>
                            </div>
                        </div>
                        <p class="serviceTopTitle">{{Ditem.name}}</p>
                        <a-tabs :default-active-key="1" @tabClick="userDataTabs($event, Ditem)">
                            <a-tab-pane v-for="item in Ditem.tabs" :key="item.index" :tab="item.name">
                                <div class="loading" v-show="isPortalLoading.userData">
                                    <img src="../../styles/assets/image/loadinggif.gif" width="50px"/>
                                </div>
                                <div class="newContent" style="display: block;padding: 5px 10px;">
                                    <div class="mineDesktopDataWrap" v-for="news in obj.userData" :key="news.id">
                                        <div class="mineDesktopData">
                                            <!-- <img class="mineDesktopDataImg" :src="news.icon" :alt="news.title" /> -->
                                            <p class="mineDesktopDataNumber">{{news.content}}</p>
                                            <div class="mineDesktopDataTitle">{{news.title}}</div>
                                        </div>
                                    </div>
                                </div>
                            </a-tab-pane>
                        </a-tabs>
                        <div class="serviceMore">
                            <i class="icon-font icon-ellipsis"></i>
                        </div>
                    </div>
                </template>

                <!-- 集成系统 -->
                <template v-else-if="Ditem.model == 'jichengxitong'">
                    <div class="newWrap">
                        <div class="small-newWrapTitle clearfix">
                            <p class="small-serviceTopTitle">{{Ditem.name}}</p>
                            <div class="small-serviceMore">
                                <i class="icon-font icon-ellipsis"></i>
                            </div>
                        </div>
                        <div class="serviceTop">
                            <p class="serviceTopTitle">{{Ditem.name}}</p>
                            <span class="serviceMore">
                                <i class="icon-font icon-ellipsis"></i>
                            </span>
                        </div>

                        <div class="newContent" style="display: block;">
                            <div class="systemHover" v-for="(news, index) in obj.systemData" :key="index">
                                <div class="system" @click="toServiceLink(news.id, 'sys')">{{news.clientName}}</div>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- 我的服务 -->
                <template v-else-if="Ditem.model == 'wodefuwu'">
                    <div class="mineServiceWrap" style="position: relative;" :key="Ditem.id">
                        <div class="small-newWrapTitle clearfix">
                            <p class="small-serviceTopTitle">{{Ditem.name}}</p>
                            <div class="small-serviceMore">
                                <i class="icon-font icon-ellipsis"></i>
                            </div>
                        </div>
                        <p class="serviceTopTitle">{{Ditem.name}}</p>

                        <a-tabs :default-active-key="1" @tabClick="serviceTabs($event, Ditem)">
                            <a-tab-pane :key="item.index" :tab="item.name" v-for="item in Ditem.tabs">
                                <div class="loading" v-show="isPortalLoading.service">
                                    <img src="../../styles/assets/image/loadinggif.gif" width="50px"/>
                                </div>
                                <div class="serviceContent topContent" style="display: block;">
                                    <div class="serviceNone" v-if="isEmpty.service">
                                        <img src="../../styles/assets/image/zanwu.png" alt/>
                                        <br/>暂无服务
                                    </div>
                                    <div class="service" v-for="news in obj.serviceData" :key="news.icon">
                                        <div class="serviceDiv" @mouseenter="onMousteIn(news.id)"
                                             @click="toServiceLink(news.id, 'service')">
                                            <img v-if="news.icon" :src="'/file/view/'+news.icon"/>
                                            <span>{{news.name}}</span>
                                            <transition name="el-zoom-in-center">
                                                <div class="serviceHover" @mouseleave="onMousteOut()"
                                                     v-show="show2&&news.id==current">
                                                    <p class="hoverTitle1">{{news.name}}</p>
                                                    <p class="hoverTitle" :class="{'hoverTitleClick': news.collect}"
                                                       @click.stop="collectHandle(item.index,Ditem, news.id)"
                                                       title="点击加入收藏"></p>
                                                    <div class="hoverWrap">
                                                        <img v-if="news.icon" :src="'/file/view/'+news.icon" :alt="news.title"/>
                                                        <p>收藏数 &nbsp;&nbsp;{{news.collectNumber}}</p>
                                                        <p>访问数 &nbsp;&nbsp;{{news.visits}}</p>
                                                    </div>
                                                </div>
                                            </transition>
                                        </div>
                                    </div>
                                </div>
                            </a-tab-pane>
                        </a-tabs>
                        <div class="serviceMore">
                            <i class="icon-font icon-ellipsis"></i>
                        </div>
                    </div>
                </template>
            </div>
        </template>
        <div class="footer1">
            <p>©2020 版权所有 智慧大学 技术支持：北京三易拓科技有限公司</p>
        </div>

        <a-modal style="height: 86vh" :visible="isNewsShow" :maskClosable="false" :footer="null" width="1100px"
                 @cancel="isNewsShow = false">
            <div class="content" :style="{overflow: 'auto', margin: '15px 0 0', padding: '0px'}">
                <h2 :style="{color: '#666', 'margin-bottom': '20px'}">{{curNews.title}}</h2>
                <p :style="{'font-size': '12px', color: '#999', 'margin-bottom': '20px'}">
                    发布时间：{{curNews.createDate}}</p>
                <p v-html="curNews.content" :style="{height: '50vh'}"></p>
            </div>
        </a-modal>
    </div>
    <!-- </div> -->
</template>

<script>
    // GetDesktopById,
    import {isCollect} from "@/api/home";
    import {mapGetters} from "vuex";
    import {ToService} from "@/api/portal";
    import {getToken} from "@/util/auth";

    export default {
        props: {
            data: {
                type: Array,
                default() {
                    return [];
                }
            },
            obj: {
                type: Object,
                default() {
                    return {};
                }
            }
        },
        data() {
            return {
                activeName: "second",
                activeName1: "two",
                collectFlag: true,
                show2: false,
                current: "",
                isNewsShow: false,
                curNews: {}
            };
        },
        methods: {
            onMousteIn: function (index) {
                this.show2 = true; //鼠标移入显示
                this.current = index;
            },
            onMousteOut: function () {
                this.show2 = false; //鼠标移出隐藏
                this.current = "";
            },
            toServiceLink(id, type) {
                if (!getToken()) {
                    this.$message.warning("请登录系统");
                    return;
                }

                let title = "";
                let context = {
                    id: id
                };
                if (type == "sys") {
                    title = "系统";
                    context.type = "sys";
                } else if (type == "service") {
                    title = "服务";
                }

                this.$confirm("即将跳转外部" + title + "，是否继续？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消"
                }).then(() => {
                    ToService(context).then(res => {
                        if (res.data.code == "00000") {
                            window.open(res.data.info);
                        } else {
                            this.$message.warning(res.data.info);
                        }
                    });
                }).catch(() => {
                });
            },
            // 收藏
            collectHandle(index, item, id) {
                if (this.collectFlag) {
                    this.collectFlag = false;
                    isCollect(id).then(res => {
                        if (res.data.code == "00000") {
                            this.serviceTabs(index, item);
                            this.$message.success(res.data.info);
                        }
                    }).finally(() => {
                        this.collectFlag = true;
                    });
                }
            },

            // 新闻详情
            showNewsDetail(item) {
                this.curNews = item;
                this.isNewsShow = true;
            },
            newsTabs(key, item) {
                this.$store.commit("SET_LOADING", {
                    name: "news",
                    flag: true
                });
                this.$emit("newsTabs", key, item);
            },
            messageTabs(key, item) {
                this.$store.commit("SET_LOADING", {
                    name: "message",
                    flag: true
                });
                this.$emit("messageTabs", key, item);
            },
            userDataTabs(key, item) {
                this.$store.commit("SET_LOADING", {
                    name: "userData",
                    flag: true
                });
                this.$emit("userDataTabs", key, item);
            },
            serviceTabs(key, item) {
                this.$store.commit("SET_LOADING", {
                    name: "service",
                    flag: true
                });
                this.$emit("serviceTabs", key, item);
            }
        },
        computed: {
            ...mapGetters(["isPortalLoading", "isEmpty"])
        }
    };
</script>

<style scoped>
    @import url(../../styles/assets/css/mine.css);

    .bl {
        width: calc(50% - 20px);
    }

    .tl {
        width: calc(100% - 20px);
    }

    .list-item {
        margin: 10px;
        /* height: 200px; */
        float: left;
        /* border: 1px solid #ccc; */
        box-sizing: border-box;
    }

    .clearfix:before,
    .clearfix:after {
        content: "";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }

    .clearfix {
        *zoom: 1;
    }

    ::-webkit-scrollbar-thumb {
        background-color: #ccc !important;
    }

    .newWrap {
        position: relative;
        width: 100%;
    }

    .center {
        padding-bottom: 0px;
    }

    .newUl li:hover {
        background: rgba(0, 0, 0, 0.07);
    }

    .bl .system {
        /* background: #f5f6f8; */
        color: #fff;
    }

    .bl .systemHover:nth-of-type(3n + 3) .system {
        margin-right: 0;
    }

    .systemHover .system {
        transition: all 0.35s;
        position: relative;
        z-index: 1;
    }

    .system:hover {
        color: #fff;
        background: none;
    }

    .system:before {
        visibility: hidden;
        content: "";
        position: absolute;
        left: 50%;
        top: 0;
        width: 0;
        height: 100%;
        background: #305395;
        z-index: -1;
        transform: skew(45deg, 0);
        transition: all 0.35s;
    }

    .system:hover:before {
        visibility: visible;
        width: 150%;
        left: -25%;
    }

    .systemHover {
        text-align: center;
    }

    .tl .systemHover .system {
        width: 15%;
        height: 44px;
        border: none;
        /* background: #4F97D4; */
        text-align: center;
        color: #fff;
        line-height: 44px;
        text-decoration: none;
        margin-right: 2%;
    }

    .tl .systemHover:nth-of-type(6n + 6) .system {
        margin-right: 0;
    }

    .systemHover .system:hover {
        border-color: #305395;
        background: none;
    }

    .footer1 {
        position: absolute;
        left: 0;
        bottom: -20px;
    }

    .serviceNone {
        text-align: center;
        color: #999;
    }

    .serviceNone img {
        opacity: 0.3;
        width: 80px;
        margin-top: 10px;
        margin-bottom: 5px;
    }

    .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .small-newWrapTitle {
        display: none;
    }


    @media screen and (max-width: 1366px) {
        .center {
            width: 100%;
            float: none;
            left: 0;
            position: relative;
            margin-left: 0;
        }

        .serviceContent {
            min-height: 190px;
            height: auto;
        }

        .newContent {
            min-height: 190px;
            height: auto;
        }


    }

    @media screen and (max-width: 1100px) {
        .bl {
            width: calc(100% - 20px);
        }

        .bl .systemHover .system {
            width: 147px;
            margin-right: 2%;
        }

        .bl .systemHover:nth-of-type(3n + 3) .system {
            margin-right: 2%;
        }

        .tl .systemHover .system {
            width: 147px;
            margin-right: 2%;
        }

        .tl .systemHover:nth-of-type(6n + 6) .system {
            margin-right: 2%;
        }
    }

    @media screen and (max-width: 850px) {

        .small-newWrapTitle {
            display: block;
        }

        .serviceTopTitle, .serviceMore, .serviceTop {
            display: none;
        }

        .service {
            margin-right: 0;
        }

        .serviceHover {
            display: none !important;
        }
    }
</style>
