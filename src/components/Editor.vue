<template>
  <div class="app-container">
    <div ref="editor" class="text" />
  </div>
</template>

<script>
import { upload } from "@/util/upload";
import E from "wangeditor";
export default {
  name: "Editor",
  props: {
    value: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      editorContent: ""
    };
  },
  mounted() {
    const _this = this;
    var editor = new E(this.$refs.editor);
    // 自定义菜单配置
    editor.customConfig.zIndex = 10;

    // editor.customConfig.uploadImgServer = "/file/upload";
    // 文件上传
    editor.customConfig.customUploadImg = function(files, insert) {
      // files 是 input 中选中的文件列表
      // insert 是获取图片 url 后，插入到编辑器的方法
      files.forEach(image => {
        upload("/file/upload", image).then(res => {
          insert(res.data.info.url);
        });
      });
    };
    editor.customConfig.onchange = html => {
      this.editorContent = html;
      _this.$emit("input", html);
    };
    editor.create();
    // 初始化数据
    editor.txt.html(_this.value);
  }
};
</script>

<style scoped>
.text {
  text-align: left;
}

::v-deep .w-e-text-container {
  height: 420px !important;
}

.left {
  position: relative;
  top: auto;
  width: 300px;
}
</style>
