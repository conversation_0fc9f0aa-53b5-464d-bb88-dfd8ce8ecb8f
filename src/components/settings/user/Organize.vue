<template>
  <div>
    <avue-crud :data="orgData" :option="option" @row-del="deleteHandle"></avue-crud>
  </div>
</template>

<script>
const DIC = [
  {
    label: "一级1",
    value: 1,
    children: [
      {
        label: "子类",
        value: 100
      }
    ]
  },
  {
    label: "一级2",
    value: 2
  },
  {
    label: "一级3",
    value: 3
  },
  {
    label: "一级4",
    value: 4
  },
  {
    label: "一级5",
    value: 5
  },
  {
    label: "一级6",
    value: 6
  },
  {
    label: "一级7",
    value: 7
  },
  {
    label: "一级8",
    value: 8
  },
  {
    label: "一级9",
    value: 9
  },
  {
    label: "一级10",
    value: 10
  }
];

export default {
  props: {
    orgData: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      obj: {},
      // data: [],
      option: {
        title: "表格的标题",
        page: false,
        align: "center",
        menuAlign: "center",
        editBtn: false,
        span: 24,
        column: [
          {
            label: "所属机构",
            prop: "tree1",
            type: "tree",
            dicData: DIC
          }
        ]
      }
    };
  },
  // watch: {
  //   orgData(val) {
  //     this.data = val;
  //   }
  // },
  methods: {
    deleteHandle(row, index) {
      console.log(",,", index);

      this.$emit("deleteOrgItem", index);
    }
  }
};
</script>

<style>
</style>