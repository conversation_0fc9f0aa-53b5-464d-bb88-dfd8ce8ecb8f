<template>
  <el-transfer v-model="value" :data="data"></el-transfer>
</template>

<script>
export default {
  props: {
    roleList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      data: [],
      value: []
    };
  },
  watch: {
    roleList(val) {
      console.log("aas");

      val.forEach(item => {
        this.data.push({
          key: item.id,
          label: item.rolename
        });
      });
    }
  }
};
</script>