<template>
  <basic-container>
    <div class="custom-tree-container">
      <el-container>
        <el-aside width="500px">
          <div class="tree">
            <div class="util">
              <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
              <button class="add-btn" @click.stop="append($event, null)">+ 添加</button>
            </div>
            <el-tree
              style="width: 500px"
              :data="data"
              node-key="id"
              default-expand-all
              :expand-on-click-node="false"
              @node-click="treeHandle"
              :filter-node-method="filterNode"
              :render-content="renderContent"
              ref="tree"
            ></el-tree>
          </div>
        </el-aside>
        <el-main>
          <h4>
            <b>{{fromTitle}}</b>
          </h4>
          <avue-form v-show="isFormShow" :option="option" v-model="form" @submit="onSubmit"></avue-form>
        </el-main>
      </el-container>

      <!-- <div class="form" v-show="isFormShow">
      <el-form ref="form" label-position="left" :inline="true" :model="form" label-width="80px">
        <h3>{{fromTitle}}</h3>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单名称">
              <el-input v-model="form.label"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="icon">
              <el-input v-model="form.icon"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="path">
          <el-input v-model="form.path"></el-input>
        </el-form-item>
        <el-form-item label="component">
          <el-input v-model="form.component"></el-input>
        </el-form-item>
        <el-form-item label="type">
          <el-input v-model="form.type"></el-input>
        </el-form-item>
        <el-form-item label="是否按钮">
          <el-input v-model="form.isBtn"></el-input>
        </el-form-item>
        <el-form-item label="是否显示">
          <el-input v-model="form.status"></el-input>
        </el-form-item>
        <br />
        <el-form-item>
          <el-button style="margin-left: 80px" type="primary" @click="onSubmit">{{fromTitle}}</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
      </div>-->
    </div>
  </basic-container>
</template>

<script>
import { getEMenu } from "@/api/user";
import { editMenu, removeMenu } from "@/api/settings";

export default {
  data() {
    return {
      filterText: "",
      data: [],
      form: {
        label: "",
        icon: "",
        path: "",
        component: "",
        isBtn: "",
        type: "1",
        status: ""
        // region: "",
        // date1: "",
        // date2: "",
        // delivery: false,
        // type: [],
        // resource: "",
        // desc: ""
      },

      fromTitle: "",
      isFormShow: false,

      // obj: {},
      option: {
        // labelWidth: 80,
        dialogClickModal: false,
        column: [
          {
            label: "菜单名称",
            prop: "label",
            rules: [
              { required: true, message: "菜单名称不能为空", trigger: "blur" }
            ]
          },
          {
            label: "图 标",
            prop: "icon"
          },
          {
            label: "访问路径",
            prop: "path",
            rules: [
              { required: true, message: "访问路径不能为空", trigger: "blur" }
            ]
          },
          {
            label: "组件路径",
            prop: "component"
            // rules: [{ required: true, message: "组件路径不能为空" }]
          },
          // {
          //   label: "类型",
          //   prop: "type",
          //   type: "select",
          //   dicData: [

          //   ],
          //   rules: [{ required: true, message: "type不能为空" }]
          // },
          {
            label: "是否按钮",
            prop: "isBtn",
            type: "radio",
            dicData: [
              {
                label: "否",
                value: "否"
              },
              {
                label: "是",
                value: "是"
              }
            ]
            // rules: [{ required: true, message: "是否按钮不能为空" }]
          },
          {
            label: "是否显示",
            prop: "status",
            type: "radio",
            dicData: [
              {
                label: "否",
                value: "否"
              },
              {
                label: "是",
                value: "是"
              }
            ]
            // rules: [{ required: true, message: "是否显示不能为空" }]
          },
          {
            label: "接口地址",
            prop: "url",
            span: 24,
            type: "textarea"
            // rules: [{ required: true, message: "是否显示不能为空" }]
          }
        ]
      }
    };
  },
  created() {
    this.getMenuInfo();
  },
  inject: ["reload"],
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    getMenuInfo() {
      getEMenu().then(res => {
        this.data = res.data.info;
      });
    },

    append(e, data) {
      e.stopPropagation();
      this.isFormShow = true;

      if (this.form.id) {
        delete this.form.id;
      }

      // 添加子菜单
      if (data != null) {
        this.fromTitle = "为【" + data.label + "】添加子级菜单";
        this.form = {
          label: "",
          icon: "",
          path: "",
          parentId: data.id,
          component: "",
          isBtn: "否",
          type: "1",
          status: "是"
        };
      } else {
        this.fromTitle = "添加一级菜单";
        // 添加一级菜单
        this.form = {
          label: "",
          icon: "",
          path: "",
          parentId: null,
          component: "",
          isBtn: "否",
          type: "1",
          status: "是"
        };
      }
    },

    remove(e, node, data) {
      e.stopPropagation();

      if (data.children.length != 0) {
        this.$message({
          type: "warning",
          message: "请确认并先删除" + data.label + "子级菜单"
        });
        return;
      }

      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          removeMenu(data.id).then(res => {
            if (res.data.code == "00000") {
              const parent = node.parent;
              const children = parent.data.children || parent.data;
              const index = children.findIndex(d => d.id === data.id);
              children.splice(index, 1);
              this.$message({
                type: "success",
                message: "删除成功!"
              });
              this.reload();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {});
    },

    renderContent(h, { node, data }) {
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span>
            <el-button
              size="mini"
              type="text"
              on-click={e => this.append(e, data)}
            >
              添加
            </el-button>
            <el-button
              size="mini"
              type="text"
              on-click={e => this.remove(e, node, data)}
            >
              删除
            </el-button>
          </span>
        </span>
      );
    },

    // 点击树 修改
    treeHandle(el) {
      this.fromTitle = "修改【" + el.label + "】";
      this.isFormShow = true;
      this.form = {
        id: el.id,
        label: el.label,
        icon: el.icon,
        path: el.path,
        component: el.component,
        type: el.type,
        isBtn: el.isBtn,
        status: el.status,
        parentId: el.parentId
      };
    },

    // form 提交
    onSubmit(form, done) {
      editMenu(this.form).then(res => {
        if (res.data.code == "00000") {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.form = { isBtn: "", type: "1", status: "" };
          done();
          this.reload();
        } else {
          this.$message.error("操作失败");
          this.reload();
        }
      });
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    }
  }
};
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.custom-tree-container {
  /* margin: 10px;
  margin-top: 0; */
  display: flex;
  min-width: 260px;
  /* background-color: #fff; */
}

.util {
  margin: 10px 0 10px 10px;
  display: flex;
}

.el-input__inner {
  border-radius: 4px 0 0 4px;
}

.util .add-btn {
  width: 100px;
  height: 40px;
  border: none;
  font-size: 14px;
  color: #409eff;
  border: 1px solid #dcdfe6;
  border-left: 0;
  background-color: transparent;
  border-radius: 0 4px 4px 0;
}

.util .add-btn:hover {
  background-color: #409eff;
  color: #fff;
  border: 1px solid #409eff;
}

.tree {
  position: relative;
  padding-bottom: 20px;
}

.form {
  margin-left: 20px;
  padding: 10px;
  flex: 1;
}

.form h3 {
  margin: 0 0 10px;
  text-indent: 80px;
}

.custom-tree-container .el-main {
  margin: 0 20px;
  border-left: 1px solid #eee;
}

/* .el-form-item__label {
  text-align: left;
} */

/* .el-main {
  position: fixed;
  left: 800px;
} */

.el-main h4 {
  font-size: 15px;
  margin-top: 20px;
  margin-left: 25px;
  margin-bottom: 25px;
}

.el-aside {
  position: relative;
  height: 100%;
  /* overflow: hidden;
  overflow-y: scroll; */
}
</style>