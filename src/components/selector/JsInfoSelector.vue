<template>
  <div class="selector-layout">
    <el-row type="flex" :gutter="20">
      <!--部门数据-->
      <el-col :span="8" v-if="isShowOrg">
        <el-card shadow="never" style="height: 100%">
          <div slot="header">
            <span>组织机构</span>
          </div>
          <el-input placeholder="请输入内容" size="mini" v-model="inputsearch">
            <template slot="append"><el-button slot="append" icon="el-icon-search" @click="searchdept"></el-button></template>
          </el-input>
          <div class="head-container" style="height: 450px;overflow-y: auto;">
            <el-tree :data="deptOptions" :props="deptProps" :expand-on-click-node="false"
              :filter-node-method="filterNode" ref="tree" @node-click="handleNodeClick" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="16" class="cols">
        <el-form :inline="true" ref="form" v-model="form" label-width="80px" >
<!--          <el-form-item label="更多人员" label-width="150px" v-if="moreuser">-->
<!--            <el-switch-->
<!--                v-model="more"-->
<!--                @change="changeMore"-->
<!--            >-->
<!--            </el-switch>-->
<!--          </el-form-item>-->
          <el-form-item label="工号">
            <el-input size="mini" v-model="form.zjh"></el-input>
          </el-form-item>
          <el-form-item label="姓名">
            <el-input size="mini" v-model="form.xm"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="searchUser">搜索</el-button>
          </el-form-item>
        </el-form>
        <div>
          <el-table ref="multipleTable" :data="userTableList" size="medium" border height="430px"
                    highlight-current-row
                    @current-change="handleCurrentChange"
                    @select="handleSelection"
                    @select-all="handleSelectionChange">
            <el-table-column v-if="checkShow" type="selection"  align="center" />
            <el-table-column label="工号" align="center" prop="zjh" />
            <el-table-column label="姓名" align="center" prop="xm" />
            <el-table-column label="性别" align="center" prop="xb" />
            <el-table-column label="所在单位" align="center" prop="ejdw" />
          </el-table>
        </div>
        <div v-show="userTotal != 0">
          <el-pagination
              layout="prev, pager, next"
              @current-change="getUserList"
              :current-page="queryParams.page"
              :page-size="queryParams.pageSize"
              :total="userTotal">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <div style="width:80%;float:left;padding:0px 20px;height:60px;overflow-y: auto;">
        <span>已选人员：</span>
        <el-tag class="selectedUser" closable size="small" @close="removeUser(item,index)" v-for="(item,index) in this.userlist">{{item.xm}}</el-tag>
      </div>
      <div style="width:20%;float:right;text-align: right;padding-top:20px;padding-right:20px;">
        <el-button size="small" class="el-button" type="primary" @click="handleTaskUserComplete">确 定</el-button>
        <el-button size="small" @click="close">取 消</el-button>
      </div>
    </div>
  </div>

</template>

<script>
import {queryJsInfoPage} from "@/api/jsInfoSpecial";
import {GetLazyOrg, GetOrg} from "@/api/basetable";


export default {
    name: "JsInfoSelector",
    props: {
      // userOpen: { //是否表单预览状态
      //   type: Boolean,
      //   default: false
      // }
      selectedUserParam:{
        type: Object,
        default: {orgname: ""}
      },

    },
    data() {
      return {
        more: false,
        isShowOrg: true,
        userOpen: false,
        moreuser: false,
        checkShow: true,
        userTableList: [],
        deptOptions: [],
        deptProps: {
          children: "children",
          label: "label"
        },
        form:{},
        nodeid: '',
        userlist: [],
        inputsearch: this.selectedUserParam.orgname,
        selectedUserDate: [],
        userTotal: 0,
        queryParams: {
          page: 1,
          pageSize: 10,
          queryParam: {}
        },
        param:{orgname: this.selectedUserParam.orgname}
      }
    },
    created() {
      // console.log(`selectedUserParam`, this.selectedUserParam);
      if(this.selectedUserParam.onlyone){
        this.checkShow = false;
      }
      this.moreuser = this.selectedUserParam.moreuser;
      if ((this.selectedUserParam.zjh && this.selectedUserParam.zjh !== '') ||
          (this.selectedUserParam.ejdw && this.selectedUserParam.ejdw !== '')
      ) {
        this.queryParams.queryParam.zjh = this.selectedUserParam.zjh;
        this.queryParams.queryParam.organizationnames = this.selectedUserParam.ejdw;
        this.getUserList();
        this.isShowOrg = false;
      } else {
        this.getDeptOptions();
      }
    },
    methods: {
      changeMore(val){
        if (val) {
          this.isShowOrg = true;
          this.getDeptOptions();
          this.queryParams.queryParam.zjh = '';
          this.queryParams.queryParam.organizationnames = '';
        } else {
          this.isShowOrg = false;
          this.queryParams.queryParam.zjh = this.selectedUserParam.zjh;
          this.queryParams.queryParam.organizationnames = this.selectedUserParam.ejdw;
        }
        this.getUserList();
      },
      // 筛选节点
      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
      searchdept(){
        if (this.inputsearch !== '') {
          this.param = {
            parent: `search`,
            orgname: this.inputsearch
          };
          GetLazyOrg(this.param).then(response => {
            this.deptOptions = response.data.info;
            this.deptOptions.unshift({
              categoryId: null,
              children: [],
              code: "0",
              displayorder: 0,
              hasChildren: false,
              id: "0",
              label: "全部",
              orgHeadHumaCode: "0",
              orgcode: null,
              orgshortname: null,
              parent: null,
              valid: "1"
            })
          })
        } else {
          this.param = {}
          GetOrg(this.param).then(response => {
            this.deptOptions = response.data.info;
            this.deptOptions.unshift({
              categoryId: null,
              children: [],
              code: "0",
              displayorder: 0,
              hasChildren: false,
              id: "0",
              label: "全部",
              orgHeadHumaCode: "0",
              orgcode: null,
              orgshortname: null,
              parent: null,
              valid: "1"
            })
          })
        }
      },
      // 节点单击事件
      handleNodeClick(data) {
        if(data.id == "0"){
          this.queryParams.queryParam = {}
        }else {
          this.queryParams.queryParam.ejdw = data.orgname;
        }
        this.getUserList();
      },
      /**
       * 查询部门下拉树结构
       */
      getDeptOptions() {
        return new Promise((resolve, reject) => {
          if (!this.deptOptions || this.deptOptions.length <= 0) {
            let queryParam = this.param;
            // console.log(`queryParam`, queryParam);
            GetOrg(queryParam).then(response => {
              this.deptOptions = response.data.info;
              this.deptOptions.unshift({
                categoryId: null,
                children: [],
                code: "0",
                displayorder: 0,
                hasChildren: false,
                id: "0",
                label: "全部",
                orgHeadHumaCode: "0",
                orgcode: null,
                orgshortname: null,
                parent: null,
                valid: "1"
              })
              // console.log(`deptOptions`,this.deptOptions)
              resolve()
            })
          } else {
            reject()
          }
        });
      },
      handleCurrentChange(val){
        if(this.selectedUserParam.onlyone){
          this.userlist = [];
          this.userlist.push(val);
        }
      },
      handleSelection(selection,row){
        let ispush = false
        let userpush = true
        selection.forEach(item=>{
          if(row.id == item.id){
            ispush = true
          }
        })
        if(ispush == true){
          this.userlist.forEach(item=>{
            if(row.id == item.id){
              userpush = false
            }
          })
          if(userpush == true ){
            this.userlist.push(row)
          }
        }else {
          this.userlist.forEach((item,index)=>{
            if(row.id == item.id){
              this.userlist.splice(index,1)
            }
          })
        }
        // this.userlist.push(row)
      },
      handleSelectionChange(selection) {
        selection.forEach((item,index)=>{
          this.userlist.forEach(row=>{
            if(item.id == row.id){
              selection.splice(index,1)
            }
          })
        })
        this.userlist.push(...selection)
        // this.selectedUserDate = selection;
      },
      searchUser(){
        this.queryParams.queryParam.zjh = this.form.zjh;
        this.queryParams.queryParam.xm = this.form.xm;
        this.getUserList();
      },
      getUserList(val) {
        if(val){
          this.queryParams.page = val
        }
        queryJsInfoPage(this.queryParams).then(response => {
          this.userTableList = response.data.info.records;
          this.userTotal = response.data.info.total;
          this.userlist.forEach((item)=>{
            this.$refs.multipleTable.toggleRowSelection(item);
          })
          // console.log(`userlist`,this.userlist)
        });
      },
      removeUser(row,val){
        this.userlist.splice(val,1);
        this.$refs.multipleTable.toggleRowSelection(row,false);
      },
      handleTaskUserComplete() {
        if (!this.userlist || this.userlist.length <= 0) {
          this.$message.error('请先选择数据');
          return;
        }
        this.$emit('selectedUser', {
          selectedUserDate: this.userlist,
          userOpen: false,
        })
        this.userOpen = false;
      },
      close(){
        this.$emit('close', {
          userOpen: false,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
 ::v-deep .el-dialog__body{
    padding-bottom: 0px !important;
  }
 ::v-deep .el-form-item{
    width: 29% !important;
    margin-top: 15px !important;
    text-align: end !important;
  }
 ::v-deep .el-form-item__label{
    width: 30% !important;
    font-size: 14px !important;
    padding: 0 12px 0 0 !important;
    line-height: 28px !important;
  }
 ::v-deep .el-form-item__content{
    width: 70% !important;
  }
  .dialog-footer {
    position: absolute;
    left:0px;
    bottom: -59px;
    z-index: 2;
    width:100%;
    height:60px;
    background-color: #ffffff;
  }
  .dialog-footer p{
    text-align: right;
    padding: 12px 20px;
  }
  .cols{
    border: 1px solid #EBEEF5;
    border-radius: 5px;
  }
  .selectedUser {
    margin-top: 5px;
    margin-left: 5px;
  }

  .selector-layout {

  }
  .el-card{
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }
</style>
