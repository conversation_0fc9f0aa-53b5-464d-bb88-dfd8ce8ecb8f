<template>
  <div style="display: flex;">
    <template v-if="fieldType == 'select'">
      <avue-form v-model="form" :option="option"></avue-form>
    </template>
    <template v-else>
      <el-input style="width: 80%;display: inline-block;" v-model="fieldValue" placeholder="请输入内容"></el-input>
    </template>
    <el-button type="text" @click="bcxx()">保存</el-button>
  </div>
</template>

<script>
export default {
  name: "fieldtype",
  props:{
    fieldType: String,
    fieldValue: String,
    field: Object,
  },
  data(){
    return{
      form:{
      },
      option: {
        labelWidth: 0,
        menuBtn: false,
        size: 'mini',
        column: [{
          prop: 'fieldValue',
          span: 24,
        }]
      }
    }
  },
  created() {
    this.form.fieldValue = this.fieldValue;
    this.option.column[0].type = this.field.fieldType;
    if(this.field.fieldData.includes("/")){
      this.option.column[0].dicUrl = this.field.fieldData;
    }
    if(this.field.fieldAttribute){
      let obj = JSON.parse(this.field.fieldAttribute);
      this.option.column[0] = {...this.option.column[0],...obj}
    }

  },
  methods:{
    bcxx(){
      this.$emit('fieldbcxx', {
        field: this.field,
        fieldValue: this.form.fieldValue
      })
    },
  },
}
</script>

<style scoped>
::v-deep .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
  margin-bottom: 0;
}
::v-deep .el-col{
  margin-bottom: 0;
}
</style>