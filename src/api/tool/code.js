import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const getList = (data) => request({
  url: baseUrl + '/syt/code/queryPage',
  method: 'post',
  data: {
    ...data
  }
});

export const build = (ids) => {
  return request({
    url: baseUrl+'/syt/code/gen-code',
    method: 'post',
    params: {
      ids,
      system: 'saber'
    }
  })
}

export const remove = (data) => request({
  url: baseUrl + '/syt/code/delete',
  method: 'post',
  data: {
    ...data
  }
});

export const edit = (row) => {
  return request({
    url: '/syt/code/edit',
    method: 'post',
    data: row
  })
}

export const copy = (id) => {
  return request({
    url: baseUrl+'/syt/code/copy',
    method: 'post',
    params: {
      id,
    }
  })
}

export const getCode = (id) => {
  return request({
    url: baseUrl+'/syt/code/detail',
    method: 'get',
    params: {
      id,
    }
  })
}
