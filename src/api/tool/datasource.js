import request from '@/router/axios';
import {baseUrl} from "@/config/env";


export const getList = (data) => request({
  url: baseUrl + '/syt/datasource/queryPage',
  method: 'post',
  data: {
    ...data
  }
});

export const getDetail = (id) => {
  return request({
    url: baseUrl +'/syt/datasource/detail',
    method: 'post',
    data: {
      ...data
    }
  })
}

export const remove = (data) => request({
  url: baseUrl + '/syt/datasource/delete',
  method: 'post',
  data: {
    ...data
  }
});

export const edit = (data) => request({
  url: baseUrl + '/syt/datasource/edit',
  method: 'post',
  data: {
    ...data
  }
});


