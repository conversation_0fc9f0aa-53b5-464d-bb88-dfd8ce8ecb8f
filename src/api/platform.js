import request from '@/router/axios';
import {baseUrl} from '@/config/env';


// 新闻管理
export const EditNews = (data) => request({
    url: baseUrl + '/sytNews/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const getNewsQuery = (data) => request({
  url: baseUrl + '/sytNews/queryPage',
  method: 'post',
  data: {
      ...data
  }
});

export const DelNews = (data) => request({
    url: baseUrl + '/sytNews/delete',
    method: 'post',
    data: {
        id: data
    }
  });


// 桌面管理 - list

export const GetDesktop = () => request({
  url: baseUrl + '/sytDesktop/list',
  method: 'post',
});


// 桌面板块
export const GetDesktopBlock = (desktopId) => request({
  url: baseUrl + '/sytDesktopBlock/list',
  method: 'post',
  data: {
    desktopId
  }
});

   // 数据分类 - list  
   export const GetDataCategory = (data) => request({
    url: baseUrl + '/sytDataCategory/list',
    method: 'post',
    data
  });



  // 我的数据
export const EditMyData = (data) => request({
  url: baseUrl + '/sytUserData/edit',
  method: 'post',
  data: {
      ...data
  }
});

export const getMyData = (data) => request({
url: baseUrl + '/sytUserData/queryPage',
method: 'post',
data: {
    ...data
}
});

export const DelMyData = (data) => request({
  url: baseUrl + '/sytUserData/delete',
  method: 'post',
  data: {
      id: data
  }
});
  