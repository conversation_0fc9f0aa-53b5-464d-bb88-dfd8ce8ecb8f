import request from '@/router/axios';
import {baseUrl} from '@/config/env';

// 分页查询数据
export const queryPage = (data) => request({
    url: baseUrl + '/stJsInfoSpecial/queryPage',
    method: 'post',
    data: {
        ...data
    }
});
//  编辑
export const editInfo = (data) => request({
    url: baseUrl + '/stJsInfoSpecial/edit',
    method: 'post',
    data: {
        ...data
    }
});

// 删除
export const deleteInfo = (data) => request({
    url: baseUrl + '/stJsInfoSpecial/delete',
    method: 'post',
    data: {
        ...data
    }
});
//  批量增加
export const addBatchIds = (data) => request({
    url: baseUrl + '/stJsInfoSpecial/addBatchIds',
    method: 'post',
    data: {
        ...data
    }
});
// 导出数据
export function exportData(data) {
    return new Promise((resolve) =>{
        // eslint-disable-next-line no-undef
        axios({
            method:'post',
            url: baseUrl + '/stJsInfoSpecial/export',
            data:data,
            responseType:'blob'
        }).then(res =>{
            resolve(res.data);
        }).catch(err =>{
            resolve('error');
        })
    })
}

// 分页查询StJsInfo信息
export const queryJsInfoPage = (data) => request({
    url: baseUrl + '/stJsInfoSpecial/queryJsInfoPage',
    method: 'post',
    data: {
        ...data
    }
});
//  根据工号查询数据
export const getJsInfoByZjh = (data) => request({
    url: baseUrl + '/stJsInfoSpecial/getJsInfoByZjh',
    method: 'post',
    data: {
        ...data
    }
});