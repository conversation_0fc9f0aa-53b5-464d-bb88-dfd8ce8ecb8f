import request from '@/router/axios';
import {baseUrl} from '@/config/env';

// 查询字典字段
export const getAllDictField = (data) => request({
    url: baseUrl + '/dict/dictfield/all',
    method: 'get',
    data: {
        ...data
    }
});

// 查询自定义列
export const getAllCustomColData = (data) => request({
    url: baseUrl + '/dict/customcol/all',
    method: 'get',
    params: {
        ...data
    }
});

// 保存自定义列
export const saveCustomColData = (data) => request({
    url: baseUrl + '/dict/customcol/saveCustomColData',
    method: 'post',
    data: {
        ...data
    }
});
