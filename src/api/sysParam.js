import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditParam = (data) => request({
    url: baseUrl + '/sytSysParam/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteParam = (data) => request({
    url: baseUrl + '/sytSysParam/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const GetSysParamList = (data) => request({
    url: baseUrl + '/sytSysParam/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const GetSysParam = (data) => request({
    url: baseUrl + '/sytSysParam/get',
    method: 'post',
    data: {
        ...data
    }
});

export const GetSysParamNoLogin = (data) => request({
    url: baseUrl + '/sytSysParam/getNoLogin',
    method: 'post',
    data: {
        ...data
    }
});
export const GetSysParamListNoLogin = (data) => request({
    url: baseUrl + '/sytSysParam/getListNoLogin',
    method: 'post',
    data: {
        ...data
    }
});
