import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditDataSet = (data) => request({
    url: baseUrl + '/report/dataSet/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteDataSet = (data) => request({
    url: baseUrl + '/report/dataSet/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDataSet = (data) => request({
    url: baseUrl + '/report/dataSet/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const ListSetType = (data) => request({
    url: baseUrl + '/report/dataSet/querySetType',
    method: 'post',
    data: {
        ...data
    }
});

export const GetDataSet = (data) => request({
    url: baseUrl + '/report/dataSet/get',
    method: 'post',
    data: {
        ...data
    }
});

export const testConnection = (data) => request({
    url: baseUrl + '/report/dataSet/testConnection',
    method: 'post',
    data: {
        ...data
    }
});

export const testTransformSet = (data) => request({
    url: baseUrl + '/report/dataSet/testTransform',
    method: 'post',
    data: {
        ...data
    }
});
