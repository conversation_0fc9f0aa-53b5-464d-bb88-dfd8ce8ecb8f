import request from '@/router/axios';
import {baseUrl} from '@/config/env';

/**
 * 查询当前用户绑定记录
 * @param data
 */
export const getCurrentBind = (data) => request({
    url: baseUrl + '/sytJustAuthUser/getCurrentData',
    method: 'post',
    data: {
        ...data
    }
});
/**
 * 解除绑定
 * String id数据id（必填）
 * @param data
 */
export const bindUserDelete = (data) => request({
    url: baseUrl + '/sytJustAuthUser/delete',
    method: 'post',
    data: {
        ...data
    }
});
/**
 * 用户绑定
 * /user/auth/login/类型
 * 类型的值对应为：
 * dingtalk对应的钉钉
 * wechat_enterprise对应的企业微信
 * 例如：/user/auth/login/dingtalk
 * @param data
 */
// export const bindUserType = (data) => request({
//     url: baseUrl + '/user/auth/login/'+data,
//     method: 'get',
// });
export const bindUserType = (data) => request({
    url: baseUrl + '  /user/auth/bind',
    method: 'post',
    data: {
        ...data
    }
});



