import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/ksgl/cjxgl/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/ksgl/cjxgl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/ksgl/cjxgl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/ksgl/cjxgl/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/ksgl/cjxgl/submit',
    method: 'post',
    data: row
  })
}

export const disposal = (ids) => {
  return request({
    url: '/ksgl/cjxgl/disposal',
    method: 'post',
    params: {
      ids,
    }
  })
}
