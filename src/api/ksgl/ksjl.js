import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/ksgl/ksjl/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/ksgl/ksjl/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/ksgl/ksjl/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/ksgl/ksjl/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/ksgl/ksjl/submit',
    method: 'post',
    data: row
  })
}

