import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditAnalysisItem = (data) => request({
    url: baseUrl + '/data/analysis/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteAnalysisItem = (data) => request({
    url: baseUrl + '/data/analysis/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const ListAnalysisItem = (data) => request({
    url: baseUrl + '/data/analysis/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const GetAnalysisList = (data) => request({
    url: baseUrl + '/data/analysis/list',
    method: 'post',
    data: {
        ...data
    }
});


export const ListTopics = (data) => request({
    url: baseUrl + '/data/analysis/queryTopics',
    method: 'post',
    data: {
        ...data
    }
});