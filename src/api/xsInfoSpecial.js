import request from '@/router/axios';
import {baseUrl} from '@/config/env';

// 分页查询数据
export const queryPage = (data) => request({
    url: baseUrl + '/stXsInfoSpecial/queryPage',
    method: 'post',
    data: {
        ...data
    }
});
//  编辑
export const editInfo = (data) => request({
    url: baseUrl + '/stXsInfoSpecial/edit',
    method: 'post',
    data: {
        ...data
    }
});

// 删除
export const deleteInfo = (data) => request({
    url: baseUrl + '/stXsInfoSpecial/delete',
    method: 'post',
    data: {
        ...data
    }
});
//  批量增加
export const addBatchIds = (data) => request({
    url: baseUrl + '/stXsInfoSpecial/addBatchIds',
    method: 'post',
    data: {
        ...data
    }
});
// 导出数据
export function exportData(data) {
    return new Promise((resolve) =>{
        // eslint-disable-next-line no-undef
        axios({
            method:'post',
            url: baseUrl + '/stXsInfoSpecial/export',
            data:data,
            responseType:'blob'
        }).then(res =>{
            resolve(res.data);
        }).catch(err =>{
            resolve('error');
        })
    })
}

// 分页查询数据
export const queryXsInfoPage = (data) => request({
    url: baseUrl + '/stXsInfoSpecial/queryXsInfoPage',
    method: 'post',
    data: {
        ...data
    }
});
//  根据学号查询数据
export const getXsinfoByXh = (data) => request({
    url: baseUrl + '/stXsInfoSpecial/getXsinfoByXh',
    method: 'post',
    data: {
        ...data
    }
});