import request from '@/router/axios';
import {baseUrl} from '@/config/env';


export const queryPage = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const sytPermissionAccountQueryPage = (data) => request({
    url: baseUrl + '/sytPermissionAccount/queryPage',
    method: 'post',
    data: {
        ...data
    }
});


export const GetUser = (data) => request({
    url: baseUrl + '/user',
    method: 'post',
    data: {
        ...data
    }
});

export const GetUserSwitchRole = (data) => request({
    url: baseUrl + '/user/switchRole',
    method: 'post',
    data: {
        ...data
    }
});
