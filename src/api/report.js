import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditReport = (data) => request({
    url: baseUrl + '/report/manage/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteReport = (data) => request({
    url: baseUrl + '/report/manage/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const QueryPageReport = (data) => request({
    url: baseUrl + '/report/manage/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const ListReport = (data) => request({
    url: baseUrl + '/report/manage/list',
    method: 'post',
    data: {
        ...data
    }
});
export const getALl = (data) => request({
    url: baseUrl + '/report/manage/getALl',
    method: 'post',
    data: {
        ...data
    }
});

export const ListReportNoLogin = (data) => request({
    url: baseUrl + '/report/manage/list/nologin',
    method: 'post',
    data: {
        ...data
    }
});

export const GetReport = (data) => request({
    url: baseUrl + '/report/manage/get',
    method: 'post',
    data: {
        ...data
    }
});

export const CopyReport = (data) => request({
    url: baseUrl + '/report/manage/copy',
    method: 'post',
    data: {
        ...data
    }
});

