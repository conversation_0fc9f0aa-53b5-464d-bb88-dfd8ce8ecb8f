import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const warnHandlequeryPage = (data) => request({
    url: baseUrl + '/dataWarning/dealrecord/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const warnHandleget = (data) => request({
    url: baseUrl + '/dataWarning/dealrecord/get',
    method: 'post',
    data: {
        ...data
    }
});

export const warnHandlesave = (data) => request({
    url: baseUrl + '/dataWarning/dealrecord/save',
    method: 'post',
    data: {
        ...data
    }
});