import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditDict = (data) => request({
    url: baseUrl + '/sytSysDict/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteDict = (data) => request({
    url: baseUrl + '/sytSysDict/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const QueryPageDict = (data) => request({
    url: baseUrl + '/sytSysDict/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const GetListDict = (data) => request({
    url: baseUrl + '/sytSysDict/list',
    method: 'post',
    params: {
        ...data
    },
    data: {
        ...data
    }
});

export const GetDict = (data) => request({
    url: baseUrl + '/sytSysDict/get',
    method: 'post',
    data: {
        ...data
    }
});

export const selectDictList = (data) => request({
    url: baseUrl + '/sytSysDict/select',
    method: 'post',
    data: {
        ...data
    }
});
