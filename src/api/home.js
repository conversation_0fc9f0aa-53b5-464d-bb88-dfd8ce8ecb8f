import request from '@/router/axios';
import {baseUrl} from '@/config/env';

//获取桌面
export const GetAllDesktop = () => request({
    url: baseUrl + '/sytDesktop/listByUser',
    method: 'post'
});

// 根据id获取桌面信息
export const GetDesktopById = (data) => request({
    url: baseUrl + '/sytDesktop/data',
    method: 'post',
    data: {
        id: data
    }
});

// 我的桌面所有数据
export const GetIndexInfo = () => request({
    url: baseUrl + '/sytDesktop/listByUser',
    method: 'post'
});

// 服务中心
export const GetServiceCenterCategory = () => request({
    url: baseUrl + '/sytServiceCenterCategory/list',
    method: 'post'
});

// 服务收藏
export const isCollect = (data) => request({
    url: baseUrl + '/sytServiceCenterCollect/operation',
    method: 'post',
    data: {
        id: data
    }
});

// 所有服务
export const GetService = (data) => request({
    url: baseUrl + '/sytServiceCenter/list',
    method: 'post',
    data: {
        ...data
    }
});

// 服务数量
export const GetServiceNum = () => request({
    url: baseUrl + '/sytServiceCenter/count',
    method: 'post',

});

// 提交 服务反馈 
export const Tofeedback = (data) => request({
    url: baseUrl + '/sytServiceCenter/feedback',
    method: 'post',
    data: {
        ...data
    }
});


// 个人设置
export const GetUserInfo = () => request({
    url: baseUrl + '/user/personalInfo',
    method: 'post'
});

// 我的消息
export const MyMessage = (data) => request({
    url: baseUrl + 'sytSysMsg/listByUser',
    method: 'post',
    data
});

// 我的消息 - 已读
export const MessageIsRead = (data) => request({
    url: baseUrl + 'sytSysMsg/read',
    method: 'post',
    data: {
        ids: data
    }
});

// 我的消息 - 删除
export const DelMessage = (data) => request({
    url: baseUrl + 'sytSysMsg/delete',
    method: 'post',
    data: {
        ids: data
    }
});

// 我的消息 - 消息数量
export const MessageCount = (data) => request({
    url: baseUrl + 'sytSysMsg/countByUser',
    method: 'post',
    data: data
});

// 我的消息 - 消息类型
export const MessageType = (data) => request({
    url: baseUrl + 'sytSysMsg/type',
    method: 'post',
    data: data
});


// 我的待办
export const MyTask = (data) => request({
    url: baseUrl + 'sytTodolist/listByUser',
    method: 'post',
    data
});



