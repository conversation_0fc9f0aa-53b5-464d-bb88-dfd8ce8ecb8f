import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditDecisionItem = (data) => request({
    url: baseUrl + '/data/decision/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteDecisionItem = (data) => request({
    url: baseUrl + '/data/decision/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDecisionItem = (data) => request({
    url: baseUrl + '/data/decision/queryPage',
    method: 'post',
    data: {
        ...data
    }
});