import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditDataWarningSet = (data) => request({
    url: baseUrl + '/dataWarning/dataSet/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteDataWarningSet = (data) => request({
    url: baseUrl + '/dataWarning/dataSet/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDataWarningSet = (data) => request({
    url: baseUrl + '/dataWarning/dataSet/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDataWarningSetType = (data) => request({
    url: baseUrl + '/dataWarning/dataSet/querySetType',
    method: 'post',
    data: {
        ...data
    }
});

export const GetDataWarningSet = (data) => request({
    url: baseUrl + '/dataWarning/dataSet/detailBysetId',
    method: 'post',
    data: {
        ...data
    }
});

export const testTransformSet = (data) => request({
    url: baseUrl + '/dataWarning/dataSet/testTransform',
    method: 'post',
    data: {
        ...data
    }
});
