import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditDataSource = (data) => request({
    url: baseUrl + '/report/dataSource/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteDataSource = (data) => request({
    url: baseUrl + '/report/dataSource/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDataSource = (data) => request({
    url: baseUrl + '/report/dataSource/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const GetDataSource = (data) => request({
    url: baseUrl + '/report/dataSource/get',
    method: 'post',
    data: {
        ...data
    }
});

export const testConnection = (data) => request({
    url: baseUrl + '/report/dataSource/testConnection',
    method: 'post',
    data: {
        ...data
    }
});
