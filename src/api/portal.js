import request from '@/router/axios';
import {baseUrl} from '@/config/env';

// 轮播图
export const GetCarousel = () => request({
    url: baseUrl + '/sytRollBanner/list',
    method: 'post',
});

// 新闻
export const GetNews = (data) => request({
  url: baseUrl + '/sytNews/list',
  method: 'post',
  data
});

// 消息
export const GetMsg = (data) => request({
  url: baseUrl + '/sytSysMsg/list',
  method: 'post',
  data
});

// 我的数据
export const GetUserData = (data) => request({
  url: baseUrl + '/sytUserData/list',
  method: 'post',
  data
});

// 集成系统 
export const GetSystem = (data) => request({
  url: baseUrl + '/oauthClientDetails/list',
  method: 'post',
  data
});

// 我的服务
// export const GetService = (data) => request({
//   url: baseUrl + '/sytSysMsg/list',
//   method: 'post',
//   data
// });

// 推荐服务
export const GetRecommend = (data) => request({
  url: baseUrl + '/sytServiceCenter/recommendList',
  method: 'post',
  data
});

// 收藏服务
export const GetCollect = (data) => request({
  url: baseUrl + '/sytServiceCenterCollect/list',
  method: 'post',
  data
});

// 进入服务
export const ToService = (data) => request({
  url: baseUrl + '/oauthClient/service',
  method: 'post',
  data
});