import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const warnspecialListPage = (data) => request({
    url: baseUrl + '/dataWarning/specialList/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const warnspecialList = (data) => request({
    url: baseUrl + '/dataWarning/specialList/list',
    method: 'post',
    data: {
        ...data
    }
});

export const warnspecialListdelete = (data) => request({
    url: baseUrl + '/dataWarning/specialList/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const warnspecialListimport = (data) => request({
    url: baseUrl + '/dataWarning/specialList/import',
    method: 'post',
    data: {
        ...data
    }
});

export const warnspecialListAdds = (data) => request({
    url: baseUrl + '/dataWarning/specialList/add',
    method: 'post',
    data: {
        ...data
    }
});