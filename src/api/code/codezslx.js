import request from '@/router/axios';
import {baseUrl} from '@/config/env';
import FileSaver from 'file-saver';

export const getList = (current, size, params) => {
  return request({
    url: '/code/codezslx/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/codezslx/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/codezslx/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/codezslx/submit',
    method: 'post',
    data: row
  })
}

export const sytSysDict = (type) => {
  return request({
    url: '/sytSysDict/list?code='+type,
    method: 'post',
  })
}

export const tjlistpage = (current, size, params) => {
  return request({
    url: '/ksgl/tdd/stat/page',
    method: 'post',
    data:{
      ...params,
      current,
      size,
    },
    params: {
    }
  })
}

export const update = (row) => {
  return request({
    url: '/code/codezslx/submit',
    method: 'post',
    data: row
  })
}

// 导出数据
export function exportData(data) {
  return new Promise((resolve) =>{
    // eslint-disable-next-line no-undef
    request({
      method:'post',
      url: baseUrl + '/code/codezslx/export',
      data:data,
      responseType:'blob'
    }).then(res =>{
      resolve(res.data);
    }).catch(err =>{
      resolve('error');
    })
  })
}

export async function exportDataAsync(data) {
  try {
    // 发送请求获取字节流数据
    const response = await request.get(baseUrl + '/code/codezslx/export', {
      responseType: 'arraybuffer',
      data:data,
    });
    // 获取文件名
    const disposition = response.headers['content-disposition'];
    const encodedFileName = disposition.split(';')[1].trim().split('=')[1];
    const fileName = decodeURIComponent(encodedFileName); // 解码文件名
    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/octet-stream' });
    // 使用FileSaver.js保存文件，传入获取到的文件名
    FileSaver.saveAs(blob, fileName);
  } catch (error) {
    console.error('Error exporting data:', error);
  }
}

export const statexport = (data) => {
  return request({
    url: '/ksgl/tdd/stat/export',
    responseType: 'arraybuffer',
    method: 'post',
    data: data
  })
}


