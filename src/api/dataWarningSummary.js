import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditTaskSummary = (data) => request({
    url: baseUrl + '/dataWarning/taskSummary/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteTaskSummary = (data) => request({
    url: baseUrl + '/dataWarning/taskSummary/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const QueryTaskSummary = (data) => request({
    url: baseUrl + '/dataWarning/taskSummary/queryPage',
    method: 'post',
    data: {
        ...data
    }
});
export const modifyEnableFlag = (data) => request({
    url: baseUrl + '/dataWarning/taskSummary/modifyEnableFlag',
    method: 'post',
    data: {
        ...data
    }
});

export const execute = (data) => request({
    url: baseUrl + '/dataWarning/taskSummary/execute',
    method: 'post',
    data: {
        ...data
    }
});