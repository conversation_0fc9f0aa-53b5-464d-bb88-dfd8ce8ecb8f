import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditAuthConfig = (data) => request({
    url: baseUrl + '/sytJustAuthConfig/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteAuthConfig = (data) => request({
    url: baseUrl + '/sytJustAuthConfig/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const GetAuthConfigList = (data) => request({
    url: baseUrl + '/sytJustAuthConfig/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const GetAuthConfig = (data) => request({
    url: baseUrl + '/sytJustAuthConfig/get',
    method: 'post',
    data: {
        ...data
    }
});