import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/code/kslxdm/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/kslxdm/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/kslxdm/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/kslxdm/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/code/kslxdm/submit',
    method: 'post',
    data: row
  })
}

