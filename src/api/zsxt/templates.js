import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/syt/templates/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/syt/templates/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/syt/templates/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/syt/templates/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/syt/templates/submit',
    method: 'post',
    data: row
  })
}

