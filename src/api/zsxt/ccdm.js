import request from '@/router/axios';

export const getAll = (params) => {
  return request({
    url: '/code/ccdm/all',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/code/ccdm/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/ccdm/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/ccdm/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/ccdm/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/code/ccdm/submit',
    method: 'post',
    data: row
  })
}

