import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/code/qbjhk/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/qbjhk/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/qbjhk/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/qbjhk/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/code/qbjhk/submit',
    method: 'post',
    data: row
  })
}

