import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/syt/lqtzsrules/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/syt/lqtzsrules/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/syt/lqtzsrules/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/syt/lqtzsrules/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/syt/lqtzsrules/submit',
    method: 'post',
    data: row
  })
}
export const generateNotificationNumber = (row) => {
  return request({
    url: '/syt/lqtzsrules/generateNotificationNumber',
    method: 'post',
    data: row
  })
}
export const clearNotificationNumber = (row) => {
  return request({
    url: '/syt/lqtzsrules/clearNotificationNumber',
    method: 'post',
    data: row
  })
}

export const getAll = (params) => {
  return request({
    url: '/ksgl/lqtzs/all',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const getPrintData = (params) => {
  return request({
    url: '/ksgl/lqtzs/getPrintData',
    method: 'post',
    params: {
      ...params,
    }
  })
}
export const print = (host,params) => {
  host = host ? host : 'http://localhost:7152';
  return request({
    url: host + '/print',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    params: {
      ...params,
    }
  })
}

