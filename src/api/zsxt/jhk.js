import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/code/jhk/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/jhk/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/jhk/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/jhk/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/code/jhk/submit',
    method: 'post',
    data: row
  })
}

