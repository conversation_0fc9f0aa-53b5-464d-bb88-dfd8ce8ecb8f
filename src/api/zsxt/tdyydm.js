import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/code/tdyydm/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/tdyydm/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/tdyydm/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/tdyydm/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/code/tdyydm/submit',
    method: 'post',
    data: row
  })
}

