import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const warnInfoqueryPage = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const warnInflist = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/list',
    method: 'post',
    data: {
        ...data
    }
});

// 生成导出人员数据
export const GenExportData = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/generateExportData',
    method: 'post',
    data: {
        ...data
    }
});

export const warnInfoexport = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/export',
    method: 'post',
    data: {
        ...data
    }
});

export const warnInfodelete = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const warnInfoget = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/get',
    method: 'post',
    data: {
        ...data
    }
});

// 预警统计
export const getStatisticsData = (data) => request({
    url: baseUrl + '/dataWarning/warnInfo/getStatisticsData',
    method: 'post',
    data: {
        ...data
    }
});
