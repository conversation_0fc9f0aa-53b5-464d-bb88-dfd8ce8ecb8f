import request from '@/router/axios';
import {baseUrl} from '@/config/env';

// 保存大屏
export const insertDashboard = (data) => request({
  url: baseUrl + '/report/dashboard/edit',
  method: 'post',
  data: {
    ...data
  }
});

// 预览、查询大屏详情
export const detailDashboard = (data) => request({
  url: baseUrl + '/report/dashboard/get',
  method: 'post',
  data: {
    ...data
  }
});

// 获取动态数据
export const getData = (data) => request({
  url: baseUrl + '/report/dashboard/getData',
  method: 'post',
  data: {
    ...data
  }
});

// 获取下钻数据
export const getDirllData = (data) => request({
  url: baseUrl + '/report/dashboard/getDirllData',
  method: 'post',
  data: {
    ...data
  }
});

// 生成导出下钻数据
export const GenExportDrillData = (data) => request({
  url: baseUrl + '/report/dashboard/generateExportData',
  method: 'post',
  data: {
    ...data
  }
});


// 数据集查询
export const queryAllDataSet = (data) => request({
  url: baseUrl + '/report/dataSet/list',
  method: 'post',
  data: {
    ...data
  }
});

// 获取数据集信息
export const detailBysetId = (data) => request({
  url: baseUrl + '/report/dataSet/detailBysetId',
  method: 'post',
  data: {
    ...data
  }
});

// 导出大屏
export function exportDashboard(data) {
  return new Promise((resolve) =>{
    axios({
      method:'get',
      url: baseUrl + '/report/dashboard/export',
      // headers: { 'Authorization': getToken() },
      params:data,
      responseType:'blob'
    }).then(res =>{
      resolve(res.data);
    }).catch(err =>{
      resolve('error');
    })
  })

}

// 导入大屏
export function importDashboard(data) {
  return request({
    url: baseUrl + '/report/dashboard/import',
    method: 'post',
    data,
  })
}

// 获取明文手机号
export const getCleartextTelmobile = (data) => request({
  url: baseUrl + '/sytPermissionAccount/getCleartextTelmobile',
  method: 'post',
  data: {
    ...data
  }
});

