import request from '@/router/axios';

export const getAll = (params) => {
  return request({
    url: '/dict/dictgroup/all',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/dict/dictgroup/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/dict/dictgroup/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/dict/dictgroup/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/dict/dictgroup/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/dict/dictgroup/submit',
    method: 'post',
    data: row
  })
}

