import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/dict/dictfield/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/dict/dictfield/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/dict/dictfield/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/dict/dictfield/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/dict/dictfield/submit',
    method: 'post',
    data: row
  })
}

