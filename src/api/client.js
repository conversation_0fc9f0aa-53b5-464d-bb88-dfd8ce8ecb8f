import request from '@/router/axios';
import {baseUrl} from '@/config/env';


export const EditClient = (data) => request({
    url: baseUrl + '/oauthClientDetails/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteClient = (data) => request({
    url: baseUrl + '/oauthClientDetails/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const GetClientList = (data) => request({
    url: baseUrl + '/oauthClientDetails/queryPage',
    method: 'post',
    data: {
        ...data
    }
});