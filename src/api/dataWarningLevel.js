import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const warningLevelqueryPage = (data) => request({
    url: baseUrl + '/dataWarning/warningLevel/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const warningLevellist = (data) => request({
    url: baseUrl + '/dataWarning/warningLevel/list',
    method: 'post',
    data: {
        ...data
    }
});

export const warningLeveledit = (data) => request({
    url: baseUrl + '/dataWarning/warningLevel/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const warningLeveldelete = (data) => request({
    url: baseUrl + '/dataWarning/warningLevel/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const warninggetLevelData = (data) => request({
    url: baseUrl + '/dataWarning/warningLevel/getLevelData',
    method: 'post',
    data: {
        ...data
    }
});
