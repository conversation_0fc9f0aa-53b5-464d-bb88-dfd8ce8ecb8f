import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const EditDataWarningRule = (data) => request({
    url: baseUrl + '/dataWarning/rule/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteDataWarningRule = (data) => request({
    url: baseUrl + '/dataWarning/rule/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const replicateWarningRule = (data) => request({
    url: baseUrl + '/dataWarning/rule/replicate',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDataWarningRule = (data) => request({
    url: baseUrl + '/dataWarning/rule/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const ListDataWarningRuleType = (data) => request({
    url: baseUrl + '/dataWarning/rule/queryType',
    method: 'post',
    data: {
        ...data
    }
});
export const ListDataWarningRuleList = (data) => request({
    url: baseUrl + '/dataWarning/rule/list',
    method: 'post',
    data: {
        ...data
    },
    params:data,
});

export const GetDDataWarningRule = (data) => request({
    url: baseUrl + '/dataWarning/rule/get',
    method: 'post',
    data: {
        ...data
    }
});

export const testTransformRule = (data) => request({
    url: baseUrl + '/dataWarning/rule/testTransform',
    method: 'post',
    data: {
        ...data
    }
});

export const modifyEnableFlag = (data) => request({
    url: baseUrl + '/dataWarning/rule/modifyEnableFlag',
    method: 'post',
    data: {
        ...data
    }
});

export const execute = (data) => request({
    url: baseUrl + '/dataWarning/rule/execute',
    method: 'post',
    data: {
        ...data
    }
});
