import Vue from 'vue';
import axios from './router/axios';
import VueAxios from 'vue-axios';
import 'vue-cron-generator/src/styles/global.css'

import App from './App';
import router from './router/router';
import mixins from '@/mixins/mixins';
// 权限
import './permission';
// 日志
import './error';
//页面缓冲
import './cache';
import store from './store';
import * as urls from '@/config/env';
import Element from 'element-ui';
import i18n from './lang' // Internationalization
import './styles/common.scss';
import basicBlock from './components/basic-block/main'
import basicContainer from './components/basic-container/main'
import './styles/assets/iconfont/iconfont.css';
import "./assets/iconfont/iconfont.css";

// element-ui
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/styles/normalize.css' // A modern alternative to CSS resets
import '@/assets/styles/common.css'
import '@/assets/styles/index.scss' // custome global css
//import Avue from '@smallwei/avue';
//import '@smallwei/avue/lib/index.css';
//Vue.use(Avue);
import VueSuperSlide from 'vue-superslide'
// 全局定义echarts
import ECharts from 'vue-echarts'
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/tooltip'
import dataV from '@jiaminghi/data-view'
import JsonExcel from 'vue-json-excel'

import Vant from 'vant';
import 'vant/lib/index.css';

Vue.use(Vant);


Vue.component('downloadExcel', JsonExcel)
Vue.use(VueSuperSlide)


//import 'echarts-liquidfill'
// import 'echarts-gl'
Vue.component('v-chart', ECharts)

Vue.use(dataV)


Vue.use(router)
Vue.use(VueAxios, axios)
Vue.use(Element, {
    i18n: (key, value) => i18n.t(key, value)
})
Vue.use(window.AVUE, {
    i18n: (key, value) => i18n.t(key, value)
})

//注册全局容器
Vue.component('basicContainer', basicContainer)
Vue.component('basicBlock', basicBlock)
// 加载相关url地址
Object.keys(urls).forEach(key => {
    Vue.prototype[key] = urls[key];
})

// 动态加载阿里云字体库
// iconfontVersion.forEach(ele => {
//     loadStyle(iconfontUrl.replace('$key', ele));
// })

// register global mixins.
Vue.mixin(mixins)

Vue.config.productionTip = false;

new Vue({
    router,
    store,
    i18n,
    render: h => h(App)
}).$mount('#app')

Vue.config.silent = true

//main.js
//方式一
Vue.prototype.$EventBus = new Vue();
