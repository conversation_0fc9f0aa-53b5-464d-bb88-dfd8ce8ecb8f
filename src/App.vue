<template>
    <div id="app">
        <div v-show="loading" class="avue-home">
            <div class="avue-home__main">
                <img
                        class="avue-home__loading"
                        src="@/assets/imgs/loading-spin.svg"
                        alt="loading"
                />
                <div class="avue-home__title">
                    正在加载资源
                </div>
                <div class="avue-home__sub-title">
                    初次加载资源可能需要较多时间 请耐心等待
                </div>
            </div>
        </div>
        <router-view v-if="isRouterAlive"/>
    </div>
</template>

<script>
    // import "@/assets/iconfont/iconfont.css";

    export default {
        name: "app",
        provide() {
            return {
                appReload: this.reload,
            };
        },
        data() {
            return {
                isRouterAlive: true,
                loading: true,
            };
        },
        created() {
            if (this._isMobile()) {
                this.$store.state.isMobile = true;
            } else {
                this.$store.state.isMobile = false;
            }
        },
        methods: {
            //App.vue，判断是否为移动端
            _isMobile() {
                let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
                return flag;
            },


            reload() {
                this.isRouterAlive = false;
                this.$nextTick(() => {
                    this.isRouterAlive = true;
                });
            },
            getPath(newVal, oldVal) {
                // console.log(newVal + ' --- ' + oldVal);
                if (newVal === '/bigscreen/designer') {
                    this.$options.methods.loadjsfile('cdn/avue/2.8.14/avue.min.js', 'js');
                } else {
                    this.$options.methods.removejsfile('cdn/avue/2.8.14/avue.min.js', 'js');
                }

                this.loading = false
            },
            loadjsfile(filename, filetype) {
                let fileref = document.createElement('script');
                fileref.setAttribute("type", "text/javascript");
                fileref.setAttribute("src", filename);
                document.getElementsByTagName("body")[0].appendChild(fileref);
            },
            removejsfile(filename, filetype) {
                var targetelement = "script";
                var targetattr = "src";
                var allsuspects = document.getElementsByTagName(targetelement);
                for (var i = allsuspects.length; i >= 0; i--) {
                    if (allsuspects[i] && allsuspects[i].getAttribute(targetattr) != null && allsuspects[i].getAttribute(targetattr).indexOf(filename) != -1) {
                        console.log(allsuspects[i].parentNode.removeChild(allsuspects[i]))
                    }
                }
            },
        },
        computed: {},
        watch: {
            // 监听路由变化
            '$route.path': 'getPath'
        }
    };
</script>
<style>
    @import url("styles/assets/font/font.css");
</style>
<style lang="scss">
    #app {
        width: 100%;
        height: 100%;
        font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, "Hiragino Sans GB",
        "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
    }
</style>
