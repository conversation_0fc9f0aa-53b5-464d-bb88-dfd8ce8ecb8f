/*! Sortable 1.10.0-rc2 - MIT | git://github.com/SortableJS/Sortable.git */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sortable=e()}(this,function(){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function I(i){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},e=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(e=e.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),e.forEach(function(t){var e,n,o;e=i,o=r[n=t],n in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o})}return i}function a(t,e){if(null==t)return{};var n,o,i=function(t,e){if(null==t)return{};var n,o,i={},r=Object.keys(t);for(o=0;o<r.length;o++)n=r[o],0<=e.indexOf(n)||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(o=0;o<r.length;o++)n=r[o],0<=e.indexOf(n)||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function e(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function t(t){return!!navigator.userAgent.match(t)}var w=t(/(?:Trident.*rv[ :]?11\.|msie|iemobile)/i),D=t(/Edge/i),c=t(/firefox/i),l=t(/safari/i)&&!t(/chrome/i)&&!t(/android/i),n=t(/iP(ad|od|hone)/i),i={capture:!1,passive:!1};function u(t,e,n){t.addEventListener(e,n,!w&&i)}function r(t,e,n){t.removeEventListener(e,n,!w&&i)}function d(t,e){if(e){if(">"===e[0]&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(t){return!1}return!1}}function k(t,e,n,o){if(t){n=n||document;do{if(null!=e&&(">"===e[0]?t.parentNode===n&&d(t,e):d(t,e))||o&&t===n)return t;if(t===n)break}while(t=(i=t).host&&i!==document&&i.host.nodeType?i.host:i.parentNode)}var i;return null}var h,f=/\s+/g;function P(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(f," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(f," ")}}function R(t,e,n){var o=t&&t.style;if(o){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),void 0===e?n:n[e];e in o||-1!==e.indexOf("webkit")||(e="-webkit-"+e),o[e]=n+("string"==typeof n?"":"px")}}function b(t,e){var n="";do{var o=R(t,"transform");o&&"none"!==o&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix;return i&&new i(n)}function p(t,e,n){if(t){var o=t.getElementsByTagName(e),i=0,r=o.length;if(n)for(;i<r;i++)n(o[i],i);return o}return[]}function M(){return w?document.documentElement:document.scrollingElement}function X(t,e,n,o,i){if(t.getBoundingClientRect||t===window){var r,a,l,s,c,u,d;if(d=t!==window&&t!==M()?(a=(r=t.getBoundingClientRect()).top,l=r.left,s=r.bottom,c=r.right,u=r.height,r.width):(l=a=0,s=window.innerHeight,c=window.innerWidth,u=window.innerHeight,window.innerWidth),(e||n)&&t!==window&&(i=i||t.parentNode,!w))do{if(i&&i.getBoundingClientRect&&("none"!==R(i,"transform")||n&&"static"!==R(i,"position"))){var h=i.getBoundingClientRect();a-=h.top+parseInt(R(i,"border-top-width")),l-=h.left+parseInt(R(i,"border-left-width")),s=a+r.height,c=l+r.width;break}}while(i=i.parentNode);if(o&&t!==window){var f=b(i||t),p=f&&f.a,g=f&&f.d;f&&(s=(a/=g)+(u/=g),c=(l/=p)+(d/=p))}return{top:a,left:l,bottom:s,right:c,width:d,height:u}}}function Y(t,e,n,o){for(var i=A(t,!0),r=(e||X(t))[n];i;){var a=X(i)[o];if(!("top"===o||"left"===o?a<=r:r<=a))return i;if(i===M())break;i=A(i,!1)}return!1}function g(t,e,n){for(var o=0,i=0,r=t.children;i<r.length;){if("none"!==r[i].style.display&&r[i]!==Mt.ghost&&r[i]!==Mt.dragged&&k(r[i],n.draggable,t,!1)){if(o===e)return r[i];o++}i++}return null}function B(t,e){for(var n=t.lastElementChild;n&&(n===Mt.ghost||"none"===R(n,"display")||e&&!d(n,e));)n=n.previousElementSibling;return n||null}function F(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)"TEMPLATE"===t.nodeName.toUpperCase()||t===Mt.clone||e&&!d(t,e)||n++;return n}function v(t){var e=0,n=0,o=M();if(t)do{var i=b(t),r=i.a,a=i.d;e+=t.scrollLeft*r,n+=t.scrollTop*a}while(t!==o&&(t=t.parentNode));return[e,n]}function A(t,e){if(!t||!t.getBoundingClientRect)return M();var n=t,o=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=R(n);if(n.clientWidth<n.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!n.getBoundingClientRect||n===document.body)return M();if(o||e)return n;o=!0}}}while(n=n.parentNode);return M()}function y(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function m(e,n){return function(){if(!h){var t=arguments;1===t.length?e.call(this,t[0]):e.apply(this,t),h=setTimeout(function(){h=void 0},n)}}}function H(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function E(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function S(t,e){R(t,"position","absolute"),R(t,"top",e.top),R(t,"left",e.left),R(t,"width",e.width),R(t,"height",e.height)}function _(t){R(t,"position",""),R(t,"top",""),R(t,"left",""),R(t,"width",""),R(t,"height","")}var L="Sortable"+(new Date).getTime();function C(){var v,m=[];return{captureAnimationState:function(){if(m=[],this.options.animation){var t=[].slice.call(this.el.children);for(var e in t)if("none"!==R(t[e],"display")&&t[e]!==Mt.ghost){m.push({target:t[e],rect:X(t[e])});var n=X(t[e]);if(t[e].thisAnimationDuration){var o=b(t[e],!0);o&&(n.top-=o.f,n.left-=o.e)}t[e].fromRect=n}}},addAnimationState:function(t){m.push(t)},removeAnimationState:function(t){m.splice(function(t,e){for(var n in t)for(var o in e)if(e[o]===t[n][o])return Number(n);return-1}(m,{target:t}),1)},animateAll:function(t){if(!this.options.animation)return clearTimeout(v),void("function"==typeof t&&t());var e,n,o,i,r=!1,a=0;for(var l in m){var s=0,c=m[l].target,u=c.fromRect,d=X(c),h=c.prevFromRect,f=c.prevToRect,p=m[l].rect,g=b(c,!0);g&&(d.top-=g.f,d.left-=g.e),c.toRect=d,(Y(c,d,"bottom","top")||Y(c,d,"top","bottom")||Y(c,d,"right","left")||Y(c,d,"left","right"))&&(Y(c,p,"bottom","top")||Y(c,p,"top","bottom")||Y(c,p,"right","left")||Y(c,p,"left","right"))&&(Y(c,u,"bottom","top")||Y(c,u,"top","bottom")||Y(c,u,"right","left")||Y(c,u,"left","right"))||(c.thisAnimationDuration&&y(h,d)&&!y(u,d)&&(p.top-d.top)/(p.left-d.left)==(u.top-d.top)/(u.left-d.left)&&(e=p,n=h,o=f,i=this.options,s=Math.sqrt(Math.pow(n.top-e.top,2)+Math.pow(n.left-e.left,2))/Math.sqrt(Math.pow(n.top-o.top,2)+Math.pow(n.left-o.left,2))*i.animation),y(d,u)||(c.prevFromRect=u,c.prevToRect=d,s||(s=this.options.animation),this.animate(c,p,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(c.animationResetTimer),c.animationResetTimer=setTimeout(function(){this.animationStates[this.i].target.animationTime=0,this.animationStates[this.i].target.prevFromRect=null,this.animationStates[this.i].target.fromRect=null,this.animationStates[this.i].target.prevToRect=null,this.animationStates[this.i].target.thisAnimationDuration=null}.bind({animationStates:m,i:Number(l)}),s),c.thisAnimationDuration=s))}clearTimeout(v),r?v=setTimeout(function(){"function"==typeof t&&t()},a):"function"==typeof t&&t(),m=[]},animate:function(t,e,n){if(n){R(t,"transition",""),R(t,"transform","");var o=X(t),i=b(this.el),r=i&&i.a,a=i&&i.d,l=(e.left-o.left)/(r||1),s=(e.top-o.top)/(a||1);t.animatingX=!!l,t.animatingY=!!s,R(t,"transform","translate3d("+l+"px,"+s+"px,0)"),function(t){t.offsetWidth}(t),R(t,"transition","transform "+n+"ms"+(this.options.easing?" "+this.options.easing:"")),R(t,"transform","translate3d(0,0,0)"),"number"==typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout(function(){R(t,"transition",""),R(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},n)}}}}var T=[],x={initializeByDefault:!0},O={mount:function(t){for(var e in x)e in t||(t[e]=x[e]);T.push(t)},pluginEvent:function(t,e,n){this.eventCanceled=!1;var o=t+"Global";for(var i in T)e[T[i].pluginName]&&(e[T[i].pluginName][o]&&(this.eventCanceled=!!e[T[i].pluginName][o](I({sortable:e},n))),e.options[T[i].pluginName]&&e[T[i].pluginName][t]&&(this.eventCanceled=this.eventCanceled||!!e[T[i].pluginName][t](I({sortable:e},n))))},initializePlugins:function(t,e,n){for(var o in T){var i=T[o].pluginName;if(t.options[i]||T[o].initializeByDefault){var r=new T[o](t,e);s(n,((r.sortable=t)[i]=r).options)}}for(var a in t.options){var l=this.modifyOption(t,a,t.options[a]);void 0!==l&&(t.options[a]=l)}},getEventOptions:function(t,e){var n={};for(var o in T)"function"==typeof T[o].eventOptions&&s(n,T[o].eventOptions.call(e,t));return n},modifyOption:function(t,e,n){var o;for(var i in T)t[T[i].pluginName]&&T[i].optionListeners&&"function"==typeof T[i].optionListeners[e]&&(o=T[i].optionListeners[e].call(t[T[i].pluginName],n));return o}};function N(t){var e,n=t.sortable,o=t.rootEl,i=t.name,r=t.targetEl,a=t.cloneEl,l=t.toEl,s=t.fromEl,c=t.oldIndex,u=t.newIndex,d=t.oldDraggableIndex,h=t.newDraggableIndex,f=t.originalEvent,p=t.putSortable,g=t.eventOptions,v=(n=n||o[L]).options,m="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||w||D?(e=document.createEvent("Event")).initEvent(i,!0,!0):e=new CustomEvent(i,{bubbles:!0,cancelable:!0}),e.to=l||o,e.from=s||o,e.item=r||o,e.clone=a,e.oldIndex=c,e.newIndex=u,e.oldDraggableIndex=d,e.newDraggableIndex=h,e.originalEvent=f,e.pullMode=p?p.lastPutMode:void 0;var b=I({},g,O.getEventOptions(i,n));for(var y in b)e[y]=b[y];o&&o.dispatchEvent(e),v[m]&&v[m].call(n,e)}function j(t,e,n){var o=2<arguments.length&&void 0!==n?n:{},i=o.evt,r=a(o,["evt"]);O.pluginEvent.bind(Mt)(t,e,I({dragEl:W,parentEl:z,ghostEl:G,rootEl:U,nextEl:q,lastDownEl:V,cloneEl:Z,cloneHidden:Q,dragStarted:at,putSortable:ot,activeSortable:Mt.active,originalEvent:i,oldIndex:$,oldDraggableIndex:tt,newIndex:J,newDraggableIndex:et,hideGhostForTarget:Ct,unhideGhostForTarget:Tt,cloneNowHidden:function(){Q=!0},cloneNowShown:function(){Q=!1},dispatchSortableEvent:function(t){K({sortable:e,name:t,originalEvent:i})}},r))}function K(t){N(I({putSortable:ot,cloneEl:Z,targetEl:W,rootEl:U,oldIndex:$,oldDraggableIndex:tt,newIndex:J,newDraggableIndex:et},t))}if("undefined"==typeof window||!window.document)throw new Error("Sortable.js requires a window with a document");var W,z,G,U,q,V,Z,Q,$,J,tt,et,nt,ot,it,rt,at,lt,st,ct,ut,dt=!1,ht=!1,ft=[],pt=!1,gt=!1,vt=[],mt=!1,bt=[],yt=n,wt=D||w?"cssFloat":"float",Dt="draggable"in document.createElement("div"),Et=function(){if(w)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}(),St=function(t,e){var n=R(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=g(t,0,e),r=g(t,1,e),a=i&&R(i),l=r&&R(r),s=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+X(i).width,c=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+X(r).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&"none"!==a.float){var u="left"===a.float?"left":"right";return!r||"both"!==l.clear&&l.clear!==u?"horizontal":"vertical"}return i&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||o<=s&&"none"===n[wt]||r&&"none"===n[wt]&&o<s+c)?"vertical":"horizontal"},_t=function(t){function s(a,l){return function(t,e,n,o){var i=t.options.group.name&&e.options.group.name&&t.options.group.name===e.options.group.name;if(null==a&&(l||i))return!0;if(null==a||!1===a)return!1;if(l&&"clone"===a)return a;if("function"==typeof a)return s(a(t,e,n,o),l)(t,e,n,o);var r=(l?t:e).options.group.name;return!0===a||"string"==typeof a&&a===r||a.join&&-1<a.indexOf(r)}}var e={},n=t.group;n&&"object"==o(n)||(n={name:n}),e.name=n.name,e.checkPull=s(n.pull,!0),e.checkPut=s(n.put),e.revertClone=n.revertClone,t.group=e},Ct=function(){!Et&&G&&R(G,"display","none")},Tt=function(){!Et&&G&&R(G,"display","")};document.addEventListener("click",function(t){if(ht)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),ht=!1},!0);function xt(t){if(W){var e=function(t,e){for(var n in ft)if(!B(ft[n])){var o=X(ft[n]),i=ft[n][L].options.emptyInsertThreshold,r=t>=o.left-i&&t<=o.right+i,a=e>=o.top-i&&e<=o.bottom+i;if(i&&r&&a)return ft[n]}}((t=t.touches?t.touches[0]:t).clientX,t.clientY);if(e){var n={};for(var o in t)n[o]=t[o];n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[L]._onDragOver(n)}}}function Ot(t){W&&W.parentNode[L]._isOutsideThisEl(t.target)}function Mt(t,e){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=s({},e),t[L]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return St(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:Number.parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Mt.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var o in O.initializePlugins(this,t,n),n)o in e||(e[o]=n[o]);for(var i in _t(e),this)"_"===i.charAt(0)&&"function"==typeof this[i]&&(this[i]=this[i].bind(this));this.nativeDraggable=!e.forceFallback&&Dt,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?u(t,"pointerdown",this._onTapStart):(u(t,"mousedown",this._onTapStart),u(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(u(t,"dragover",this),u(t,"dragenter",this)),ft.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),s(this,C())}function At(t,e,n,o,i,r,a,l){var s,c,u=t[L],d=u.options.onMove;return!window.CustomEvent||w||D?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=e,s.from=t,s.dragged=n,s.draggedRect=o,s.related=i||e,s.relatedRect=r||X(e),s.willInsertAfter=l,s.originalEvent=a,t.dispatchEvent(s),d&&(c=d.call(u,s,a)),c}function Nt(t){t.draggable=!1}function It(){mt=!1}function kt(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function Pt(t){return setTimeout(t,0)}function Rt(t){return clearTimeout(t)}Mt.prototype={constructor:Mt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(lt=null)},_getDirection:function(t,e){return"function"==typeof this.options.direction?this.options.direction.call(this,t,e,W):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,o=this.el,t=this.options,i=t.preventOnFilter,r=e.type,a=e.touches&&e.touches[0],l=(a||e).target,s=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||l,c=t.filter;if(function(t){bt.length=0;var e=t.getElementsByTagName("input"),n=e.length;for(;n--;){var o=e[n];o.checked&&bt.push(o)}}(o),!W&&!(/mousedown|pointerdown/.test(r)&&0!==e.button||t.disabled||s.isContentEditable||(l=k(l,t.draggable,o,!1))&&l.animated||V===l)){if($=F(l),tt=F(l,t.draggable),"function"==typeof c){if(c.call(this,e,l,this))return K({sortable:n,rootEl:s,name:"filter",targetEl:l,toEl:o,fromEl:o}),j("filter",n,{evt:e}),void(i&&e.cancelable&&e.preventDefault())}else if(c&&(c=c.split(",").some(function(t){if(t=k(s,t.trim(),o,!1))return K({sortable:n,rootEl:t,name:"filter",targetEl:l,fromEl:o,toEl:o}),j("filter",n,{evt:e}),!0})))return void(i&&e.cancelable&&e.preventDefault());t.handle&&!k(s,t.handle,o,!1)||this._prepareDragStart(e,a,l)}}},_prepareDragStart:function(t,e,n){var o,i=this,r=i.el,a=i.options,l=r.ownerDocument;if(n&&!W&&n.parentNode===r)if(U=r,z=(W=n).parentNode,q=W.nextSibling,V=n,nt=a.group,it={target:Mt.dragged=W,clientX:(e||t).clientX,clientY:(e||t).clientY},this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,W.style["will-change"]="all",o=function(){j("delayEnded",i,{evt:t}),Mt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!c&&i.nativeDraggable&&(W.draggable=!0),i._triggerDragStart(t,e),K({sortable:i,name:"choose",originalEvent:t}),P(W,a.chosenClass,!0))},a.ignore.split(",").forEach(function(t){p(W,t.trim(),Nt)}),u(l,"dragover",xt),u(l,"mousemove",xt),u(l,"touchmove",xt),u(l,"mouseup",i._onDrop),u(l,"touchend",i._onDrop),u(l,"touchcancel",i._onDrop),c&&this.nativeDraggable&&(this.options.touchStartThreshold=4,W.draggable=!0),j("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!e||this.nativeDraggable&&(D||w))o();else{if(Mt.eventCanceled)return void this._onDrop();u(l,"mouseup",i._disableDelayedDrag),u(l,"touchend",i._disableDelayedDrag),u(l,"touchcancel",i._disableDelayedDrag),u(l,"mousemove",i._delayedDragTouchMoveHandler),u(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&u(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,a.delay)}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){W&&Nt(W),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;r(t,"mouseup",this._disableDelayedDrag),r(t,"touchend",this._disableDelayedDrag),r(t,"touchcancel",this._disableDelayedDrag),r(t,"mousemove",this._delayedDragTouchMoveHandler),r(t,"touchmove",this._delayedDragTouchMoveHandler),r(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||("touch"==t.pointerType?t:null),!this.nativeDraggable||e?this.options.supportPointer?u(document,"pointermove",this._onTouchMove):u(document,e?"touchmove":"mousemove",this._onTouchMove):(u(W,"dragend",this),u(U,"dragstart",this._onDragStart));try{document.selection?Pt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(t,e){if(dt=!1,U&&W){j("dragStarted",this,{evt:e}),this.nativeDraggable&&u(document,"dragover",Ot);var n=this.options;t||P(W,n.dragClass,!1),P(W,n.ghostClass,!0),Mt.active=this,t&&this._appendGhost(),K({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(rt){this._lastX=rt.clientX,this._lastY=rt.clientY,Ct();for(var t=document.elementFromPoint(rt.clientX,rt.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(rt.clientX,rt.clientY))!==e;)e=t;if(W.parentNode[L]._isOutsideThisEl(t),e)do{if(e[L]){if(e[L]._onDragOver({clientX:rt.clientX,clientY:rt.clientY,target:t,rootEl:e})&&!this.options.dragoverBubble)break}t=e}while(e=e.parentNode);Tt()}},_onTouchMove:function(t){if(it){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,i=t.touches?t.touches[0]:t,r=G&&b(G),a=G&&r&&r.a,l=G&&r&&r.d,s=yt&&ut&&v(ut),c=(i.clientX-it.clientX+o.x)/(a||1)+(s?s[0]-vt[0]:0)/(a||1),u=(i.clientY-it.clientY+o.y)/(l||1)+(s?s[1]-vt[1]:0)/(l||1),d=t.touches?"translate3d("+c+"px,"+u+"px,0)":"translate("+c+"px,"+u+"px)";if(!Mt.active&&!dt){if(n&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}rt=i,R(G,"webkitTransform",d),R(G,"mozTransform",d),R(G,"msTransform",d),R(G,"transform",d),t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!G){var t=this.options.fallbackOnBody?document.body:U,e=X(W,!0,yt,!0,t),n=this.options;if(yt){for(ut=t;"static"===R(ut,"position")&&"none"===R(ut,"transform")&&ut!==document;)ut=ut.parentNode;ut!==document.body&&ut!==document.documentElement?(ut===document&&(ut=M()),e.top+=ut.scrollTop,e.left+=ut.scrollLeft):ut=M(),vt=v(ut)}P(G=W.cloneNode(!0),n.ghostClass,!1),P(G,n.fallbackClass,!0),P(G,n.dragClass,!0),R(G,"transition",""),R(G,"transform",""),R(G,"box-sizing","border-box"),R(G,"margin",0),R(G,"top",e.top),R(G,"left",e.left),R(G,"width",e.width),R(G,"height",e.height),R(G,"opacity","0.8"),R(G,"position",yt?"absolute":"fixed"),R(G,"zIndex","100000"),R(G,"pointerEvents","none"),Mt.ghost=G,t.appendChild(G)}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,i=n.options;j("dragStart",this,{evt:t}),Mt.eventCanceled?this._onDrop():(j("setupClone",this),Mt.eventCanceled||((Z=E(W)).draggable=!1,Z.style["will-change"]="",this._hideClone(),P(Z,this.options.chosenClass,!1),Mt.clone=Z),n.cloneId=Pt(function(){j("clone",n),Mt.eventCanceled||(n.options.removeCloneOnHide||U.insertBefore(Z,W),n._hideClone(),K({sortable:n,name:"clone"}))}),e||P(W,i.dragClass,!0),e?(ht=!0,n._loopId=setInterval(n._emulateDragOver,50)):(r(document,"mouseup",n._onDrop),r(document,"touchend",n._onDrop),r(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,W)),u(document,"drop",n),R(W,"transform","translateZ(0)")),dt=!0,n._dragStartId=Pt(n._dragStarted.bind(n,e,t)),u(document,"selectstart",n),at=!0,l&&R(document.body,"user-select","none"))},_onDragOver:function(n){var o,i,r,a,l=this.el,s=n.target,e=this.options,t=e.group,c=Mt.active,u=nt===t,d=e.sort,h=ot||c,f=this,p=!1;if(!mt){if(void 0!==n.preventDefault&&n.cancelable&&n.preventDefault(),s=k(s,e.draggable,l,!0),O("dragOver"),Mt.eventCanceled)return p;if(W.contains(n.target)||s.animated&&s.animatingX&&s.animatingY||f._ignoreWhileAnimating===s)return A(!1);if(ht=!1,c&&!e.disabled&&(u?d||(r=!U.contains(W)):ot===this||(this.lastPutMode=nt.checkPull(this,c,W,n))&&t.checkPut(this,c,W,n))){if(a="vertical"===this._getDirection(n,s),o=X(W),O("dragOverValid"),Mt.eventCanceled)return p;if(r)return z=U,M(),this._hideClone(),O("revert"),Mt.eventCanceled||(q?U.insertBefore(W,q):U.appendChild(W)),A(!0);var g=B(l,e.draggable);if(!g||function(t,e,n){var o=X(B(n.el,n.options.draggable));return e?t.clientX>o.right+10||t.clientX<=o.right&&t.clientY>o.bottom&&t.clientX>=o.left:t.clientX>o.right&&t.clientY>o.top||t.clientX<=o.right&&t.clientY>o.bottom+10}(n,a,this)&&!g.animated){if(g===W)return A(!1);if(g&&l===n.target&&(s=g),s&&(i=X(s)),!1!==At(U,l,W,o,s,i,n,!!s))return M(),l.appendChild(W),z=l,N(),A(!0)}else if(s.parentNode===l){i=X(s);var v,m,b,y=W.parentNode!==l,w=!function(t,e,n){var o=n?t.left:t.top,i=n?t.right:t.bottom,r=n?t.width:t.height,a=n?e.left:e.top,l=n?e.right:e.bottom,s=n?e.width:e.height;return o===a||i===l||o+r/2===a+s/2}(W.animated&&W.toRect||o,s.animated&&s.toRect||i,a),D=a?"top":"left",E=Y(s,null,"top","top")||Y(W,null,"top","top"),S=E?E.scrollTop:void 0;if(lt!==s&&(m=i[D],pt=!1,gt=!w&&e.invertSwap||y),0!==(v=function(t,e,n,o,i,r,a){var l=X(e),s=n?t.clientY:t.clientX,c=n?l.height:l.width,u=n?l.top:l.left,d=n?l.bottom:l.right,h=!1;if(!r)if(a&&ct<c*o){if(!pt&&(1===st?u+c*i/2<s:s<d-c*i/2)&&(pt=!0),pt)h=!0;else if(1===st?s<u+ct:d-ct<s)return-st}else if(u+c*(1-o)/2<s&&s<d-c*(1-o)/2)return function(t){return F(W)<F(t)?1:-1}(e);if((h=h||r)&&(s<u+c*i/2||d-c*i/2<s))return u+c/2<s?1:-1;return 0}(n,s,a,w?1:e.swapThreshold,null==e.invertedSwapThreshold?e.swapThreshold:e.invertedSwapThreshold,gt,lt===s)))for(var _=F(W);_-=v,(b=z.children[_])&&("none"===R(b,"display")||b===G););if(0===v||b===s)return A(!1);st=v;var C=(lt=s).nextElementSibling,T=!1,x=At(U,l,W,o,s,i,n,T=1===v);if(!1!==x)return 1!==x&&-1!==x||(T=1===x),mt=!0,setTimeout(It,30),M(),T&&!C?l.appendChild(W):s.parentNode.insertBefore(W,T?C:s),E&&H(E,0,S-E.scrollTop),z=W.parentNode,void 0===m||gt||(ct=Math.abs(m-X(s)[D])),N(),A(!0)}if(l.contains(W))return A(!1)}return!1}function O(t,e){j(t,f,I({evt:n,isOwner:u,axis:a?"vertical":"horizontal",revert:r,dragRect:o,targetRect:i,canSort:d,fromSortable:h,target:s,completed:A,onMove:function(t,e){return At(U,l,W,o,t,X(t),n,e)},changed:N},e))}function M(){O("dragOverAnimationCapture"),f.captureAnimationState(),f!==h&&h.captureAnimationState()}function A(t){return O("dragOverCompleted",{insertion:t}),t&&(u?c._hideClone():c._showClone(f),f!==h&&(P(W,ot?ot.options.ghostClass:c.options.ghostClass,!1),P(W,e.ghostClass,!0)),ot!==f&&f!==Mt.active?ot=f:f===Mt.active&&ot&&(ot=null),h===f&&(f._ignoreWhileAnimating=s),f.animateAll(function(){O("dragOverAnimationComplete"),f._ignoreWhileAnimating=null}),f!==h&&(h.animateAll(),h._ignoreWhileAnimating=null)),(s===W&&!W.animated||s===l&&!s.animated)&&(lt=null),e.dragoverBubble||n.rootEl||s===document||(W.parentNode[L]._isOutsideThisEl(n.target),t||xt(n)),!e.dragoverBubble&&n.stopPropagation&&n.stopPropagation(),p=!0}function N(){J=F(W),et=F(W,e.draggable),K({sortable:f,name:"change",toEl:l,newIndex:J,newDraggableIndex:et,originalEvent:n})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){r(document,"mousemove",this._onTouchMove),r(document,"touchmove",this._onTouchMove),r(document,"pointermove",this._onTouchMove),r(document,"dragover",xt),r(document,"mousemove",xt),r(document,"touchmove",xt)},_offUpEvents:function(){var t=this.el.ownerDocument;r(t,"mouseup",this._onDrop),r(t,"touchend",this._onDrop),r(t,"pointerup",this._onDrop),r(t,"touchcancel",this._onDrop),r(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;J=F(W),et=F(W,n.draggable),j("drop",this,{evt:t}),J=F(W),et=F(W,n.draggable),Mt.eventCanceled||(pt=gt=dt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Rt(this.cloneId),Rt(this._dragStartId),this.nativeDraggable&&(r(document,"drop",this),r(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),l&&R(document.body,"user-select",""),t&&(at&&(t.cancelable&&t.preventDefault(),n.dropBubble||t.stopPropagation()),G&&G.parentNode&&G.parentNode.removeChild(G),(U===z||ot&&"clone"!==ot.lastPutMode)&&Z&&Z.parentNode&&Z.parentNode.removeChild(Z),W&&(this.nativeDraggable&&r(W,"dragend",this),Nt(W),W.style["will-change"]="",at&&!dt&&P(W,ot?ot.options.ghostClass:this.options.ghostClass,!1),P(W,this.options.chosenClass,!1),K({sortable:this,name:"unchoose",toEl:z,newIndex:null,newDraggableIndex:null,originalEvent:t}),U!==z?(0<=J&&(K({rootEl:z,name:"add",toEl:z,fromEl:U,originalEvent:t}),K({sortable:this,name:"remove",toEl:z,originalEvent:t}),K({rootEl:z,name:"sort",toEl:z,fromEl:U,originalEvent:t}),K({sortable:this,name:"sort",toEl:z,originalEvent:t})),ot&&ot.save()):J!==$&&0<=J&&(K({sortable:this,name:"update",toEl:z,originalEvent:t}),K({sortable:this,name:"sort",toEl:z,originalEvent:t})),Mt.active&&(null!=J&&-1!==J||(J=$,et=tt),K({sortable:this,name:"end",toEl:z,originalEvent:t}),this.save())))),this._nulling()},_nulling:function(){j("nulling",this),U=W=z=G=q=Z=V=Q=it=rt=at=J=et=$=tt=lt=st=ot=nt=Mt.dragged=Mt.ghost=Mt.clone=Mt.active=null,bt.forEach(function(t){t.checked=!0}),bt.length=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":W&&(this._onDragOver(t),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move");t.cancelable&&t.preventDefault()}(t));break;case"selectstart":t.preventDefault()}},toArray:function(){for(var t,e=[],n=this.el.children,o=0,i=n.length,r=this.options;o<i;o++)k(t=n[o],r.draggable,this.el,!1)&&e.push(t.getAttribute(r.dataIdAttr)||kt(t));return e},sort:function(t){var o={},i=this.el;this.toArray().forEach(function(t,e){var n=i.children[e];k(n,this.options.draggable,i,!1)&&(o[t]=n)},this),t.forEach(function(t){o[t]&&(i.removeChild(o[t]),i.appendChild(o[t]))})},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return k(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(void 0===e)return n[t];var o=O.modifyOption(this,t,e);n[t]=void 0!==o?o:e,"group"===t&&_t(n)},destroy:function(){j("destroy",this);var t=this.el;t[L]=null,r(t,"mousedown",this._onTapStart),r(t,"touchstart",this._onTapStart),r(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(r(t,"dragover",this),r(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),ft.splice(ft.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!Q){if(j("hideClone",this),Mt.eventCanceled)return;R(Z,"display","none"),this.options.removeCloneOnHide&&Z.parentNode&&Z.parentNode.removeChild(Z),Q=!0}},_showClone:function(t){if("clone"===t.lastPutMode){if(Q){if(j("showClone",this),Mt.eventCanceled)return;U.contains(W)&&!this.options.group.revertClone?U.insertBefore(Z,W):q?U.insertBefore(Z,q):U.appendChild(Z),this.options.group.revertClone&&this._animate(W,Z),R(Z,"display",""),Q=!1}}else this._hideClone()}},u(document,"touchmove",function(t){(Mt.active||dt)&&t.cancelable&&t.preventDefault()}),Mt.utils={on:u,off:r,css:R,find:p,is:function(t,e){return!!k(t,e,t,!1)},extend:function(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},throttle:m,closest:k,toggleClass:P,clone:E,index:F,nextTick:Pt,cancelNextTick:Rt,detectDirection:St,getChild:g},Mt.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];for(var o in e[0].constructor===Array&&(e=e[0]),e){var i=e[o];if(!i.prototype||!i.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(el));i.utils&&(Mt.utils=I({},Mt.utils,i.utils)),O.mount(i)}},Mt.create=function(t,e){return new Mt(t,e)};var Xt,Yt,Bt,Ft,Ht,Lt,jt=[],Kt=!(Mt.version="1.10.0-rc2");function Wt(){jt.forEach(function(t){clearInterval(t.pid)}),jt=[]}function zt(){clearInterval(Lt)}function Gt(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,i=t.activeSortable,r=t.dispatchSortableEvent,a=t.hideGhostForTarget,l=t.unhideGhostForTarget,s=n||i;a();var c=document.elementFromPoint(e.clientX,e.clientY);l(),s&&!s.el.contains(c)&&(r("spill"),this.onSpill(o))}var Ut,qt=m(function(n,t,e,o){if(t.scroll){var i,r=t.scrollSensitivity,a=t.scrollSpeed,l=M(),s=!1;Yt!==e&&(Yt=e,Wt(),Xt=t.scroll,i=t.scrollFn,!0===Xt&&(Xt=A(e,!0)));var c=0,u=Xt;do{var d=u,h=X(d),f=h.top,p=h.bottom,g=h.left,v=h.right,m=h.width,b=h.height,y=void 0,w=void 0,D=d.scrollWidth,E=d.scrollHeight,S=R(d),_=d.scrollLeft,C=d.scrollTop;w=d===l?(y=m<D&&("auto"===S.overflowX||"scroll"===S.overflowX||"visible"===S.overflowX),b<E&&("auto"===S.overflowY||"scroll"===S.overflowY||"visible"===S.overflowY)):(y=m<D&&("auto"===S.overflowX||"scroll"===S.overflowX),b<E&&("auto"===S.overflowY||"scroll"===S.overflowY));var T=y&&(Math.abs(v-n.clientX)<=r&&_+m<D)-(Math.abs(g-n.clientX)<=r&&!!_),x=w&&(Math.abs(p-n.clientY)<=r&&C+b<E)-(Math.abs(f-n.clientY)<=r&&!!C);if(!jt[c])for(var O=0;O<=c;O++)jt[O]||(jt[O]={});jt[c].vx==T&&jt[c].vy==x&&jt[c].el===d||(jt[c].el=d,jt[c].vx=T,jt[c].vy=x,clearInterval(jt[c].pid),0==T&&0==x||(s=!0,jt[c].pid=setInterval(function(){o&&0===this.layer&&Mt.active._onTouchMove(Ht);var t=jt[this.layer].vy?jt[this.layer].vy*a:0,e=jt[this.layer].vx?jt[this.layer].vx*a:0;"function"==typeof i&&"continue"!==i.call(Mt.dragged.parentNode[L],e,t,n,Ht,jt[this.layer].el)||H(jt[this.layer].el,e,t)}.bind({layer:c}),24))),c++}while(t.bubbleScroll&&u!==l&&(u=A(u,!1)));Kt=s}},30);function Vt(){}function Zt(){}Vt.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){this.sortable.captureAnimationState();var e=g(this.sortable.el,this.startIndex,this.sortable.options);e?this.sortable.el.insertBefore(t,e):this.sortable.el.appendChild(t),this.sortable.animateAll()},drop:Gt},s(Vt,{pluginName:"revertOnSpill"}),Zt.prototype={onSpill:function(t){this.sortable.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),this.sortable.animateAll()},drop:Gt},s(Zt,{pluginName:"removeOnSpill"});var Qt,$t,Jt,te,ee,ne=[],oe=[],ie=!1,re=!1,ae=!1;function le(t,e){for(var n in oe){var o=e.children[oe[n].sortableIndex+(t?Number(n):0)];o?e.insertBefore(oe[n],o):e.appendChild(oe[n])}}function se(){for(var t in ne)ne[t]!==Jt&&ne[t].parentNode&&ne[t].parentNode.removeChild(ne[t])}return Mt.mount(new function(){function t(){for(var t in this.options={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?u(document,"dragover",this._handleAutoScroll):this.sortable.options.supportPointer?u(document,"pointermove",this._handleFallbackAutoScroll):e.touches?u(document,"touchmove",this._handleFallbackAutoScroll):u(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.sortable.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?r(document,"dragover",this._handleAutoScroll):(r(document,"pointermove",this._handleFallbackAutoScroll),r(document,"touchmove",this._handleFallbackAutoScroll),r(document,"mousemove",this._handleFallbackAutoScroll)),zt(),Wt(),clearTimeout(h),h=void 0},nulling:function(){Ht=Yt=Xt=Kt=Lt=Bt=Ft=null,jt.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(e,n){var o=this,i=e.clientX,r=e.clientY,t=document.elementFromPoint(i,r);if(Ht=e,n||D||w||l){qt(e,this.options,t,n);var a=A(t,!0);!Kt||Lt&&i===Bt&&r===Ft||(Lt&&zt(),Lt=setInterval(function(){var t=A(document.elementFromPoint(i,r),!0);t!==a&&(a=t,Wt()),qt(e,o.options,t,n)},10),Bt=i,Ft=r)}else{if(!this.sortable.options.bubbleScroll||A(t,!0)===M())return void Wt();qt(e,this.options,A(t,!1),!1)}}},s(t,{pluginName:"scroll",initializeByDefault:!0})}),Mt.mount(Zt,Vt),Mt.mount(new function(){function t(){this.options={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){var e=t.dragEl;Ut=e},dragOverValid:function(t){var e=t.completed,n=t.target,o=t.onMove,i=t.activeSortable,r=t.changed;if(i.options.swap){var a=this.sortable.el,l=this.sortable.options;if(n&&n!==a){var s=Ut;Ut=!1!==o(n)?(P(n,l.swapClass,!0),n):null,s&&s!==Ut&&P(s,l.swapClass,!1)}return r(),e(!0)}},drop:function(t){var e=t.activeSortable,n=t.putSortable,o=t.dragEl,i=n||this.sortable,r=this.sortable.options;Ut&&P(Ut,r.swapClass,!1),Ut&&(r.swap||n&&n.options.swap)&&o!==Ut&&(i.captureAnimationState(),i!==e&&e.captureAnimationState(),function(t,e){var n,o,i=t.parentNode,r=e.parentNode;if(!i||!r||i.isEqualNode(e)||r.isEqualNode(t))return;n=F(t),o=F(e),i.isEqualNode(r)&&n<o&&o++;i.insertBefore(e,i.children[n]),r.insertBefore(t,r.children[o])}(o,Ut),i.animateAll(),i!==e&&e.animateAll())},nulling:function(){Ut=null}},s(t,{pluginName:"swap",eventOptions:function(){return{swapItem:Ut}}})}),Mt.mount(new function(){function t(i){for(var t in this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this));i.options.supportPointer?u(document,"pointerup",this._deselectMultiDrag):(u(document,"mouseup",this._deselectMultiDrag),u(document,"touchend",this._deselectMultiDrag)),u(document,"keydown",this._checkKeyDown),u(document,"keyup",this._checkKeyUp),this.options={selectedClass:"sortable-selected",multiDragKey:null,setData:function(t,e){var n="";if(ne.length&&$t===i)for(var o in ne)n+=(o?", ":"")+ne[o].textContent;else n=e.textContent;t.setData("Text",n)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var e=t.dragEl;Jt=e},delayEnded:function(){this.isMultiDrag=~ne.indexOf(Jt)},setupClone:function(t){var e=t.sortable;if(this.isMultiDrag){for(var n in ne)oe.push(E(ne[n])),oe[n].sortableIndex=ne[n].sortableIndex,oe[n].draggable=!1,oe[n].style["will-change"]="",P(oe[n],e.options.selectedClass,!1),ne[n]===Jt&&P(oe[n],e.options.chosenClass,!1);return e._hideClone(),!0}},clone:function(t){var e=t.sortable,n=t.rootEl,o=t.dispatchSortableEvent;if(this.isMultiDrag)return!e.options.removeCloneOnHide&&ne.length&&$t===e?(le(!0,n),o("clone"),!0):void 0},showClone:function(t){var e=t.cloneNowShown,n=t.rootEl;if(this.isMultiDrag){for(var o in le(!1,n),oe)R(oe[o],"display","");return e(),!(ee=!1)}},hideClone:function(t){var e=t.sortable,n=t.cloneNowHidden;if(this.isMultiDrag){for(var o in oe)R(oe[o],"display","none"),e.options.removeCloneOnHide&&oe[o].parentNode&&oe[o].parentNode.removeChild(oe[o]);return n(),ee=!0}},dragStartGlobal:function(t){t.sortable;for(var e in!this.isMultiDrag&&$t&&$t.multiDrag._deselectMultiDrag(),ne)ne[e].sortableIndex=F(ne[e]);ne=ne.sort(function(t,e){return t.sortableIndex-e.sortableIndex}),ae=!0},dragStarted:function(t){var e=t.sortable;if(this.isMultiDrag){if(e.options.sort&&(e.captureAnimationState(),e.options.animation)){for(var n in ne)ne[n]!==Jt&&R(ne[n],"position","absolute");var o=X(Jt,!1,!0,!0);for(var i in ne)ne[i]!==Jt&&S(ne[i],o);ie=re=!0}e.animateAll(function(){if(ie=re=!1,e.options.animation)for(var t in ne)_(ne[t]);e.options.sort&&se()})}},dragOver:function(t){var e=t.target,n=t.completed;if(re&&~ne.indexOf(e))return n(!1)},revert:function(t){var e=t.fromSortable,n=t.rootEl,o=t.sortable,i=t.dragRect;if(1<ne.length){for(var r in ne)o.addAnimationState({target:ne[r],rect:re?X(ne[r]):i}),_(ne[r]),ne[r].fromRect=i,e.removeAnimationState(ne[r]);re=!1,function(t,e){for(var n in ne){var o=e.children[ne[n].sortableIndex+(t?Number(n):0)];o?e.insertBefore(ne[n],o):e.appendChild(ne[n])}}(!o.options.removeCloneOnHide,n)}},dragOverCompleted:function(t){var e=t.sortable,n=t.isOwner,o=t.insertion,i=t.activeSortable,r=t.parentEl,a=t.putSortable,l=e.options;if(o){if(n&&i._hideClone(),ie=!1,l.animation&&1<ne.length&&(re||!n&&!i.options.sort&&!a)){var s=X(Jt,!1,!0,!0);for(var c in ne)ne[c]!==Jt&&(S(ne[c],s),r.appendChild(ne[c]));re=!0}if(!n)if(re||se(),1<ne.length){var u=ee;if(i._showClone(e),i.options.animation&&!ee&&u)for(var d in oe)i.addAnimationState({target:oe[d],rect:te}),oe[d].fromRect=te,oe[d].thisAnimationDuration=null}else i._showClone(e)}},dragOverAnimationCapture:function(t){var e=t.dragRect,n=t.isOwner,o=t.activeSortable;for(var i in ne)ne[i].thisAnimationDuration=null;if(o.options.animation&&!n&&o.multiDrag.isMultiDrag){te=s({},e);var r=b(Jt,!0);te.top-=r.f,te.left-=r.e}},dragOverAnimationComplete:function(){re&&(re=!1,se())},drop:function(t){var e=t.originalEvent,n=t.rootEl,o=t.parentEl,i=t.sortable,r=t.dispatchSortableEvent,a=t.oldIndex,l=t.putSortable,s=l||this.sortable;if(e){var c=i.options,u=o.children;if(!ae)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),P(Jt,c.selectedClass,!~ne.indexOf(Jt)),~ne.indexOf(Jt))ne.splice(ne.indexOf(Jt),1),Qt=null,N({sortable:i,rootEl:n,name:"deselect",targetEl:Jt,originalEvt:e});else{if(ne.push(Jt),N({sortable:i,rootEl:n,name:"select",targetEl:Jt,originalEvt:e}),(!c.multiDragKey||this.multiDragKeyDown)&&e.shiftKey&&Qt&&i.el.contains(Qt)){var d,h,f=F(Qt),p=F(Jt);if(~f&&~p&&f!==p)for(d=f<p?(h=f,p):(h=p,f+1);h<d;h++)~ne.indexOf(u[h])||(P(u[h],c.selectedClass,!0),ne.push(u[h]),N({sortable:i,rootEl:n,name:"select",targetEl:u[h],originalEvt:e}))}else Qt=Jt;$t=s}if(ae&&this.isMultiDrag){if((o[L].options.sort||o!==n)&&1<ne.length){var g=X(Jt),v=F(Jt,":not(."+this.options.selectedClass+")");if(!ie&&c.animation&&(Jt.thisAnimationDuration=null),s.captureAnimationState(),!ie){if(c.animation)for(var m in Jt.fromRect=g,ne)if(ne[m].thisAnimationDuration=null,ne[m]!==Jt){var b=re?X(ne[m]):g;ne[m].fromRect=b,s.addAnimationState({target:ne[m],rect:b})}for(var y in se(),ne)u[v]?o.insertBefore(ne[y],u[v]):o.appendChild(ne[y]),v++;if(a===F(Jt)){var w=!1;for(var D in ne)if(ne[D].sortableIndex!==F(ne[D])){w=!0;break}w&&r("update")}}for(var E in ne)_(ne[E]);s.animateAll()}$t=s}if(n===o||l&&"clone"!==l.lastPutMode)for(var S in oe)oe[S].parentNode&&oe[S].parentNode.removeChild(oe[S])}},nullingGlobal:function(){this.isMultiDrag=ae=!1,oe.length=0},destroy:function(){this._deselectMultiDrag(),r(document,"pointerup",this._deselectMultiDrag),r(document,"mouseup",this._deselectMultiDrag),r(document,"touchend",this._deselectMultiDrag),r(document,"keydown",this._checkKeyDown),r(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!ae&&$t===this.sortable&&!(t&&k(t.target,this.sortable.options.draggable,this.sortable.el,!1)||t&&0!==t.button))for(;ne.length;){var e=ne[0];P(e,this.sortable.options.selectedClass,!1),ne.shift(),N({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvt:t})}},_checkKeyDown:function(t){t.key===this.sortable.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.sortable.options.multiDragKey&&(this.multiDragKeyDown=!1)}},s(t,{pluginName:"multiDrag",utils:{select:function(t){var e=t.parentNode[L];e&&e.options.multiDrag&&!~ne.indexOf(t)&&($t&&$t!==e&&($t.multiDrag._deselectMultiDrag(),$t=e),P(t,e.options.selectedClass,!0),ne.push(t))},deselect:function(t){var e=t.parentNode[L],n=ne.indexOf(t);e&&e.options.multiDrag&&~n&&(P(t,e.options.selectedClass,!1),ne.splice(n,1))}},eventOptions:function(){var n=this,o=[],i=[];return ne.forEach(function(t){var e;o.push({element:t,index:t.sortableIndex}),e=re&&t!==Jt?-1:re?F(t,":not(."+n.options.selectedClass+")"):F(t),i.push({element:t,index:e})}),{items:e(ne),clones:[].concat(oe),oldIndicies:o,newIndicies:i}},optionListeners:{multiDragKey:function(t){return"ctrl"===(t=t.toLowerCase())?t="Control":1<t.length&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}),Mt});