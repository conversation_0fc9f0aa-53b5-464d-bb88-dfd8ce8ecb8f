@charset "utf-8";
.wrapAll{
    background: url(../img/bd.png) !important;
    background-size: 100% 100% !important;
}
.con_head h1 {
	font-size: 36px;
	padding: 20px;
	color:#333!important;
}

.con_head p {
	font-size: 14px;
	color: #606060 !important;
	width: 80%;
	padding: 30px;
	margin: auto;
}

.analyseHover{
    background: #305395 !important;
    border: 1px solid #305395 !important;
}
.analyseHover .analyseName{
    color: #f8f8f8 !important;
    border-bottom: 1px solid #999 !important;
}
.analyseHover .analyseDetial{
    color: #e8e8e8 !important;
}

.analyse{
    background: #fff !important;
    color: #333 !important;
}
.analyseName{
    border-bottom: 1px solid #ddd !important;
    color: #333 !important;
}
.analyseDetial{
    color: #999 !important;
}
/* .analyse>div>img{
    filter: brightness(0.3) !important;
} */
.analyse i{
    color: #1890FF !important;
}
.analyseHover i{
    color: #fff !important;
}

.sortWrap p{
    color: #333 !important;
}
.sort{
    color: #333 !important;
}
.sort:hover{
    color: #fff !important;
	background-color: #0048d1a3 !important;
}
.text1{
    font-size: 35px;
    text-align: center;
    font-weight: bold;
    margin-top: 50px;
}
.text2{
    font-size: 16px;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 80px;
    color: #333
}
.tabClick {background-color: #3b67b8 !important;}
.tabClick:hover {background-color: #0048d1a3 !important;}
.analyseHover .analyseName {
    border-bottom: 1px solid #ffffff1c !important;
}
.list>ul>li{
    background: #305395 !important;
}
.list>ul>li:hover{
    background: #769de6 !important;
}
.list>ul>.activeDetail{
    background: #769de6 !important;
}