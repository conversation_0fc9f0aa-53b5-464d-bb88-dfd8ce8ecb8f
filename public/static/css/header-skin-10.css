@charset "utf-8";
.wrapAll{
    background: url(../img/bd.png) !important;
    background-size: 100% 100% !important;
}
.con_head h1 {
	font-size: 36px;
	padding: 20px;
	font-weight: bold;
	color:#333!important;
}

.con_head p {
	font-size: 14px;
	color: #8c8c8c !important;
	width: 80%;
	padding: 30px;
	margin: auto;
}

.analyseHover{
    background: #58b9c7 !important;
    border: 1px solid #58b9c7 !important;
}
.analyseHover .analyseName{
    color: #f8f8f8 !important;
    border-bottom: 1px solid #fff3 !important;
}
.analyseHover .analyseDetial{
    color: #e8e8e8 !important;
}

.analyse{
    background: #fff !important;
    color: #333 !important;
}
.analyseName{
    border-bottom: 1px solid #e7e7e7  !important;
    color: #333 !important;
}
.analyseDetial{
    color: #afafaf !important;
}
/* .analyse>div>img{
    filter: brightness(0.3) !important;
} */
.analyse i{
    color: #58b9c7 !important;
}
.analyseHover i{
    color: #fff !important;
}

.sortWrap p{
    color: #333 !important;
}
.sort{
    color: #999 !important;
}
.sort:hover{
    color: #fff !important;
}
.tabClick {background-color: #00A2B8;}
.tabClick:hover {background-color: #00A2B8;}
.topbar,
.leftMenuButton,
.rightMenuButton {
	background: rgba(19, 119, 126, 1) !important;
}

.topbar::after {
	content: "";
	opacity: 0.1;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	position: absolute;
	z-index: -1;
	background-image: url(../img/u25716.png) !important;
	background-repeat: no-repeat !important;
}

.text1{
    font-size: 35px;
    text-align: center;
    font-weight: bold;
    margin-top: 50px;
}
.text2{
    font-size: 16px;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 80px;
    color: #333
}
.list>ul>li{
    background: rgba(19, 119, 126, 1) !important;
}
.list>ul>li:hover{
    background: rgb(52, 159, 167) !important;
}
.list>ul>.activeDetail{
    background: rgb(52, 159, 167) !important;
}