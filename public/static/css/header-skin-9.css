@charset "utf-8";
.con_head h1 {
	font-size: 36px;
	padding: 20px;
	color:#333!important;
}

.con_head p {
	font-size: 14px;
	color: #606060 !important;
	width: 80%;
	padding: 30px;
	margin: auto;
}

.analyseHover{
    background: #305395 !important;
    border: 1px solid #305395 !important;
}
.analyseHover .analyseName{
    color: #f8f8f8 !important;
    border-bottom: 1px solid #999 !important;
}
.analyseHover .analyseDetial{
    color: #e8e8e8 !important;
}

.analyse{
    background: #fff !important;
    color: #333 !important;
}
.analyseName{
    border-bottom: 1px solid #ddd !important;
    color: #333 !important;
}
.analyseDetial{
    color: #999 !important;
}
.analyse>div>img{
    filter: brightness(0.3) !important;
}
.analyse i{
    color: #1890FF !important;
}
.analyseHover i{
    color: #fff !important;
}
.topbar,.leftMenuButton,.rightMenuButton{
    background: #fff !important;
}
.schoolName{
	color: #333 !important;
}
.toolbar li{
	color: #333 !important;
}
.toolbar .ant-menu-item-selected, .toolbar .ant-menu-horizontal>.ant-menu-item:hover{
	border-color: #1890ff !important;
	color: #1890ff !important;
	font-weight: bold !important;
}
.searchWrap input{
	background: #e8e8e8 !important;
	color: #666 !important;
}
.stateBar .name{
	color: #666 !important;
}
.searchWrap .searchTopButton {
  position: absolute;
  right: 5px;
  top: 19px;
  width: 20px;
  height: 20px;
  background: url(../img/searchBlack.png) no-repeat !important;
  background-size: 20px !important;
  z-index: 10000;
  opacity: 0.7;
}
.threeLeftBottomWrap{
	height: 274px !important;
}
.sortWrap p{
    color: #333 !important;
}
.sort{
    color: #333 !important;
}
.sort:hover{
    color: #fff !important;
}
.text1{
    font-size: 35px;
    text-align: center;
    font-weight: bold;
    margin-top: 50px;
	color: #fff;
}
.text2{
    font-size: 16px;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 80px;
	color: #fff;
}
.analyseDetial {color: rgba(255, 255, 255, 0.5) !important}
.analyse {background: rgba(0, 0, 0, 0.25) !important;}
.analyse i {color: #ffffff91 !important;}
.analyseName {color: #ffffff !important;}
.list>ul>li{
    background: rgb(153, 152, 152) !important;
}
.list>ul>li:hover{
    background: #d6d5d5 !important;
}
.list>ul>.activeDetail{
    background: #d6d5d5 !important;
}