@charset "utf-8";
.wrapAll{
    background: url(../img/bgdbj.png) !important;
    background-size: 100% 125% !important;
    /* background: linear-gradient(128.353617800267deg, rgba(39, 89, 147, 1) 9%, rgba(59, 151, 212, 1) 51%, rgba(53, 95, 153, 1) 87%); */
}
.center,.wrap{
  background: none !important;
}
.topbar {background: #3084C5 !important;}
.con_head h1 {
	font-size: 36px;
	padding: 20px;
	color:#fff;
}

.con_head p {
	font-size: 14px;
	color:  rgba(255, 255, 255, .7) !important;
	width: 80%;
	padding: 30px;
	margin: auto;
}
.footer p {color:  rgba(255, 255, 255, .7) !important;}
.text1{
    font-size: 35px;
    text-align: center;
    font-weight: bold;
    margin-top: 50px;
	color: #fff;
}
.text2{
    font-size: 16px;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 80px;
	color: #fff;
}
.tabClick { background: rgba(0,0,0,0.3) !important;}
.sort:hover, .tab div:hover {background: rgba(0,0,0,0.2) !important;}
.sort {color: #ffffffb0 !important;}
.analyseDetial {color: rgba(255, 255, 255, 0.5) !important}
.analyse {background: rgba(0, 0, 0, 0.25) !important;box-shadow: 4px 3px 8px rgba(38, 38, 38, 0.13)!important;}
.analyse i {color: #ffffff91 !important;}
.analyseName {color: #ffffff !important;}
.list>ul>li{
    background: #3084C5 !important;
}
.list>ul>li:hover{
    background: #61b3f1 !important;
}
.list>ul>.activeDetail{
    background: #61b3f1 !important;
}