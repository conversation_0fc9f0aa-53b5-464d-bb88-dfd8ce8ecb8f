var contextPath;
$(function () {

    var pathName = window.location.pathname.substring(1);
    var webName = pathName == '' ? '' : pathName.substring(0, pathName.indexOf('/'));
    contextPath = window.location.protocol + '//' + window.location.host + '/' + webName;
    if (contextPath.charAt(contextPath.length - 1) == "/") {
        contextPath = contextPath.substring(0, contextPath.length - 1);
    }

    $("#userlogin").click(function () {
        if ($("#user").val() === '') {
            alert('请输入用户名');
            $("#user").focus();
            return;
        }
        if ($("#pass").val() === '') {
            alert('请输入密码');
            $("#pass").focus();
            return;
        }
        if ($("#captcha").val() === '') {
            alert('请输入验证码');
            $("#captcha").focus();
            return;
        }
        var url = contextPath + "/login";
        var data = {
            "username": encrypt($("#user").val()),
            "password": encrypt($("#pass").val()),
            "vercode": encrypt($("#captcha").val())
        };
        var loading = document.getElementById("loading");
        loading.style.display = "block";
        $.ajax(url, {
            method: 'POST',
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            data: data,
            success: function (res) {
                loading.style.display = "none";
                if (res.code === '00000') {
                    var redirectUrl = res.info.redirectUrl;
                    var regex = "^((https|http)?://)";
                    var re = new RegExp(regex);
                    if (re.test(redirectUrl)) {
                        window.location.href = redirectUrl;
                    } else {
                        redirectUrl = contextPath + redirectUrl;
                        window.location.href = redirectUrl;
                    }
                } else {
                    alert(res.info);
                    if (res.code === '00003') {
                        window.location.reload();
                    } else {
                        changeCaptcha();
                    }
                }
            }
        });
    });

    $(document).keydown(function (event) {
        if (event.keyCode === 13) {
            $("#userlogin").click();
        }
    });
});

function changeCaptcha() {
    document.getElementById("captchaImg").src = contextPath + '/captcha?' + Math.random();
}

